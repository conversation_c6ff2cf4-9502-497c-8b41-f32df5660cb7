{"paper_id": "Transformer", "title": "Provided proper attribution is provided, Google hereby grants permission to reproduce the tables and figures in this paper solely for use in journalistic or scholarly works. Attention Is All You Need", "abstract": "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show these models to be superior in quality while being more parallelizable and requiring significantly less time to train. Our model achieves 28.4 BLEU on the WMT 2014 Englishto-German translation task, improving over the existing best results, including ensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task, our model establishes a new single-model state-of-the-art BLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction of the training costs of the best models from the literature. We show that the Transformer generalizes well to other tasks by applying it successfully to English constituency parsing both with large and limited training data. * Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started the effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and has been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head attention and the parameter-free position representation and became the other person involved in nearly every detail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and tensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and efficient inference and visualizations. <PERSON><PERSON>z and <PERSON> spent countless long days designing various parts of and implementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating our research.\n† Work performed while at Google Brain.\n‡ Work performed while at Google Research.", "pdf_parse": {"paper_id": "Transformer", "abstract": [{"text": "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show these models to be superior in quality while being more parallelizable and requiring significantly less time to train. Our model achieves 28.4 BLEU on the WMT 2014 Englishto-German translation task, improving over the existing best results, including ensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task, our model establishes a new single-model state-of-the-art BLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction of the training costs of the best models from the literature. We show that the Transformer generalizes well to other tasks by applying it successfully to English constituency parsing both with large and limited training data. * Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started the effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and has been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head attention and the parameter-free position representation and became the other person involved in nearly every detail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and tensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and efficient inference and visualizations. <PERSON><PERSON>z and <PERSON> spent countless long days designing various parts of and implementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating our research.", "cite_spans": [], "section": "Abstract", "sec_num": null}, {"text": "† Work performed while at Google Brain.", "cite_spans": [], "section": "Abstract", "sec_num": null}, {"text": "‡ Work performed while at Google Research.", "cite_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Recurrent neural networks, long short-term memory [13] and gated recurrent [7] neural networks in particular, have been firmly established as state of the art approaches in sequence modeling and transduction problems such as language modeling and machine translation [35, 2, 5] . Numerous efforts have since continued to push the boundaries of recurrent language models and encoder-decoder architectures [38, 24, 15] .", "cite_spans": [{"start": 50, "end": 54, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 75, "end": 78, "text": "[7]", "ref_id": "BIBREF6"}, {"start": 267, "end": 271, "text": "[35,", "ref_id": "BIBREF34"}, {"start": 272, "end": 274, "text": "2,", "ref_id": "BIBREF1"}, {"start": 275, "end": 277, "text": "5]", "ref_id": "BIBREF4"}, {"start": 404, "end": 408, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 409, "end": 412, "text": "24,", "ref_id": "BIBREF23"}, {"start": 413, "end": 416, "text": "15]", "ref_id": "BIBREF14"}], "section": "Introduction", "sec_num": "1"}, {"text": "Recurrent models typically factor computation along the symbol positions of the input and output sequences. Aligning the positions to steps in computation time, they generate a sequence of hidden states h t , as a function of the previous hidden state h t-1 and the input for position t. This inherently sequential nature precludes parallelization within training examples, which becomes critical at longer sequence lengths, as memory constraints limit batching across examples. Recent work has achieved significant improvements in computational efficiency through factorization tricks [21] and conditional computation [32] , while also improving model performance in case of the latter. The fundamental constraint of sequential computation, however, remains.", "cite_spans": [{"start": 586, "end": 590, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 619, "end": 623, "text": "[32]", "ref_id": "BIBREF31"}], "section": "Introduction", "sec_num": "1"}, {"text": "Attention mechanisms have become an integral part of compelling sequence modeling and transduction models in various tasks, allowing modeling of dependencies without regard to their distance in the input or output sequences [2, 19] . In all but a few cases [27] , however, such attention mechanisms are used in conjunction with a recurrent network.", "cite_spans": [{"start": 224, "end": 227, "text": "[2,", "ref_id": "BIBREF1"}, {"start": 228, "end": 231, "text": "19]", "ref_id": "BIBREF18"}, {"start": 257, "end": 261, "text": "[27]", "ref_id": "BIBREF26"}], "section": "Introduction", "sec_num": "1"}, {"text": "In this work we propose the Transformer, a model architecture eschewing recurrence and instead relying entirely on an attention mechanism to draw global dependencies between input and output. The Transformer allows for significantly more parallelization and can reach a new state of the art in translation quality after being trained for as little as twelve hours on eight P100 GPUs.", "cite_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "The goal of reducing sequential computation also forms the foundation of the Extended Neural GPU [16] , ByteNet [18] and ConvS2S [9] , all of which use convolutional neural networks as basic building block, computing hidden representations in parallel for all input and output positions. In these models, the number of operations required to relate signals from two arbitrary input or output positions grows in the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes it more difficult to learn dependencies between distant positions [12] . In the Transformer this is reduced to a constant number of operations, albeit at the cost of reduced effective resolution due to averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as described in section 3.2.", "cite_spans": [{"start": 97, "end": 101, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 112, "end": 116, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 129, "end": 132, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 574, "end": 578, "text": "[12]", "ref_id": "BIBREF11"}], "section": "Background", "sec_num": "2"}, {"text": "Self-attention, sometimes called intra-attention is an attention mechanism relating different positions of a single sequence in order to compute a representation of the sequence. Self-attention has been used successfully in a variety of tasks including reading comprehension, abstractive summarization, textual entailment and learning task-independent sentence representations [4, 27, 28, 22] .", "cite_spans": [{"start": 377, "end": 380, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 381, "end": 384, "text": "27,", "ref_id": "BIBREF26"}, {"start": 385, "end": 388, "text": "28,", "ref_id": "BIBREF27"}, {"start": 389, "end": 392, "text": "22]", "ref_id": "BIBREF21"}], "section": "Background", "sec_num": "2"}, {"text": "End-to-end memory networks are based on a recurrent attention mechanism instead of sequencealigned recurrence and have been shown to perform well on simple-language question answering and language modeling tasks [34] .", "cite_spans": [{"start": 212, "end": 216, "text": "[34]", "ref_id": "BIBREF33"}], "section": "Background", "sec_num": "2"}, {"text": "To the best of our knowledge, however, the Transformer is the first transduction model relying entirely on self-attention to compute representations of its input and output without using sequencealigned RNNs or convolution. In the following sections, we will describe the Transformer, motivate self-attention and discuss its advantages over models such as [17, 18] and [9] .", "cite_spans": [{"start": 356, "end": 360, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 361, "end": 364, "text": "18]", "ref_id": "BIBREF17"}, {"start": 369, "end": 372, "text": "[9]", "ref_id": "BIBREF8"}], "section": "Background", "sec_num": "2"}, {"text": "Most competitive neural sequence transduction models have an encoder-decoder structure [5, 2, 35] . Here, the encoder maps an input sequence of symbol representations (x 1 , ..., x n ) to a sequence of continuous representations z = (z 1 , ..., z n ). Given z, the decoder then generates an output sequence (y 1 , ..., y m ) of symbols one element at a time. At each step the model is auto-regressive [10] , consuming the previously generated symbols as additional input when generating the next. The Transformer follows this overall architecture using stacked self-attention and point-wise, fully connected layers for both the encoder and decoder, shown in the left and right halves of Figure 1 , respectively.", "cite_spans": [{"start": 87, "end": 90, "text": "[5,", "ref_id": "BIBREF4"}, {"start": 91, "end": 93, "text": "2,", "ref_id": "BIBREF1"}, {"start": 94, "end": 97, "text": "35]", "ref_id": "BIBREF34"}, {"start": 401, "end": 405, "text": "[10]", "ref_id": "BIBREF9"}], "section": "Model Architecture", "sec_num": "3"}, {"text": "Encoder: The encoder is composed of a stack of N = 6 identical layers. Each layer has two sub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, positionwise fully connected feed-forward network. We employ a residual connection [11] around each of the two sub-layers, followed by layer normalization [1] . That is, the output of each sub-layer is LayerNorm(x + Sublayer(x)), where Sublayer(x) is the function implemented by the sub-layer itself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding layers, produce outputs of dimension d model = 512.", "cite_spans": [{"start": 265, "end": 269, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 337, "end": 340, "text": "[1]", "ref_id": null}], "section": "Encoder and Decoder Stacks", "sec_num": "3.1"}, {"text": "Decoder: The decoder is also composed of a stack of N = 6 identical layers. In addition to the two sub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head attention over the output of the encoder stack. Similar to the encoder, we employ residual connections around each of the sub-layers, followed by layer normalization. We also modify the self-attention sub-layer in the decoder stack to prevent positions from attending to subsequent positions. This masking, combined with fact that the output embeddings are offset by one position, ensures that the predictions for position i can depend only on the known outputs at positions less than i.", "cite_spans": [], "section": "Encoder and Decoder Stacks", "sec_num": "3.1"}, {"text": "An attention function can be described as mapping a query and a set of key-value pairs to an output, where the query, keys, values, and output are all vectors. The output is computed as a weighted sum Scaled Dot-Product Attention Multi-Head Attention of the values, where the weight assigned to each value is computed by a compatibility function of the query with the corresponding key.", "cite_spans": [], "section": "Attention", "sec_num": "3.2"}, {"text": "We call our particular attention \"Scaled Dot-Product Attention\" (Figure 2 ). The input consists of queries and keys of dimension d k , and values of dimension d v . We compute the dot products of the query with all keys, divide each by √ d k , and apply a softmax function to obtain the weights on the values.", "cite_spans": [], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "In practice, we compute the attention function on a set of queries simultaneously, packed together into a matrix Q. The keys and values are also packed together into matrices K and V . We compute the matrix of outputs as:", "cite_spans": [], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "EQUATION", "cite_spans": [], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "The two most commonly used attention functions are additive attention [2] , and dot-product (multiplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor of 1", "cite_spans": [{"start": 70, "end": 73, "text": "[2]", "ref_id": "BIBREF1"}], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "√ d k", "cite_spans": [], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": ". Additive attention computes the compatibility function using a feed-forward network with a single hidden layer. While the two are similar in theoretical complexity, dot-product attention is much faster and more space-efficient in practice, since it can be implemented using highly optimized matrix multiplication code.", "cite_spans": [], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "While for small values of d k the two mechanisms perform similarly, additive attention outperforms dot product attention without scaling for larger values of d k [3] . We suspect that for large values of d k , the dot products grow large in magnitude, pushing the softmax function into regions where it has extremely small gradients4 . To counteract this effect, we scale the dot products by 1", "cite_spans": [{"start": 162, "end": 165, "text": "[3]", "ref_id": "BIBREF2"}], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "√ d k .", "cite_spans": [], "section": "Scaled Dot-Product Attention", "sec_num": "3.2.1"}, {"text": "Instead of performing a single attention function with d model -dimensional keys, values and queries, we found it beneficial to linearly project the queries, keys and values h times with different, learned linear projections to d k , d k and d v dimensions, respectively. On each of these projected versions of queries, keys and values we then perform the attention function in parallel, yielding d v -dimensional output values. These are concatenated and once again projected, resulting in the final values, as depicted in Figure 2 .", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "Multi-head attention allows the model to jointly attend to information from different representation subspaces at different positions. With a single attention head, averaging inhibits this.", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "MultiHead(Q, K, V ) = Concat(head 1 , ..., head h )W O", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "where", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "head i = Attention(QW Q i , KW K i , V W V i )", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "Where the projections are parameter matrices", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "W Q i ∈ R dmodel×d k , W K i ∈ R dmodel×d k , W V i ∈ R dmodel×dv and W O ∈ R hdv×dmodel .", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "In this work we employ h = 8 parallel attention layers, or heads. For each of these we use", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "d k = d v = d model /h = 64.", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "Due to the reduced dimension of each head, the total computational cost is similar to that of single-head attention with full dimensionality.", "cite_spans": [], "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "The Transformer uses multi-head attention in three different ways:", "cite_spans": [], "section": "Applications of Attention in our Model", "sec_num": "3.2.3"}, {"text": "• In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer, and the memory keys and values come from the output of the encoder. This allows every position in the decoder to attend over all positions in the input sequence. This mimics the typical encoder-decoder attention mechanisms in sequence-to-sequence models such as [38, 2, 9] .", "cite_spans": [{"start": 353, "end": 357, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 358, "end": 360, "text": "2,", "ref_id": "BIBREF1"}, {"start": 361, "end": 363, "text": "9]", "ref_id": "BIBREF8"}], "section": "Applications of Attention in our Model", "sec_num": "3.2.3"}, {"text": "• The encoder contains self-attention layers. In a self-attention layer all of the keys, values and queries come from the same place, in this case, the output of the previous layer in the encoder. Each position in the encoder can attend to all positions in the previous layer of the encoder.", "cite_spans": [], "section": "Applications of Attention in our Model", "sec_num": "3.2.3"}, {"text": "• Similarly, self-attention layers in the decoder allow each position in the decoder to attend to all positions in the decoder up to and including that position. We need to prevent leftward information flow in the decoder to preserve the auto-regressive property. We implement this inside of scaled dot-product attention by masking out (setting to -∞) all values in the input of the softmax which correspond to illegal connections. See Figure 2 .", "cite_spans": [], "section": "Applications of Attention in our Model", "sec_num": "3.2.3"}, {"text": "In addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully connected feed-forward network, which is applied to each position separately and identically. This consists of two linear transformations with a ReLU activation in between.", "cite_spans": [], "section": "Position-wise Feed-Forward Networks", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "section": "Position-wise Feed-Forward Networks", "sec_num": "3.3"}, {"text": "While the linear transformations are the same across different positions, they use different parameters from layer to layer. Another way of describing this is as two convolutions with kernel size 1.", "cite_spans": [], "section": "Position-wise Feed-Forward Networks", "sec_num": "3.3"}, {"text": "The dimensionality of input and output is d model = 512, and the inner-layer has dimensionality d f f = 2048.", "cite_spans": [], "section": "Position-wise Feed-Forward Networks", "sec_num": "3.3"}, {"text": "Similarly to other sequence transduction models, we use learned embeddings to convert the input tokens and output tokens to vectors of dimension d model . We also use the usual learned linear transformation and softmax function to convert the decoder output to predicted next-token probabilities. In our model, we share the same weight matrix between the two embedding layers and the pre-softmax linear transformation, similar to [30] . In the embedding layers, we multiply those weights by √ d model . ", "cite_spans": [{"start": 430, "end": 434, "text": "[30]", "ref_id": "BIBREF29"}], "section": "Embeddings and Softmax", "sec_num": "3.4"}, {"text": "(n 2 • d) O(1) O(1) Recurrent O(n • d 2 ) O(n) O(n) Convolutional O(k • n • d 2 ) O(1) O(log k (n)) Self-Attention (restricted) O(r • n • d) O(1) O(n/r)", "cite_spans": [], "section": "Embeddings and Softmax", "sec_num": "3.4"}, {"text": "Since our model contains no recurrence and no convolution, in order for the model to make use of the order of the sequence, we must inject some information about the relative or absolute position of the tokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the bottoms of the encoder and decoder stacks. The positional encodings have the same dimension d model as the embeddings, so that the two can be summed. There are many choices of positional encodings, learned and fixed [9] .", "cite_spans": [{"start": 517, "end": 520, "text": "[9]", "ref_id": "BIBREF8"}], "section": "Positional Encoding", "sec_num": "3.5"}, {"text": "In this work, we use sine and cosine functions of different frequencies:", "cite_spans": [], "section": "Positional Encoding", "sec_num": "3.5"}, {"text": "P E (pos,2i) = sin(pos/10000 2i/dmodel ) P E (pos,2i+1) = cos(pos/10000 2i/dmodel )", "cite_spans": [], "section": "Positional Encoding", "sec_num": "3.5"}, {"text": "where pos is the position and i is the dimension. That is, each dimension of the positional encoding corresponds to a sinusoid. The wavelengths form a geometric progression from 2π to 10000 • 2π. We chose this function because we hypothesized it would allow the model to easily learn to attend by relative positions, since for any fixed offset k, P E pos+k can be represented as a linear function of P E pos .", "cite_spans": [], "section": "Positional Encoding", "sec_num": "3.5"}, {"text": "We also experimented with using learned positional embeddings [9] instead, and found that the two versions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version because it may allow the model to extrapolate to sequence lengths longer than the ones encountered during training.", "cite_spans": [{"start": 62, "end": 65, "text": "[9]", "ref_id": "BIBREF8"}], "section": "Positional Encoding", "sec_num": "3.5"}, {"text": "In this section we compare various aspects of self-attention layers to the recurrent and convolutional layers commonly used for mapping one variable-length sequence of symbol representations (x 1 , ..., x n ) to another sequence of equal length (z 1 , ..., z n ), with x i , z i ∈ R d , such as a hidden layer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we consider three desiderata.", "cite_spans": [], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "One is the total computational complexity per layer. Another is the amount of computation that can be parallelized, as measured by the minimum number of sequential operations required.", "cite_spans": [], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "The third is the path length between long-range dependencies in the network. Learning long-range dependencies is a key challenge in many sequence transduction tasks. One key factor affecting the ability to learn such dependencies is the length of the paths forward and backward signals have to traverse in the network. The shorter these paths between any combination of positions in the input and output sequences, the easier it is to learn long-range dependencies [12] . Hence we also compare the maximum path length between any two input and output positions in networks composed of the different layer types.", "cite_spans": [{"start": 465, "end": 469, "text": "[12]", "ref_id": "BIBREF11"}], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "As noted in Table 1 , a self-attention layer connects all positions with a constant number of sequentially executed operations, whereas a recurrent layer requires O(n) sequential operations. In terms of computational complexity, self-attention layers are faster than recurrent layers when the sequence length n is smaller than the representation dimensionality d, which is most often the case with sentence representations used by state-of-the-art models in machine translations, such as word-piece [38] and byte-pair [31] representations. To improve computational performance for tasks involving very long sequences, self-attention could be restricted to considering only a neighborhood of size r in the input sequence centered around the respective output position. This would increase the maximum path length to O(n/r). We plan to investigate this approach further in future work.", "cite_spans": [{"start": 499, "end": 503, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 518, "end": 522, "text": "[31]", "ref_id": "BIBREF30"}], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "A single convolutional layer with kernel width k < n does not connect all pairs of input and output positions. Doing so requires a stack of O(n/k) convolutional layers in the case of contiguous kernels, or O(log k (n)) in the case of dilated convolutions [18] , increasing the length of the longest paths between any two positions in the network. Convolutional layers are generally more expensive than recurrent layers, by a factor of k. Separable convolutions [6] , however, decrease the complexity considerably, to O(k", "cite_spans": [{"start": 255, "end": 259, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 461, "end": 464, "text": "[6]", "ref_id": "BIBREF5"}], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "• n • d + n • d 2 ).", "cite_spans": [], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "Even with k = n, however, the complexity of a separable convolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer, the approach we take in our model.", "cite_spans": [], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "As side benefit, self-attention could yield more interpretable models. We inspect attention distributions from our models and present and discuss examples in the appendix. Not only do individual attention heads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic and semantic structure of the sentences.", "cite_spans": [], "section": "Why Self-Attention", "sec_num": "4"}, {"text": "This section describes the training regime for our models.", "cite_spans": [], "section": "Training", "sec_num": "5"}, {"text": "We trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million sentence pairs. Sentences were encoded using byte-pair encoding [3] , which has a shared sourcetarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT 2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece vocabulary [38] . Sentence pairs were batched together by approximate sequence length. Each training batch contained a set of sentence pairs containing approximately 25000 source tokens and 25000 target tokens.", "cite_spans": [{"start": 155, "end": 158, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 392, "end": 396, "text": "[38]", "ref_id": "BIBREF37"}], "section": "Training Data and Batching", "sec_num": "5.1"}, {"text": "We trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using the hyperparameters described throughout the paper, each training step took about 0.4 seconds. We trained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the bottom line of table 3 ), step time was 1.0 seconds. The big models were trained for 300,000 steps (3.5 days).", "cite_spans": [], "section": "Hardware and Schedule", "sec_num": "5.2"}, {"text": "We used the Adam optimizer [20] with β 1 = 0.9, β 2 = 0.98 and ϵ = 10 -9 . We varied the learning rate over the course of training, according to the formula:", "cite_spans": [{"start": 27, "end": 31, "text": "[20]", "ref_id": "BIBREF19"}], "section": "Optimizer", "sec_num": "5.3"}, {"text": "EQUATION", "cite_spans": [], "section": "Optimizer", "sec_num": "5.3"}, {"text": "This corresponds to increasing the learning rate linearly for the first warmup_steps training steps, and decreasing it thereafter proportionally to the inverse square root of the step number. We used warmup_steps = 4000.", "cite_spans": [], "section": "Optimizer", "sec_num": "5.3"}, {"text": "We employ three types of regularization during training: Residual Dropout We apply dropout [33] to the output of each sub-layer, before it is added to the sub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the positional encodings in both the encoder and decoder stacks. For the base model, we use a rate of P drop = 0.1.", "cite_spans": [{"start": 91, "end": 95, "text": "[33]", "ref_id": "BIBREF32"}], "section": "Regularization", "sec_num": "5.4"}, {"text": "During training, we employed label smoothing of value ϵ ls = 0.1 [36] . This hurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.", "cite_spans": [{"start": 65, "end": 69, "text": "[36]", "ref_id": "BIBREF35"}], "section": "Label Smoothing", "sec_num": null}, {"text": "6 Results", "cite_spans": [], "section": "Label Smoothing", "sec_num": null}, {"text": "On the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big) in Table 2 ) outperforms the best previously reported models (including ensembles) by more than 2.0 BLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is listed in the bottom line of Table 3 . Training took 3.5 days on 8 P100 GPUs. Even our base model surpasses all previously published models and ensembles, at a fraction of the training cost of any of the competitive models.", "cite_spans": [], "section": "Machine Translation", "sec_num": "6.1"}, {"text": "On the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0, outperforming all of the previously published single models, at less than 1/4 the training cost of the previous state-of-the-art model. The Transformer (big) model trained for English-to-French used dropout rate P drop = 0.1, instead of 0.3.", "cite_spans": [], "section": "Machine Translation", "sec_num": "6.1"}, {"text": "For the base models, we used a single model obtained by averaging the last 5 checkpoints, which were written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We used beam search with a beam size of 4 and length penalty α = 0.6 [38] . These hyperparameters were chosen after experimentation on the development set. We set the maximum output length during inference to input length + 50, but terminate early when possible [38] .", "cite_spans": [{"start": 259, "end": 263, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 452, "end": 456, "text": "[38]", "ref_id": "BIBREF37"}], "section": "Machine Translation", "sec_num": "6.1"}, {"text": "Table 2 summarizes our results and compares our translation quality and training costs to other model architectures from the literature. We estimate the number of floating point operations used to train a model by multiplying the training time, the number of GPUs used, and an estimate of the sustained single-precision floating-point capacity of each GPU5 .", "cite_spans": [], "section": "Machine Translation", "sec_num": "6.1"}, {"text": "To evaluate the importance of different components of the Transformer, we varied our base model in different ways, measuring the change in performance on English-to-German translation on the . We used beam search as described in the previous section, but no checkpoint averaging. We present these results in Table 3 .", "cite_spans": [], "section": "Model Variations", "sec_num": "6.2"}, {"text": "In Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions, keeping the amount of computation constant, as described in Section 3.2.2. While single-head attention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.", "cite_spans": [], "section": "Model Variations", "sec_num": "6.2"}, {"text": "In Table 3 rows (B), we observe that reducing the attention key size d k hurts model quality. This suggests that determining compatibility is not easy and that a more sophisticated compatibility function than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected, bigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our sinusoidal positional encoding with learned positional embeddings [9] , and observe nearly identical results to the base model.", "cite_spans": [{"start": 470, "end": 473, "text": "[9]", "ref_id": "BIBREF8"}], "section": "Model Variations", "sec_num": "6.2"}, {"text": "To evaluate if the Transformer can generalize to other tasks we performed experiments on English constituency parsing. This task presents specific challenges: the output is subject to strong structural constraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence models have not been able to attain state-of-the-art results in small-data regimes [37] .", "cite_spans": [{"start": 379, "end": 383, "text": "[37]", "ref_id": "BIBREF36"}], "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "We trained a 4-layer transformer with d model = 1024 on the Wall Street Journal (WSJ) portion of the Penn Treebank [25] , about 40K training sentences. We also trained it in a semi-supervised setting, using the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences [37] . We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens for the semi-supervised setting.", "cite_spans": [{"start": 115, "end": 119, "text": "[25]", "ref_id": "BIBREF24"}, {"start": 298, "end": 302, "text": "[37]", "ref_id": "BIBREF36"}], "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "We performed only a small number of experiments to select the dropout, both attention and residual (section 5.4), learning rates and beam size on the Section 22 development set, all other parameters remained unchanged from the English-to-German base translation model. During inference, we increased the maximum output length to input length + 300. We used a beam size of 21 and α = 0.3 for both WSJ only and the semi-supervised setting.", "cite_spans": [], "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "Our results in Table 4 show that despite the lack of task-specific tuning our model performs surprisingly well, yielding better results than all previously reported models with the exception of the Recurrent Neural Network Grammar [8] .", "cite_spans": [{"start": 231, "end": 234, "text": "[8]", "ref_id": "BIBREF7"}], "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "In contrast to RNN sequence-to-sequence models [37] , the Transformer outperforms the Berkeley-Parser [29] even when training only on the WSJ training set of 40K sentences.", "cite_spans": [{"start": 47, "end": 51, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 102, "end": 106, "text": "[29]", "ref_id": "BIBREF28"}], "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "In this work, we presented the Transformer, the first sequence transduction model based entirely on attention, replacing the recurrent layers most commonly used in encoder-decoder architectures with multi-headed self-attention.", "cite_spans": [], "section": "Conclusion", "sec_num": "7"}, {"text": "For translation tasks, the Transformer can be trained significantly faster than architectures based on recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014 English-to-French translation tasks, we achieve a new state of the art. In the former task our best model outperforms even all previously reported ensembles.", "cite_spans": [], "section": "Conclusion", "sec_num": "7"}, {"text": "We are excited about the future of attention-based models and plan to apply them to other tasks. We plan to extend the Transformer to problems involving input and output modalities other than text and to investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs such as images, audio and video. Making generation less sequential is another research goals of ours.", "cite_spans": [], "section": "Conclusion", "sec_num": "7"}, {"text": "The code we used to train and evaluate our models is available at https://github.com/ tensorflow/tensor2tensor. ", "cite_spans": [], "section": "Conclusion", "sec_num": "7"}, {"text": "The Full attentions for head 5. Bottom: Isolated attentions from just the word 'its' for attention heads 5 and 6. Note that the attentions are very sharp for this word.", "cite_spans": [], "section": "Input-Input Layer5", "sec_num": null}, {"text": "The ", "cite_spans": [], "section": "Input-Input Layer5", "sec_num": null}, {"text": "To illustrate why the dot products get large, assume that the components of q and k are independent random variables with mean 0 and variance 1. Then their dot product, q • k = d k i=1 qiki, has mean 0 and variance d k .", "cite_spans": [], "section": "", "sec_num": null}, {"text": "We used values of 2.8, 3.7,", "cite_spans": [], "section": "", "sec_num": null}, {"text": "6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.", "cite_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "Acknowledgements We are grateful to <PERSON><PERSON> and <PERSON> for their fruitful comments, corrections and inspiration.", "cite_spans": [], "section": "acknowledgement", "sec_num": null}], "ref_entries": {"FIGREF0": {"text": "Figure 1: The Transformer -model architecture.", "num": null, "type_str": "figure", "fig_num": "1", "uris": null}, "FIGREF1": {"text": "Figure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several attention layers running in parallel.", "num": null, "type_str": "figure", "fig_num": "2", "uris": null}, "FIGREF2": {"text": "Figure 3: An example of the attention mechanism following long-distance dependencies in the encoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of the verb 'making', completing the phrase 'making...more difficult'. Attentions here shown only for the word 'making'. Different colors represent different heads. Best viewed in color.", "num": null, "type_str": "figure", "fig_num": "3", "uris": null}, "FIGREF3": {"text": "Figure4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top: Full attentions for head 5. Bottom: Isolated attentions from just the word 'its' for attention heads 5 and 6. Note that the attentions are very sharp for this word.", "num": null, "type_str": "figure", "fig_num": "4", "uris": null}, "FIGREF4": {"text": "Figure5: Many of the attention heads exhibit behaviour that seems related to the structure of the sentence. We give two such examples above, from two different heads from the encoder self-attention at layer 5 of 6. The heads clearly learned to perform different tasks.", "num": null, "type_str": "figure", "fig_num": "5", "uris": null}, "TABREF0": {"text": "Maximum path lengths, per-layer complexity and minimum number of sequential operations for different layer types. n is the sequence length, d is the representation dimension, k is the kernel size of convolutions and r the size of the neighborhood in restricted self-attention.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Layer Type</td><td>Complexity per Layer Sequential Maximum Path Length</td></tr><tr><td/><td>Operations</td></tr><tr><td>Self-Attention</td><td>O</td></tr></table>"}, "TABREF1": {"text": "The Transformer achieves better BLEU scores than previous state-of-the-art models on the English-to-German and English-to-French newstest2014 tests at a fraction of the training cost.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Model</td><td colspan=\"2\">BLEU EN-DE EN-FR</td><td>Training Cost (FLOPs) EN-DE EN-FR</td></tr><tr><td>ByteNet [18]</td><td>23.75</td><td/><td/></tr><tr><td>Deep-Att + PosUnk [39]</td><td/><td>39.2</td><td>1.0 • 10 20</td></tr><tr><td>GNMT + RL [38]</td><td>24.6</td><td>39.92</td><td>2.3 • 10 19 1.4 • 10 20</td></tr><tr><td>ConvS2S [9]</td><td>25.16</td><td>40.46</td><td>9.6 • 10 18 1.5 • 10 20</td></tr><tr><td>MoE [32]</td><td>26.03</td><td>40.56</td><td>2.0 • 10 19 1.2 • 10 20</td></tr><tr><td>Deep-Att + PosUnk Ensemble [39]</td><td/><td>40.4</td><td>8.0 • 10 20</td></tr><tr><td>GNMT + RL Ensemble [38]</td><td>26.30</td><td>41.16</td><td>1.8 • 10 20 1.1 • 10 21</td></tr><tr><td>ConvS2S Ensemble [9]</td><td>26.36</td><td>41.29</td><td>7.7 • 10 19 1.2 • 10 21</td></tr><tr><td>Transformer (base model)</td><td>27.3</td><td>38.1</td><td>3.3 • 10 18</td></tr><tr><td>Transformer (big)</td><td>28.4</td><td>41.8</td><td>2.3 • 10 19</td></tr></table>"}, "TABREF2": {"text": "Variations on the Transformer architecture. Unlisted values are identical to those of the base model. All metrics are on the English-to-German translation development set, newstest2013. Listed perplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to per-word perplexities.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td/><td colspan=\"2\">N d model</td><td>d ff</td><td>h</td><td>d k</td><td>d v</td><td colspan=\"2\">P drop ϵ ls</td><td>train steps (dev) (dev) PPL BLEU params ×10 6</td></tr><tr><td colspan=\"2\">base 6</td><td>512</td><td colspan=\"2\">2048 8</td><td>64</td><td>64</td><td>0.1</td><td colspan=\"2\">0.1 100K 4.92</td><td>25.8</td><td>65</td></tr><tr><td/><td/><td/><td/><td colspan=\"3\">1 512 512</td><td/><td/><td>5.29</td><td>24.9</td></tr><tr><td>(A)</td><td/><td/><td/><td colspan=\"3\">4 128 128 16 32 32</td><td/><td/><td>5.00 4.91</td><td>25.5 25.8</td></tr><tr><td/><td/><td/><td/><td colspan=\"2\">32 16</td><td>16</td><td/><td/><td>5.01</td><td>25.4</td></tr><tr><td>(B)</td><td/><td/><td/><td/><td>16 32</td><td/><td/><td/><td>5.16 5.01</td><td>25.1 25.4</td><td>58 60</td></tr><tr><td/><td>2</td><td/><td/><td/><td/><td/><td/><td/><td>6.11</td><td>23.7</td><td>36</td></tr><tr><td/><td>4</td><td/><td/><td/><td/><td/><td/><td/><td>5.19</td><td>25.3</td><td>50</td></tr><tr><td/><td>8</td><td/><td/><td/><td/><td/><td/><td/><td>4.88</td><td>25.5</td><td>80</td></tr><tr><td>(C)</td><td/><td>256</td><td/><td/><td>32</td><td>32</td><td/><td/><td>5.75</td><td>24.5</td><td>28</td></tr><tr><td/><td/><td>1024</td><td/><td/><td colspan=\"2\">128 128</td><td/><td/><td>4.66</td><td>26.0</td><td>168</td></tr><tr><td/><td/><td/><td>1024</td><td/><td/><td/><td/><td/><td>5.12</td><td>25.4</td><td>53</td></tr><tr><td/><td/><td/><td>4096</td><td/><td/><td/><td/><td/><td>4.75</td><td>26.2</td><td>90</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td>0.0</td><td/><td>5.77</td><td>24.6</td></tr><tr><td>(D)</td><td/><td/><td/><td/><td/><td/><td>0.2</td><td>0.0</td><td>4.95 4.67</td><td>25.5 25.3</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td>0.2</td><td>5.47</td><td>25.7</td></tr><tr><td>(E)</td><td/><td colspan=\"7\">positional embedding instead of sinusoids</td><td>4.92</td><td>25.7</td></tr><tr><td>big</td><td>6</td><td colspan=\"3\">1024 4096 16</td><td/><td/><td>0.3</td><td/><td>300K 4.33</td><td>26.4</td><td>213</td></tr><tr><td colspan=\"4\">development set, newstest2013</td><td/><td/><td/><td/><td/></tr></table>"}, "TABREF3": {"text": "The Transformer generalizes well to English constituency parsing (Results are on Section 23 of WSJ)", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Parser</td><td>Training</td><td>WSJ 23 F1</td></tr><tr><td colspan=\"2\">Vinyal<PERSON> &amp; <PERSON> el al. (2014) [37] WSJ only, discriminative</td><td>88.3</td></tr><tr><td><PERSON><PERSON> et al. (2006) [29]</td><td>WSJ only, discriminative</td><td>90.4</td></tr><tr><td><PERSON> et al. (2013) [40]</td><td>WSJ only, discriminative</td><td>90.4</td></tr><tr><td><PERSON> et al. (2016) [8]</td><td>WSJ only, discriminative</td><td>91.7</td></tr><tr><td>Transformer (4 layers)</td><td>WSJ only, discriminative</td><td>91.3</td></tr><tr><td><PERSON> et al. (2013) [40]</td><td>semi-supervised</td><td>91.3</td></tr><tr><td>Huang &amp; Harper (2009) [14]</td><td>semi-supervised</td><td>91.3</td></tr><tr><td>McClosky et al. (2006) [26]</td><td>semi-supervised</td><td>92.1</td></tr><tr><td>Vinyals &amp; Kaiser el al. (2014) [37]</td><td>semi-supervised</td><td>92.1</td></tr><tr><td>Transformer (4 layers)</td><td>semi-supervised</td><td>92.7</td></tr><tr><td>Luong et al. (2015) [23]</td><td>multi-task</td><td>93.0</td></tr><tr><td>Dyer et al. (2016) [8]</td><td>generative</td><td>93.3</td></tr></table>"}}}}