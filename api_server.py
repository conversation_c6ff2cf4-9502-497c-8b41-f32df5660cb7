from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
import sys
import json
import shutil
from pathlib import Path
import logging
from typing import Optional, AsyncGenerator
import asyncio
import subprocess

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Paper2Code Streaming API", description="将PDF论文转换为代码的流式API服务")

# 添加CORS支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ProcessRequest(BaseModel):
    filename: str
    paper_name: Optional[str] = None
    gpt_version: str = "openai/o3-mini"

async def run_command_stream(command: str, stage_name: str, cwd: str = None) -> AsyncGenerator[str, None]:
    """流式执行命令并返回输出"""
    try:
        # 发送开始状态
        yield f"data: {json.dumps({'type': 'status', 'stage': stage_name, 'message': f'开始执行: {command}'})}\n\n"
        
        # 创建进程
        process = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            cwd=cwd
        )
        
        # 流式读取输出
        while True:
            line = await process.stdout.readline()
            if not line:
                break
            
            line_text = line.decode('utf-8', errors='ignore').strip()
            if line_text:
                yield f"data: {json.dumps({'type': 'output', 'stage': stage_name, 'message': line_text})}\n\n"
        
        # 等待进程完成
        await process.wait()
        
        if process.returncode == 0:
            yield f"data: {json.dumps({'type': 'status', 'stage': stage_name, 'message': f'{stage_name} 执行完成'})}\n\n"
        else:
            yield f"data: {json.dumps({'type': 'error', 'stage': stage_name, 'message': f'{stage_name} 执行失败，返回码: {process.returncode}'})}\n\n"
            raise Exception(f"命令执行失败: {command}")
            
    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'stage': stage_name, 'message': f'{stage_name} 发生错误: {str(e)}'})}\n\n"
        raise

async def process_paper_stream(request: ProcessRequest) -> AsyncGenerator[str, None]:
    """流式处理论文的主函数"""
    try:
        # 验证文件名
        if not request.filename:
            yield f"data: {json.dumps({'type': 'error', 'stage': 'validation', 'message': '文件名不能为空'})}\n\n"
            return
        
        # 构建文件路径
        filename_without_ext = request.filename.replace('.pdf', '').replace('.json', '')
        paper_name = request.paper_name or filename_without_ext
        
        yield f"data: {json.dumps({'type': 'status', 'stage': 'init', 'message': f'开始处理论文: {paper_name}'})}\n\n"
        
        # 定义路径
        data_dir = Path("data")
        
        # 查找文件
        pdf_path = None
        pdf_json_path = None
        
        # 首先在data目录查找
        data_pdf = data_dir / request.filename
        data_json = data_dir / f"{filename_without_ext}.json"
        
        if data_pdf.exists():
            pdf_path = data_pdf
        if data_json.exists():
            pdf_json_path = data_json
            
        # 如果在data目录没找到，尝试在examples目录查找并复制
        if not pdf_path or not pdf_json_path:
            examples_dir = Path("examples")
            if examples_dir.exists():
                # 查找并复制PDF文件
                if not pdf_path:
                    for pdf_file in examples_dir.rglob(request.filename):
                        data_dir.mkdir(exist_ok=True)
                        pdf_path = data_dir / pdf_file.name
                        shutil.copy2(pdf_file, pdf_path)
                        yield f"data: {json.dumps({'type': 'status', 'stage': 'file_copy', 'message': f'已从examples复制PDF文件: {pdf_file.name}'})}\n\n"
                        break
                
                # 查找并复制JSON文件
                if not pdf_json_path:
                    json_filename = f"{filename_without_ext}.json"
                    for json_file in examples_dir.rglob(json_filename):
                        data_dir.mkdir(exist_ok=True)
                        pdf_json_path = data_dir / json_file.name
                        shutil.copy2(json_file, pdf_json_path)
                        yield f"data: {json.dumps({'type': 'status', 'stage': 'file_copy', 'message': f'已从examples复制JSON文件: {json_file.name}'})}\n\n"
                        break
        
        # 验证PDF文件存在
        if not pdf_path or not pdf_path.exists():
            yield f"data: {json.dumps({'type': 'error', 'stage': 'validation', 'message': f'PDF文件不存在: {request.filename}'})}\n\n"
            return

        # 如果JSON文件不存在，尝试从PDF转换
        if not pdf_json_path or not pdf_json_path.exists():
            yield f"data: {json.dumps({'type': 'status', 'stage': 'pdf_conversion', 'message': f'JSON文件不存在，开始从PDF转换: {filename_without_ext}.json'})}\n\n"

            # 检查s2orc-doc2json是否存在
            s2orc_path = Path("s2orc-doc2json")
            if not s2orc_path.exists():
                yield f"data: {json.dumps({'type': 'error', 'stage': 'pdf_conversion', 'message': 's2orc-doc2json目录不存在，请先克隆该仓库: git clone https://github.com/allenai/s2orc-doc2json.git'})}\n\n"
                return

            # 创建必要的目录
            temp_dir = s2orc_path / "temp_dir"
            output_dir_s2orc = s2orc_path / "output_dir" / "paper_coder"
            temp_dir.mkdir(parents=True, exist_ok=True)
            output_dir_s2orc.mkdir(parents=True, exist_ok=True)

            # 执行PDF转JSON
            pdf_conversion_cmd = f"python ./s2orc-doc2json/doc2json/grobid2json/process_pdf.py -i {pdf_path} -t ./s2orc-doc2json/temp_dir/ -o ./s2orc-doc2json/output_dir/paper_coder"

            try:
                async for chunk in run_command_stream(pdf_conversion_cmd, "PDF转JSON"):
                    yield chunk

                # 查找生成的JSON文件并复制到data目录
                generated_json = None
                for json_file in output_dir_s2orc.glob("*.json"):
                    if json_file.stem == filename_without_ext or json_file.stem == pdf_path.stem:
                        generated_json = json_file
                        break

                if not generated_json:
                    # 如果没找到匹配的文件名，使用第一个JSON文件
                    json_files = list(output_dir_s2orc.glob("*.json"))
                    if json_files:
                        generated_json = json_files[0]

                if generated_json:
                    pdf_json_path = data_dir / f"{filename_without_ext}.json"
                    shutil.copy2(generated_json, pdf_json_path)
                    yield f"data: {json.dumps({'type': 'status', 'stage': 'pdf_conversion', 'message': f'PDF转JSON完成，已保存到: {pdf_json_path.name}'})}\n\n"
                else:
                    yield f"data: {json.dumps({'type': 'error', 'stage': 'pdf_conversion', 'message': 'PDF转JSON失败，未找到生成的JSON文件'})}\n\n"
                    return

            except Exception as e:
                yield f"data: {json.dumps({'type': 'error', 'stage': 'pdf_conversion', 'message': f'PDF转JSON过程中发生错误: {str(e)}'})}\n\n"
                return
        
        # 定义其他路径
        pdf_json_cleaned_path = data_dir / f"{filename_without_ext}_cleaned.json"
        output_dir = Path("outputs") / paper_name
        output_repo_dir = Path("outputs") / f"{paper_name}_repo"
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        output_repo_dir.mkdir(parents=True, exist_ok=True)
        
        yield f"data: {json.dumps({'type': 'status', 'stage': 'init', 'message': '文件验证完成，开始处理流程'})}\n\n"
        
        # 步骤1: 预处理
        cmd1 = f"python codes/0_pdf_process.py --input_json_path {pdf_json_path} --output_json_path {pdf_json_cleaned_path}"
        async for chunk in run_command_stream(cmd1, "预处理"):
            yield chunk
        
        # 步骤2: 规划
        cmd2 = f"python codes/1_planning.py --paper_name {paper_name} --gpt_version {request.gpt_version} --pdf_json_path {pdf_json_cleaned_path} --output_dir {output_dir}"
        async for chunk in run_command_stream(cmd2, "规划阶段"):
            yield chunk
        
        # 步骤3: 提取配置
        cmd3 = f"python codes/1.1_extract_config.py --paper_name {paper_name} --output_dir {output_dir}"
        async for chunk in run_command_stream(cmd3, "配置提取"):
            yield chunk
        
        # 步骤4: 复制配置文件
        yield f"data: {json.dumps({'type': 'status', 'stage': 'config_copy', 'message': '复制配置文件'})}\n\n"
        config_source = output_dir / "planning_config.yaml"
        config_dest = output_repo_dir / "config.yaml"
        if config_source.exists():
            shutil.copy2(config_source, config_dest)
            yield f"data: {json.dumps({'type': 'status', 'stage': 'config_copy', 'message': '配置文件复制完成'})}\n\n"
        else:
            yield f"data: {json.dumps({'type': 'status', 'stage': 'config_copy', 'message': '配置文件不存在，跳过复制'})}\n\n"
        
        # 步骤5: 分析
        cmd4 = f"python codes/2_analyzing.py --paper_name {paper_name} --gpt_version {request.gpt_version} --pdf_json_path {pdf_json_cleaned_path} --output_dir {output_dir}"
        async for chunk in run_command_stream(cmd4, "分析阶段"):
            yield chunk
        
        # 步骤6: 编码
        cmd5 = f"python codes/3_coding.py --paper_name {paper_name} --gpt_version {request.gpt_version} --pdf_json_path {pdf_json_cleaned_path} --output_dir {output_dir} --output_repo_dir {output_repo_dir}"
        async for chunk in run_command_stream(cmd5, "编码阶段"):
            yield chunk
        
        # 完成
        yield f"data: {json.dumps({'type': 'complete', 'stage': 'finished', 'message': f'论文 {paper_name} 处理完成', 'data': {'output_dir': str(output_dir), 'output_repo_dir': str(output_repo_dir)}})}\n\n"
        
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}"
        yield f"data: {json.dumps({'type': 'error', 'stage': 'general', 'message': error_msg})}\n\n"

@app.post("/process_paper_stream")
async def process_paper_stream_endpoint(request: ProcessRequest):
    """流式处理论文接口"""
    return StreamingResponse(
        process_paper_stream(request),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "Paper2Code Streaming API服务",
        "version": "2.0.0",
        "description": "将PDF论文转换为代码的流式API服务",
        "features": ["流式响应", "实时状态更新", "Server-Sent Events"]
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}

@app.get("/list_papers")
async def list_papers():
    """列出data目录和examples目录下的所有PDF和JSON文件"""
    try:
        result = {
            "data_pdf_files": [],
            "data_json_files": [],
            "examples_pdf_files": [],
            "examples_json_files": []
        }
        
        # 检查data目录
        data_dir = Path("data")
        if data_dir.exists():
            result["data_pdf_files"] = [f.name for f in data_dir.glob("*.pdf")]
            result["data_json_files"] = [f.name for f in data_dir.glob("*.json") if not f.name.endswith("_cleaned.json")]
        
        # 检查examples目录
        examples_dir = Path("examples")
        if examples_dir.exists():
            for pdf_file in examples_dir.rglob("*.pdf"):
                result["examples_pdf_files"].append(str(pdf_file.relative_to(examples_dir)))
            for json_file in examples_dir.rglob("*.json"):
                if not json_file.name.endswith("_cleaned.json"):
                    result["examples_json_files"].append(str(json_file.relative_to(examples_dir)))
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@app.post("/copy_example_to_data")
async def copy_example_to_data(filename: str):
    """将examples目录下的文件复制到data目录"""
    try:
        examples_dir = Path("examples")
        data_dir = Path("data")
        
        data_dir.mkdir(exist_ok=True)
        
        source_file = None
        for file_path in examples_dir.rglob(filename):
            source_file = file_path
            break
        
        if not source_file:
            raise HTTPException(status_code=404, detail=f"在examples目录中未找到文件: {filename}")
        
        dest_file = data_dir / source_file.name
        shutil.copy2(source_file, dest_file)
        
        copied_files = [source_file.name]
        
        # 如果是PDF文件，也尝试复制对应的JSON文件
        if source_file.suffix.lower() == '.pdf':
            json_filename = source_file.stem + '.json'
            json_source = source_file.parent / json_filename
            if json_source.exists():
                json_dest = data_dir / json_filename
                shutil.copy2(json_source, json_dest)
                copied_files.append(json_filename)
        
        return {
            "success": True,
            "message": f"已复制 {', '.join(copied_files)} 到data目录",
            "copied_files": copied_files
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"复制文件失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
