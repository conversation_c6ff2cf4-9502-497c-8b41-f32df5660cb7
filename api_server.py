from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import os
import subprocess
import json
import shutil
from pathlib import Path
import logging
from typing import Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Paper2Code API", description="将PDF论文转换为代码的API服务")

class ProcessRequest(BaseModel):
    filename: str
    paper_name: Optional[str] = None
    gpt_version: str = "openai/o3-mini"
    
class ProcessResponse(BaseModel):
    success: bool
    message: str
    output_dir: Optional[str] = None
    output_repo_dir: Optional[str] = None
    error: Optional[str] = None

def run_command(command: str, cwd: str = None) -> tuple[bool, str]:
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            timeout=3600  # 1小时超时
        )
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令执行超时"
    except Exception as e:
        return False, str(e)

@app.post("/process_paper", response_model=ProcessResponse)
async def process_paper(request: ProcessRequest):
    """
    处理PDF论文，执行完整的Paper2Code流程
    
    Args:
        request: 包含文件名和其他参数的请求
        
    Returns:
        ProcessResponse: 处理结果
    """
    try:
        # 验证文件名
        if not request.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 构建文件路径
        filename_without_ext = request.filename.replace('.pdf', '').replace('.json', '')
        paper_name = request.paper_name or filename_without_ext

        # 定义路径
        data_dir = Path("data")

        # 支持从data目录或examples目录查找文件
        pdf_path = None
        pdf_json_path = None

        # 首先在data目录查找
        data_pdf = data_dir / request.filename
        data_json = data_dir / f"{filename_without_ext}.json"

        if data_pdf.exists():
            pdf_path = data_pdf
        if data_json.exists():
            pdf_json_path = data_json

        # 如果在data目录没找到，尝试在examples目录查找并复制
        if not pdf_path or not pdf_json_path:
            examples_dir = Path("examples")
            if examples_dir.exists():
                # 查找PDF文件
                if not pdf_path:
                    for pdf_file in examples_dir.rglob(request.filename):
                        # 复制到data目录
                        data_dir.mkdir(exist_ok=True)
                        pdf_path = data_dir / pdf_file.name
                        shutil.copy2(pdf_file, pdf_path)
                        logger.info(f"已从examples复制PDF文件: {pdf_file} -> {pdf_path}")
                        break

                # 查找JSON文件
                if not pdf_json_path:
                    json_filename = f"{filename_without_ext}.json"
                    for json_file in examples_dir.rglob(json_filename):
                        # 复制到data目录
                        data_dir.mkdir(exist_ok=True)
                        pdf_json_path = data_dir / json_file.name
                        shutil.copy2(json_file, pdf_json_path)
                        logger.info(f"已从examples复制JSON文件: {json_file} -> {pdf_json_path}")
                        break

        # 最终检查文件是否存在
        if not pdf_path or not pdf_path.exists():
            raise HTTPException(status_code=404, detail=f"PDF文件不存在: {request.filename}")

        if not pdf_json_path or not pdf_json_path.exists():
            raise HTTPException(status_code=404, detail=f"JSON文件不存在: {filename_without_ext}.json，请先使用s2orc-doc2json转换PDF")

        # 定义其他路径
        pdf_json_cleaned_path = data_dir / f"{filename_without_ext}_cleaned.json"
        output_dir = Path("outputs") / paper_name
        output_repo_dir = Path("outputs") / f"{paper_name}_repo"
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        output_repo_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"开始处理论文: {paper_name}")
        
        # 步骤1: 预处理 - 清理JSON数据
        logger.info("步骤1: 预处理 - 清理JSON数据")
        success, output = run_command(
            f"python codes/0_pdf_process.py --input_json_path {pdf_json_path} --output_json_path {pdf_json_cleaned_path}"
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"预处理失败: {output}")
        
        # 步骤2: 规划阶段
        logger.info("步骤2: 规划阶段")
        success, output = run_command(
            f"python codes/1_planning.py --paper_name {paper_name} --gpt_version {request.gpt_version} --pdf_json_path {pdf_json_cleaned_path} --output_dir {output_dir}"
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"规划阶段失败: {output}")
        
        # 步骤3: 提取配置
        logger.info("步骤3: 提取配置")
        success, output = run_command(
            f"python codes/1.1_extract_config.py --paper_name {paper_name} --output_dir {output_dir}"
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"配置提取失败: {output}")
        
        # 步骤4: 复制配置文件
        logger.info("步骤4: 复制配置文件")
        config_source = output_dir / "planning_config.yaml"
        config_dest = output_repo_dir / "config.yaml"
        if config_source.exists():
            shutil.copy2(config_source, config_dest)
        else:
            logger.warning("配置文件不存在，跳过复制步骤")
        
        # 步骤5: 分析阶段
        logger.info("步骤5: 分析阶段")
        success, output = run_command(
            f"python codes/2_analyzing.py --paper_name {paper_name} --gpt_version {request.gpt_version} --pdf_json_path {pdf_json_cleaned_path} --output_dir {output_dir}"
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"分析阶段失败: {output}")
        
        # 步骤6: 编码阶段
        logger.info("步骤6: 编码阶段")
        success, output = run_command(
            f"python codes/3_coding.py --paper_name {paper_name} --gpt_version {request.gpt_version} --pdf_json_path {pdf_json_cleaned_path} --output_dir {output_dir} --output_repo_dir {output_repo_dir}"
        )
        if not success:
            raise HTTPException(status_code=500, detail=f"编码阶段失败: {output}")
        
        logger.info(f"论文处理完成: {paper_name}")
        
        return ProcessResponse(
            success=True,
            message=f"论文 {paper_name} 处理完成",
            output_dir=str(output_dir),
            output_repo_dir=str(output_repo_dir)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理论文时发生错误: {str(e)}")
        return ProcessResponse(
            success=False,
            message="处理失败",
            error=str(e)
        )

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "Paper2Code API服务",
        "version": "1.0.0",
        "description": "将PDF论文转换为代码的API服务"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}

@app.get("/list_papers")
async def list_papers():
    """列出data目录和examples目录下的所有PDF和JSON文件"""
    try:
        result = {
            "data_pdf_files": [],
            "data_json_files": [],
            "examples_pdf_files": [],
            "examples_json_files": []
        }

        # 检查data目录
        data_dir = Path("data")
        if data_dir.exists():
            result["data_pdf_files"] = [f.name for f in data_dir.glob("*.pdf")]
            result["data_json_files"] = [f.name for f in data_dir.glob("*.json") if not f.name.endswith("_cleaned.json")]

        # 检查examples目录
        examples_dir = Path("examples")
        if examples_dir.exists():
            # 递归查找examples目录下的PDF和JSON文件
            for pdf_file in examples_dir.rglob("*.pdf"):
                result["examples_pdf_files"].append(str(pdf_file.relative_to(examples_dir)))
            for json_file in examples_dir.rglob("*.json"):
                if not json_file.name.endswith("_cleaned.json"):
                    result["examples_json_files"].append(str(json_file.relative_to(examples_dir)))

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@app.post("/copy_example_to_data")
async def copy_example_to_data(filename: str):
    """将examples目录下的文件复制到data目录"""
    try:
        examples_dir = Path("examples")
        data_dir = Path("data")

        # 创建data目录
        data_dir.mkdir(exist_ok=True)

        # 查找源文件
        source_file = None
        for file_path in examples_dir.rglob(filename):
            source_file = file_path
            break

        if not source_file:
            raise HTTPException(status_code=404, detail=f"在examples目录中未找到文件: {filename}")

        # 复制文件到data目录
        dest_file = data_dir / source_file.name
        shutil.copy2(source_file, dest_file)

        # 如果是PDF文件，也尝试复制对应的JSON文件
        if source_file.suffix.lower() == '.pdf':
            json_filename = source_file.stem + '.json'
            json_source = source_file.parent / json_filename
            if json_source.exists():
                json_dest = data_dir / json_filename
                shutil.copy2(json_source, json_dest)
                return {
                    "success": True,
                    "message": f"已复制 {source_file.name} 和 {json_filename} 到data目录",
                    "copied_files": [source_file.name, json_filename]
                }

        return {
            "success": True,
            "message": f"已复制 {source_file.name} 到data目录",
            "copied_files": [source_file.name]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"复制文件失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
