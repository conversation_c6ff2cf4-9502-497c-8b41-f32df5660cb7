## dataset_loader.py

from typing import List, Any
import yaml

class DatasetLoader:
    def __init__(self, config: dict):
        self.config = config
        self.data_path = config['dataset']['path']
        self.data_format = config['dataset']['format']

    def load_data(self) -> List[Any]:
        if self.data_format == "list_of_integers":
            # Read the data as a list of integers
            with open(self.data_path, 'r') as file:
                data = [int(line.strip()) for line in file]
        elif self.data_format == "list_of_strings":
            # Read the data as a list of strings
            with open(self.data_path, 'r') as file:
                data = [line.strip() for line in file]
        else:
            raise ValueError(f"Unsupported data format: {self.data_format}")
        
        return data

    def preprocess_data(self, data: List[Any]) -> List[Any]:
        # For this implementation, we assume the data is already in the correct format
        # Additional preprocessing can be added here if needed
        return data

    def get_preprocessed_data(self) -> List[Any]:
        raw_data = self.load_data()
        preprocessed_data = self.preprocess_data(raw_data)
        return preprocessed_data

# Example usage (for testing purposes)
if __name__ == "__main__":
    # Load configuration from config.yaml
    with open("config.yaml", 'r') as config_file:
        config = yaml.safe_load(config_file)

    # Initialize DatasetLoader and get preprocessed data
    loader = DatasetLoader(config)
    data = loader.get_preprocessed_data()
    print(data)
