## main.py

import yaml
from typing import Dict, List, Any
from dataset_loader import Dataset<PERSON>oader
from quicksort import <PERSON>Sort
from evaluation import Evaluation
import time

class Main:
    def __init__(self, config: dict):
        self.config = config
        self.dataset_loader = None
        self.quicksort = None
        self.evaluation = None
        self.original_data = None
        self.sorted_data = None

    def run_experiment(self) -> None:
        # Step 3: Initialize DatasetLoader
        self.dataset_loader = DatasetLoader(self.config)

        # Step 4: Load the Data
        self.original_data = self.dataset_loader.get_preprocessed_data()

        # Step 5: Initialize QuickSort
        self.quicksort = QuickSort(self.original_data, self.config['quicksort'])

        # Step 6: Perform the Sort
        start_time = time.time()
        self.quicksort.sort()
        end_time = time.time()

        # Step 7: Initialize Evaluation
        self.evaluation = Evaluation(self.quicksort.data, self.original_data, self.quicksort, start_time, end_time)

        # Step 8: Evaluate the Sorted Data
        metrics = self.evaluation.evaluate()

        # Step 9: Report the Results
        self.report_results(metrics)

    def report_results(self, metrics: Dict[str, float]) -> None:
        print("Evaluation Results:")
        for metric, value in metrics.items():
            print(f"{metric}: {value}")

if __name__ == "__main__":
    # Step 1: Load Configuration
    with open('config.yaml', 'r') as config_file:
        config = yaml.safe_load(config_file)

    # Step 2: Initialize Main Class
    main = Main(config)

    # Step 10: Run the Experiment
    main.run_experiment()
