## evaluation.py

from typing import List, Any, Dict
import time
import yaml

class Evaluation:
    def __init__(self, sorted_data: List[Any], original_data: List[Any], quicksort_instance: 'QuickSort', start_time: float, end_time: float):
        self.sorted_data = sorted_data
        self.original_data = original_data
        self.quicksort_instance = quicksort_instance
        self.start_time = start_time
        self.end_time = end_time
        self.metrics = {
            'number_of_comparisons': 0,
            'number_of_exchanges': 0,
            'total_time_taken': 0.0
        }

    def evaluate(self) -> Dict[str, float]:
        # Verify correctness
        expected_sorted_data = sorted(self.original_data)
        if self.sorted_data != expected_sorted_data:
            raise ValueError("The sorted data does not match the expected sorted order.")

        # Calculate metrics
        self.metrics['number_of_comparisons'] = self.quicksort_instance.comparisons
        self.metrics['number_of_exchanges'] = self.quicksort_instance.exchanges
        self.metrics['total_time_taken'] = self.end_time - self.start_time

        # Generate report
        return self.metrics

# Example usage (for testing purposes)
if __name__ == "__main__":
    # Load configuration from config.yaml
    with open("config.yaml", 'r') as config_file:
        config = yaml.safe_load(config_file)

    # Example dataset
    original_data = [5, 3, 8, 4, 2, 7, 1, 10, 9, 6]

    # Initialize QuickSort and sort the data
    quicksort = QuickSort(original_data, config)
    start_time = time.time()
    quicksort.sort()
    end_time = time.time()

    # Initialize Evaluation and evaluate the sorted data
    evaluation = Evaluation(quicksort.data, original_data, quicksort, start_time, end_time)
    metrics = evaluation.evaluate()
    print(f"Performance Metrics: {metrics}")
