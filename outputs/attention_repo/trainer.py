"""trainer.py

This module defines the Trainer class which handles training of the TransformerModel.
It initializes the optimizer with a custom learning rate scheduler, manages the training loop,
performs checkpoint saving and loading, and logs progress. It uses configuration parameters
from config.yaml. The Trainer class expects the training data to be provided as a tuple of batches,
where each batch is a tuple of (src_tensor, tgt_tensor) for translation tasks.
"""

import os
import time
import math
import logging
from typing import Tuple, Any, List, Dict

import torch
import torch.nn as nn
import torch.optim as optim

from model import TransformerModel  # Ensure that model.py is available in the same project structure

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Constants for checkpointing
DEFAULT_CHECKPOINT_DIR: str = "checkpoints"
CHECKPOINT_INTERVAL: int = 1000  # Save checkpoint every 1000 steps by default
PRINT_INTERVAL: int = 100  # Print training status every 100 steps

# Special token index for padding (assumed to be 0 as set in dataset_loader.py)
PAD_INDEX: int = 0


class LabelSmoothingLoss(nn.Module):
    """
    Implements label smoothing as described in the paper.
    This loss computes the cross-entropy loss with smoothing over one-hot labels.
    """
    def __init__(self, smoothing: float, vocab_size: int, ignore_index: int = PAD_INDEX) -> None:
        """
        Args:
            smoothing (float): Label smoothing value (e.g., 0.1).
            vocab_size (int): Vocabulary size.
            ignore_index (int): Index to ignore in the loss (e.g., PAD_INDEX).
        """
        super(LabelSmoothingLoss, self).__init__()
        assert 0.0 <= smoothing < 1.0, "Smoothing value must be in [0, 1)"
        self.smoothing: float = smoothing
        self.vocab_size: int = vocab_size
        self.ignore_index: int = ignore_index
        self.confidence: float = 1.0 - smoothing
        self.criterion = nn.KLDivLoss(reduction='sum')

    def forward(self, output: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            output (torch.Tensor): Log probabilities from the model of shape [N, vocab_size].
            target (torch.Tensor): Ground truth token indices of shape [N].
        
        Returns:
            torch.Tensor: The computed loss.
        """
        # Create a smoothed target distribution
        true_dist = torch.empty_like(output)
        true_dist.fill_(self.smoothing / (self.vocab_size - 2))
        # fill all indices with smoothing value except PAD index and the target index.
        # Set confidence for target tokens.
        target = target.unsqueeze(1)
        true_dist.scatter_(1, target, self.confidence)
        # Ensure that the distribution for PAD tokens should be all zeros.
        true_dist[:, self.ignore_index] = 0.0
        mask = (target.squeeze(1) == self.ignore_index)
        if mask.any():
            true_dist[mask] = 0.0

        loss = self.criterion(output, true_dist)
        return loss


class Trainer:
    """
    Trainer class handles training of the TransformerModel.
    
    Public Methods:
        - __init__(model: TransformerModel, data: tuple, config: dict)
        - train() -> None
        - save_checkpoint() -> None
        - load_checkpoint(path: str) -> None
    """
    def __init__(self, model: TransformerModel, data: Tuple[List[Any], Any, Any], config: Dict[str, Any]) -> None:
        """
        Initializes the Trainer with model, dataset, and configuration.
        
        Args:
            model (TransformerModel): The Transformer model to be trained.
            data (tuple): Tuple containing training batches and optionally validation/test batches.
                          Expected format: (train_batches, dev_batches, test_batches).
            config (dict): Configuration dictionary loaded from config.yaml.
        """
        self.config: Dict[str, Any] = config
        self.model: TransformerModel = model
        self.train_batches: List[Any] = data[0]  # Only training batches required for training loop
        
        # Training hyperparameters from config
        training_config: Dict[str, Any] = self.config.get("training", {})
        model_config: Dict[str, Any] = self.config.get("model", {})

        self.total_steps: int = int(training_config.get("total_steps", 100000))
        self.warmup_steps: int = int(training_config.get("warmup_steps", 4000))
        self.dropout: float = float(training_config.get("dropout", 0.1))
        self.label_smoothing: float = float(training_config.get("label_smoothing", 0.1))

        # Model parameters
        self.d_model: int = int(model_config.get("d_model", 512))

        # Optimizer configuration
        beta1: float = float(training_config.get("beta1", 0.9))
        beta2: float = float(training_config.get("beta2", 0.98))
        epsilon: float = float(training_config.get("epsilon", 1e-9))

        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=0,  # initial lr will be set by scheduler
            betas=(beta1, beta2),
            eps=epsilon
        )
        
        # Set device using available GPUs, else CPU
        self.device: torch.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)

        # Initialize global step counter
        self.global_step: int = 0

        # Create checkpoint directory if not exists
        self.checkpoint_dir: str = self.config.get("checkpoint_dir", DEFAULT_CHECKPOINT_DIR)
        os.makedirs(self.checkpoint_dir, exist_ok=True)

        # Assume vocabulary size is set via model's output projection weight size.
        self.vocab_size: int = self.model.out_projection.weight.size(0)
        self.criterion = LabelSmoothingLoss(self.label_smoothing, self.vocab_size, ignore_index=PAD_INDEX)

        logging.info("Trainer initialized on device: %s", self.device)

    def _compute_learning_rate(self, step: int) -> float:
        """
        Computes the learning rate for the given step using the schedule:
            lr = d_model^(-0.5) * min(step^(-0.5), step * warmup_steps^(-1.5))
        
        Args:
            step (int): Current training step.
        
        Returns:
            float: Computed learning rate.
        """
        if step == 0:
            step = 1
        lr = (self.d_model ** -0.5) * min(step ** -0.5, step * (self.warmup_steps ** -1.5))
        return lr

    def _update_learning_rate(self) -> None:
        """
        Updates the learning rate for each parameter group in the optimizer based on the global step.
        """
        lr = self._compute_learning_rate(self.global_step)
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr

    def train(self) -> None:
        """
        Runs the training loop for the specified total number of steps.
        """
        self.model.train()
        total_batches: int = len(self.train_batches)
        start_time: float = time.time()

        # Epoch loop: Since our data is provided as a list of batches, iterate repeatedly.
        batch_index: int = 0

        while self.global_step < self.total_steps:
            # Loop over available batches repeatedly.
            src_batch, tgt_batch = self.train_batches[batch_index]

            src_batch = src_batch.to(self.device)
            tgt_batch = tgt_batch.to(self.device)

            # For translation training, use teacher forcing:
            # Input to decoder is tgt_batch excluding the last token; target labels are tgt_batch excluding the first token.
            if tgt_batch.size(1) < 2:
                # Skip batches too short to create decoder input and target
                batch_index = (batch_index + 1) % total_batches
                continue

            decoder_input = tgt_batch[:, :-1]
            target_labels = tgt_batch[:, 1:]

            # Generate target mask for decoder (subsequent mask)
            tgt_seq_len: int = decoder_input.size(1)
            tgt_mask = TransformerModel.generate_square_subsequent_mask(tgt_seq_len).to(self.device)

            # Forward pass through the model: logits shape [batch_size, tgt_seq_len, vocab_size]
            logits = self.model(src_batch, decoder_input, src_mask=None, tgt_mask=tgt_mask)

            # Reshape logits and target labels for loss computation
            logits_flat = logits.contiguous().view(-1, self.vocab_size)
            target_flat = target_labels.contiguous().view(-1)

            # Compute loss (with label smoothing)
            loss = self.criterion(nn.functional.log_softmax(logits_flat, dim=-1), target_flat)
            # Normalize loss by number of non-pad tokens
            non_pad_mask = target_flat.ne(PAD_INDEX)
            num_tokens = non_pad_mask.sum().item()
            if num_tokens == 0:
                num_tokens = 1
            loss = loss / num_tokens

            # Backward pass and optimizer step
            self.optimizer.zero_grad()
            loss.backward()
            self._update_learning_rate()
            self.optimizer.step()

            self.global_step += 1

            # Logging training info every PRINT_INTERVAL steps
            if self.global_step % PRINT_INTERVAL == 0:
                current_lr = self._compute_learning_rate(self.global_step)
                elapsed = time.time() - start_time
                perplexity = math.exp(loss.item()) if loss.item() < 300 else float("inf")
                logging.info("Step: %d, Loss: %.4f, Perplexity: %.4f, LR: %.6f, Elapsed: %.2f sec",
                             self.global_step, loss.item(), perplexity, current_lr, elapsed)
                start_time = time.time()

            # Save checkpoint periodically
            if self.global_step % CHECKPOINT_INTERVAL == 0:
                self.save_checkpoint()

            # Move to next batch, loop around dataset if needed
            batch_index = (batch_index + 1) % total_batches

        logging.info("Training completed at global step: %d", self.global_step)

    def save_checkpoint(self) -> None:
        """
        Saves the current model and optimizer states along with current global step
        to a checkpoint file.
        """
        checkpoint = {
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }
        checkpoint_path = os.path.join(self.checkpoint_dir, f"checkpoint_step_{self.global_step}.pt")
        torch.save(checkpoint, checkpoint_path)
        logging.info("Saved checkpoint: %s", checkpoint_path)

    def load_checkpoint(self, path: str) -> None:
        """
        Loads model and optimizer states from the specified checkpoint file.
        
        Args:
            path (str): Path to the checkpoint file.
        """
        if not os.path.exists(path):
            logging.error("Checkpoint file not found: %s", path)
            raise FileNotFoundError(f"Checkpoint file not found: {path}")
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.global_step = checkpoint.get('global_step', 0)
        logging.info("Loaded checkpoint from %s at global step: %d", path, self.global_step)


# For testing purposes: run training with dummy data if this file is executed directly.
if __name__ == "__main__":
    # This section is for testing trainer.py independently.
    # Create a dummy configuration dictionary with default values that reflect config.yaml
    dummy_config = {
        "training": {
            "optimizer": "Adam",
            "beta1": 0.9,
            "beta2": 0.98,
            "epsilon": 1e-9,
            "warmup_steps": 4000,
            "total_steps": 1000,  # Use lower total steps for testing
            "batch_size_tokens": 25000,
            "dropout": 0.1,
            "label_smoothing": 0.1
        },
        "model": {
            "d_model": 512,
            "d_ff": 2048,
            "num_layers": 6,
            "num_heads": 8,
            "d_k": 64,
            "d_v": 64,
            "positional_encoding": "sinusoidal"
        },
        "checkpoint_dir": "checkpoints",
        "hardware": {
            "num_gpus": 8,
            "gpu_type": "NVIDIA P100"
        }
    }

    # Create a dummy TransformerModel instance using the dummy_config.
    model_instance = TransformerModel(dummy_config)
    # Assume dummy training batches with random tensors (simulate token indices).
    # For testing we use small sizes; in practice, these will be loaded from DatasetLoader.
    dummy_train_batches = []
    for _ in range(10):
        # Create random source and target batches (batch_size = 4, sequence lengths = 10 and 12 respectively)
        src_tensor = torch.randint(2, 50000, (4, 10))  # tokens from 2 to 50000 (since 0=PAD, 1=UNK)
        tgt_tensor = torch.randint(2, 50000, (4, 12))
        dummy_train_batches.append((src_tensor, tgt_tensor))
    
    # Instantiate Trainer with dummy data
    trainer = Trainer(model_instance, (dummy_train_batches, None, None), dummy_config)
    # Run training for testing purposes (will run for dummy total_steps)
    trainer.train()
