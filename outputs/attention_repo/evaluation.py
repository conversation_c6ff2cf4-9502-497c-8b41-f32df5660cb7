"""evaluation.py

This module implements the Evaluation class which provides methods to run inference
on the trained TransformerModel and compute task‐specific metrics for machine translation and
constituency parsing. It uses beam search decoding with parameters specified in the configuration (config.yaml)
and computes corpus-level BLEU for translation as well as average F1 score based on constituent extraction for parsing.

Usage:
    from evaluation import Evaluation
    evaluation = Evaluation(model, (translation_eval_batches, parsing_eval_batches), config)
    translation_metrics = evaluation.evaluate_translation()
    parsing_metrics = evaluation.evaluate_parsing()
"""

import math
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple, Dict, Any

# Set up logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Define special token indices (set defaults)
BOS_TOKEN: int = 2  # Beginning-of-sentence token index
EOS_TOKEN: int = 3  # End-of-sentence token index
PAD_TOKEN: int = 0  # Padding token index
UNK_TOKEN: int = 1  # Unknown token index

class Evaluation:
    """
    Evaluation class performs inference on the TransformerModel and computes metrics for
    both machine translation (BLEU score) and constituency parsing (F1 score).

    Public Methods:
        __init__(model: TransformerModel, data: tuple, config: dict) -> None
        evaluate_translation() -> dict
        evaluate_parsing() -> dict
    """
    def __init__(self, model: nn.Module, data: Tuple[List[Any], List[Any]], config: Dict[str, Any]) -> None:
        """
        Initializes the Evaluation instance.

        Args:
            model (nn.Module): The trained TransformerModel.
            data (Tuple[List[Any], List[Any]]): A tuple containing evaluation data for translation
                and parsing tasks respectively. Each evaluation dataset is assumed to be a list of batches.
                For translation, each batch is a tuple (src_tensor, tgt_tensor). For parsing, each batch is a tuple
                (src_tensor, parse_list) where parse_list contains the ground truth bracketed parse strings.
            config (Dict[str, Any]): Configuration dictionary loaded from config.yaml.
        """
        self.model: nn.Module = model
        self.model.eval()  # Set model to evaluation mode
        # Unpack evaluation data
        self.translation_data: List[Any] = data[0] if data and data[0] is not None else []
        self.parsing_data: List[Any] = data[1] if data and len(data) > 1 and data[1] is not None else []
        self.config: Dict[str, Any] = config
        
        # Retrieve evaluation parameters from configuration
        evaluation_config: Dict[str, Any] = self.config.get("evaluation", {})
        self.trans_beam_size: int = int(evaluation_config.get("beam_size", 4))
        self.trans_length_penalty: float = float(evaluation_config.get("length_penalty", 0.6))
        self.pars_beam_size: int = int(evaluation_config.get("beam_size_parsing", 21))  # Use 21 for parsing if provided
        if "beam_size_parsing" not in evaluation_config:
            self.pars_beam_size = 21
        self.pars_length_penalty: float = float(evaluation_config.get("length_penalty_parsing", 0.3))
        if "length_penalty_parsing" not in evaluation_config:
            self.pars_length_penalty = 0.3

        # For translation, maximum output length is computed as: input_length + 50
        # For parsing, maximum output length is computed as: input_length + 300

        # Set a default reverse vocabulary mapping for detokenization.
        self.index2token: Dict[int, str] = {
            PAD_TOKEN: "<PAD>",
            UNK_TOKEN: "<UNK>",
            BOS_TOKEN: "<BOS>",
            EOS_TOKEN: "<EOS>"
        }
        # For all other indices, simply convert to string.
        # In a real setting, this mapping should come from the DatasetLoader.
        # Here, we set default behavior.
    
    def evaluate_translation(self) -> Dict[str, Any]:
        """
        Evaluates the translation performance of the Transformer model.
        It decodes source sentences using beam search and computes corpus-level BLEU score.

        Returns:
            Dict[str, Any]: A dictionary containing the computed BLEU score and the number of samples evaluated.
                            Example: {'BLEU': computed_bleu, 'num_samples': count}
        """
        logging.info("Starting translation evaluation ...")
        predictions: List[str] = []
        references: List[str] = []
        num_samples: int = 0

        # Disable gradient computation during evaluation.
        with torch.no_grad():
            for batch in self.translation_data:
                # Each batch is (src_tensor, tgt_tensor)
                src_batch, tgt_batch = batch
                batch_size: int = src_batch.size(0)
                # Process each example individually.
                for i in range(batch_size):
                    src_seq: torch.Tensor = src_batch[i]  # shape: [seq_len]
                    # Remove padding tokens from source
                    src_seq = src_seq[ src_seq != PAD_TOKEN ]
                    src_length: int = src_seq.size(0)
                    max_length: int = int(src_length + 50)
                    # Run beam search to generate output sequence.
                    pred_indices: List[int] = self._beam_search(
                        src_seq, beam_size=self.trans_beam_size,
                        max_length=max_length, length_penalty=self.trans_length_penalty
                    )
                    pred_sentence: str = self._detokenize(pred_indices)
                    # The reference target: remove PAD tokens and EOS/BOS if present.
                    tgt_seq: torch.Tensor = tgt_batch[i]
                    tgt_seq = tgt_seq[ tgt_seq != PAD_TOKEN ]
                    ref_sentence: str = self._detokenize(tgt_seq.tolist())
                    
                    predictions.append(pred_sentence)
                    references.append(ref_sentence)
                    num_samples += 1

        bleu_score: float = self._compute_bleu(predictions, references)
        logging.info("Translation Evaluation: Processed %d samples, BLEU: %.2f", num_samples, bleu_score)
        return {'BLEU': bleu_score, 'num_samples': num_samples}

    def evaluate_parsing(self) -> Dict[str, Any]:
        """
        Evaluates the constituency parsing performance of the Transformer model.
        It decodes source sentences with beam search using parameters suitable for parsing,
        extracts constituent spans from predicted and reference parse trees, and computes the average F1 score.

        Returns:
            Dict[str, Any]: A dictionary containing the computed F1 score and the number of samples evaluated.
                            Example: {'F1': computed_f1, 'num_samples': count}
        """
        logging.info("Starting parsing evaluation ...")
        f1_scores: List[float] = []
        num_samples: int = 0

        with torch.no_grad():
            for batch in self.parsing_data:
                # Each batch is (src_tensor, parse_list)
                src_batch, parse_list = batch
                batch_size: int = src_batch.size(0)
                for i in range(batch_size):
                    src_seq: torch.Tensor = src_batch[i]
                    src_seq = src_seq[src_seq != PAD_TOKEN]
                    src_length: int = src_seq.size(0)
                    max_length: int = int(src_length + 300)
                    
                    pred_indices: List[int] = self._beam_search(
                        src_seq, beam_size=self.pars_beam_size,
                        max_length=max_length, length_penalty=self.pars_length_penalty
                    )
                    pred_tree: str = self._detokenize(pred_indices)
                    gold_tree: str = parse_list[i]
                    
                    pred_constituents = self._extract_constituents(pred_tree)
                    gold_constituents = self._extract_constituents(gold_tree)
                    
                    f1: float = self._compute_f1(pred_constituents, gold_constituents)
                    f1_scores.append(f1)
                    num_samples += 1

        average_f1: float = sum(f1_scores) / num_samples if num_samples > 0 else 0.0
        logging.info("Parsing Evaluation: Processed %d samples, Average F1: %.2f", num_samples, average_f1)
        return {'F1': average_f1, 'num_samples': num_samples}

    def _beam_search(self, src_seq: torch.Tensor, beam_size: int, max_length: int, length_penalty: float) -> List[int]:
        """
        Performs beam search decoding for a single source sequence.

        Args:
            src_seq (torch.Tensor): Source sequence tensor of shape [seq_len] (token indices).
            beam_size (int): Beam size.
            max_length (int): Maximum length of generated sequence.
            length_penalty (float): Length penalty factor to normalize scores.

        Returns:
            List[int]: The best output token sequence (list of token indices).
        """
        device = src_seq.device
        # Encode the source sequence; add batch dim.
        encoder_output = self.model.encode(src_seq.unsqueeze(0))  # shape: [1, src_len, d_model]

        # Each beam is a tuple: (token_sequence, accumulated_log_prob)
        beams: List[Tuple[List[int], float]] = [([BOS_TOKEN], 0.0)]
        completed_beams: List[Tuple[List[int], float]] = []

        for _ in range(max_length):
            new_beams: List[Tuple[List[int], float]] = []
            for seq, score in beams:
                # If EOS has been generated, add beam to completed list and do not expand further.
                if seq[-1] == EOS_TOKEN:
                    completed_beams.append((seq, score))
                    continue

                tgt_seq = torch.tensor(seq, dtype=torch.long, device=device).unsqueeze(0)  # [1, cur_len]
                # Generate target mask
                tgt_mask = self.model.generate_square_subsequent_mask(tgt_seq.size(1)).to(device)
                # Decode using the encoder output
                decoder_output = self.model.decode(tgt_seq, encoder_output, tgt_mask=tgt_mask, src_mask=None)
                # Get logits for last time step; shape: [1, vocab_size]
                logits = self.model.out_projection(decoder_output[:, -1, :])
                log_probs = F.log_softmax(logits, dim=-1).squeeze(0)  # shape: [vocab_size]

                # Expand each candidate with all possible tokens
                top_log_probs, top_indices = torch.topk(log_probs, beam_size)
                for log_prob, token_idx in zip(top_log_probs.tolist(), top_indices.tolist()):
                    new_seq = seq + [token_idx]
                    new_score = score + log_prob
                    new_beams.append((new_seq, new_score))
            # Sort the new beams based on (normalized) score and keep top beam_size beams.
            # Normalization: score / (len(seq)^length_penalty)
            new_beams.sort(key=lambda tup: tup[1] / (len(tup[0]) ** length_penalty), reverse=True)
            beams = new_beams[:beam_size]
            
            # If all beams have generated EOS, terminate early.
            if all(seq[-1] == EOS_TOKEN for seq, _ in beams):
                completed_beams.extend(beams)
                break

        if not completed_beams:
            completed_beams = beams

        # Choose the beam with highest normalized score.
        best_beam = max(completed_beams, key=lambda tup: tup[1] / (len(tup[0]) ** length_penalty))
        # Return the token sequence excluding the BOS token.
        return best_beam[0][1:]  # Remove BOS token

    def _detokenize(self, token_indices: List[int]) -> str:
        """
        Converts a list of token indices into a string by mapping indices to tokens.
        If a token is not found in the reverse vocabulary mapping, it is converted by str().

        Args:
            token_indices (List[int]): List of token indices.

        Returns:
            str: Detokenized sentence string.
        """
        tokens: List[str] = []
        for idx in token_indices:
            if idx in (BOS_TOKEN, EOS_TOKEN, PAD_TOKEN):
                continue
            token: str = self.index2token.get(idx, str(idx))
            tokens.append(token)
        return " ".join(tokens)

    def _compute_bleu(self, predictions: List[str], references: List[str]) -> float:
        """
        Computes corpus-level BLEU score (up to 4-grams) for the given predicted and reference sentences.

        Args:
            predictions (List[str]): List of predicted sentences.
            references (List[str]): List of reference sentences.

        Returns:
            float: BLEU score (between 0 and 100).
        """
        # Tokenize sentences: split by whitespace.
        pred_tokens: List[List[str]] = [pred.split() for pred in predictions]
        ref_tokens: List[List[str]] = [ref.split() for ref in references]
        # Calculate n-gram precisions for n=1 to 4.
        weights: Tuple[float, float, float, float] = (0.25, 0.25, 0.25, 0.25)
        score: float = self._corpus_bleu(ref_tokens, pred_tokens, weights)
        return score * 100.0

    def _corpus_bleu(self, list_refs: List[List[str]], list_hyps: List[List[str]], weights: Tuple[float, ...]) -> float:
        """
        A basic implementation of corpus BLEU.
        
        Args:
            list_refs (List[List[str]]): List of reference sentences (each is a list of tokens).
            list_hyps (List[List[str]]): List of hypothesis sentences (each is a list of tokens).
            weights (Tuple[float, ...]): Weights for 1-gram to N-gram precisions.
        
        Returns:
            float: BLEU score (0 to 1).
        """
        def ngrams(tokens: List[str], n: int) -> Dict[Tuple[str, ...], int]:
            counts: Dict[Tuple[str, ...], int] = {}
            for i in range(len(tokens) - n + 1):
                gram = tuple(tokens[i:i+n])
                counts[gram] = counts.get(gram, 0) + 1
            return counts

        eps: float = 1e-8
        p_n: List[float] = []
        for n, weight in enumerate(weights, start=1):
            total_match: int = 0
            total_count: int = 0
            for ref, hyp in zip(list_refs, list_hyps):
                ref_ngram = ngrams(ref, n)
                hyp_ngram = ngrams(hyp, n)
                match_count: int = sum(min(hyp_ngram.get(gram, 0), ref_ngram.get(gram, 0)) for gram in hyp_ngram)
                total_match += match_count
                total_count += max(len(hyp) - n + 1, 0)
            if total_count == 0:
                p_n.append(0.0)
            else:
                p_n.append(total_match / (total_count + eps))
        # Geometric mean of n-gram precisions
        if min(p_n) > 0:
            geo_mean: float = math.exp(sum(weight * math.log(p) for p, weight in zip(p_n, weights)))
        else:
            geo_mean = 0.0
        # Brevity penalty
        ref_len: int = sum(len(ref) for ref in list_refs)
        hyp_len: int = sum(len(hyp) for hyp in list_hyps)
        bp: float = math.exp(1 - ref_len / hyp_len) if hyp_len < ref_len else 1.0
        bleu: float = bp * geo_mean
        return bleu

    def _extract_constituents(self, parse_str: str) -> set:
        """
        Extracts constituent spans from a bracketed parse tree string.
        This simple implementation assumes that the parse string tokens and parentheses
        are separated by whitespace.
        
        Args:
            parse_str (str): The bracketed parse tree string.
        
        Returns:
            set: A set of tuples representing constituent spans as (start, end) indices.
        """
        tokens: List[str] = parse_str.strip().split()
        constituents: set = set()
        stack: List[int] = []
        word_index: int = 0
        for token in tokens:
            if token.startswith("("):
                # Begin a new constituent span; push current word index.
                stack.append(word_index)
            elif token == ")":
                if stack:
                    start: int = stack.pop()
                    # Only add constituent if it covers at least one word.
                    if word_index > start:
                        constituents.add((start, word_index))
            else:
                # This is a word token; increment word index.
                word_index += 1
        return constituents

    def _compute_f1(self, pred_constituents: set, gold_constituents: set) -> float:
        """
        Computes the F1 score given the sets of predicted and gold constituent spans.
        
        Args:
            pred_constituents (set): Set of predicted constituent spans.
            gold_constituents (set): Set of gold constituent spans.
        
        Returns:
            float: F1 score.
        """
        if not pred_constituents or not gold_constituents:
            return 0.0
        correct: int = len(pred_constituents.intersection(gold_constituents))
        precision: float = correct / len(pred_constituents) if pred_constituents else 0.0
        recall: float = correct / len(gold_constituents) if gold_constituents else 0.0
        if precision + recall == 0:
            return 0.0
        f1: float = 2 * precision * recall / (precision + recall)
        return f1

# For testing the evaluation module independently.
if __name__ == "__main__":
    # Dummy test for Evaluation module
    logging.info("Running evaluation module test...")

    # Create a dummy configuration dictionary (as would be loaded from config.yaml)
    dummy_config: Dict[str, Any] = {
        "evaluation": {
            "beam_size": 4,
            "length_penalty": 0.6,
            "beam_size_parsing": 21,
            "length_penalty_parsing": 0.3,
            "max_output_length": "input_length + 50"  # Not used directly here.
        }
    }

    # Create a dummy model with minimal implementation using torch.nn.Module.
    # For testing, we'll use a model that simply returns a tensor containing EOS_TOKEN after BOS.
    class DummyTransformer(nn.Module):
        def __init__(self):
            super(DummyTransformer, self).__init__()
            self.out_projection = nn.Linear(10, 50000, bias=False)
            # Dummy embedding to enable encode/decode calls.
            self.embedding = nn.Embedding(50000, 10)
            self.pos_encoding = nn.Identity()
            self.encoder_layers = nn.ModuleList([])
            self.decoder_layers = nn.ModuleList([])

        def encode(self, src: torch.Tensor, src_mask=None) -> torch.Tensor:
            batch_size, seq_len = src.size()
            # Return a dummy tensor
            return torch.zeros(batch_size, seq_len, 10, device=src.device)

        def decode(self, tgt: torch.Tensor, encoder_output: torch.Tensor, tgt_mask=None, src_mask=None) -> torch.Tensor:
            batch_size, seq_len = tgt.size()
            # Return a dummy tensor that increases output features.
            return torch.zeros(batch_size, seq_len, 10, device=tgt.device)

        @staticmethod
        def generate_square_subsequent_mask(sz: int) -> torch.Tensor:
            mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
            mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
            return mask

    dummy_model = DummyTransformer()
    dummy_model.eval()

    # Create dummy evaluation data for translation: 1 batch with 2 examples.
    dummy_src = torch.tensor([[BOS_TOKEN, 5, 6, 7, EOS_TOKEN, PAD_TOKEN], [BOS_TOKEN, 8, 9, EOS_TOKEN, PAD_TOKEN, PAD_TOKEN]], dtype=torch.long)
    dummy_tgt = torch.tensor([[BOS_TOKEN, 15, 16, EOS_TOKEN, PAD_TOKEN], [BOS_TOKEN, 20, 21, 22, EOS_TOKEN]], dtype=torch.long)
    translation_eval_data = [(dummy_src, dummy_tgt)]

    # Create dummy evaluation data for parsing: 1 batch with 2 examples.
    dummy_src_pars = torch.tensor([[BOS_TOKEN, 11, 12, EOS_TOKEN, PAD_TOKEN], [BOS_TOKEN, 13, 14, EOS_TOKEN, PAD_TOKEN]], dtype=torch.long)
    # Dummy parse trees as bracketed strings.
    dummy_parses = ["( S ( NP John ) ( VP runs ) )", "( S ( NP Mary ) ( VP sleeps ) )"]
    parsing_eval_data = [(dummy_src_pars, dummy_parses)]

    eval_data = (translation_eval_data, parsing_eval_data)

    evaluator = Evaluation(dummy_model, eval_data, dummy_config)
    translation_metrics = evaluator.evaluate_translation()
    parsing_metrics = evaluator.evaluate_parsing()

    logging.info("Dummy Translation Metrics: %s", translation_metrics)
    logging.info("Dummy Parsing Metrics: %s", parsing_metrics)