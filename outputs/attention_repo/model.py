"""model.py

This module implements the TransformerModel class along with its helper submodules,
reproducing the architecture detailed in "Attention Is All You Need".
It includes the embedding layers (with weight sharing), positional encoding, multi-head attention,
feed-forward networks, encoder and decoder layers with residual connections and layer normalization.

The class TransformerModel provides three public methods:
    - forward(src: torch.Tensor, tgt: torch.Tensor) -> torch.Tensor
    - encode(src: torch.Tensor) -> torch.Tensor
    - decode(tgt: torch.Tensor, encoder_output: torch.Tensor) -> torch.Tensor

All configuration values are read from the provided configuration dictionary.
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from typing import Optional, Dict, Any

# --------------------------- Helper Modules ----------------------------------

class PositionalEncoding(nn.Module):
    """Implements the sinusoidal positional encoding function."""
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000) -> None:
        """
        Args:
            d_model (int): Embedding dimension.
            dropout (float): Dropout rate applied after adding positional encoding.
            max_len (int): Maximum sequence length to precompute positional encodings.
        """
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # Create constant 'pe' matrix with values dependant on pos and i
        pe = torch.zeros(max_len, d_model)  # shape: [max_len, d_model]
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)  # shape: [max_len, 1]
        div_term = torch.exp(torch.arange(0, d_model, 2, dtype=torch.float) * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)  # even indices
        pe[:, 1::2] = torch.cos(position * div_term)  # odd indices
        pe = pe.unsqueeze(0)  # shape: [1, max_len, d_model]
        # Register as buffer so it's not considered a parameter but still moved to GPU
        self.register_buffer('pe', pe)

    def forward(self, x: Tensor) -> Tensor:
        """
        Args:
            x (Tensor): Tensor of shape [batch_size, seq_length, d_model].

        Returns:
            Tensor: Output tensor after adding positional encodings and applying dropout.
        """
        seq_len = x.size(1)
        # Add positional encoding
        x = x + self.pe[:, :seq_len, :]
        return self.dropout(x)


class MultiHeadAttention(nn.Module):
    """Implements multi-head attention mechanism."""
    def __init__(self, d_model: int, num_heads: int, d_k: int, d_v: int, dropout: float = 0.1) -> None:
        """
        Args:
            d_model (int): Model dimension.
            num_heads (int): Number of attention heads.
            d_k (int): Dimensionality of the key/query per head.
            d_v (int): Dimensionality of the value per head.
            dropout (float): Dropout rate applied to attention weights.
        """
        super(MultiHeadAttention, self).__init__()
        self.num_heads = num_heads
        self.d_k = d_k
        self.d_v = d_v

        # Linear layers for projecting input to queries, keys and values
        self.linear_q = nn.Linear(d_model, num_heads * d_k)
        self.linear_k = nn.Linear(d_model, num_heads * d_k)
        self.linear_v = nn.Linear(d_model, num_heads * d_v)

        # Final output linear transformation
        self.linear_out = nn.Linear(num_heads * d_v, d_model)

        self.dropout = nn.Dropout(p=dropout)

    def forward(self, query: Tensor, key: Tensor, value: Tensor, mask: Optional[Tensor] = None) -> Tensor:
        """
        Compute multi-head attention.
        
        Args:
            query (Tensor): Query tensor of shape (batch_size, seq_len_q, d_model).
            key (Tensor): Key tensor of shape (batch_size, seq_len_k, d_model).
            value (Tensor): Value tensor of shape (batch_size, seq_len_v, d_model).
            mask (Optional[Tensor]): Attention mask of shape (batch_size, 1, seq_len_q, seq_len_k) or (batch_size, seq_len_q, seq_len_k).

        Returns:
            Tensor: Output tensor of shape (batch_size, seq_len_q, d_model).
        """
        batch_size = query.size(0)

        # Linear projections
        q = self.linear_q(query)  # shape: [batch_size, seq_len_q, num_heads * d_k]
        k = self.linear_k(key)    # shape: [batch_size, seq_len_k, num_heads * d_k]
        v = self.linear_v(value)  # shape: [batch_size, seq_len_v, num_heads * d_v]

        # Reshape and transpose for attention: shape: [batch_size, num_heads, seq_len, d_k or d_v]
        q = q.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        k = k.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        v = v.view(batch_size, -1, self.num_heads, self.d_v).transpose(1, 2)

        # Scaled dot-product attention
        # scores shape: [batch_size, num_heads, seq_len_q, seq_len_k]
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.d_k)

        if mask is not None:
            # mask should be broadcastable to scores' shape.
            scores = scores.masked_fill(mask == 0, float('-inf'))

        attn = F.softmax(scores, dim=-1)
        attn = self.dropout(attn)

        # Multiply attention weights with values, shape: [batch_size, num_heads, seq_len_q, d_v]
        context = torch.matmul(attn, v)

        # Concatenate heads and apply final linear projection
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.num_heads * self.d_v)
        output = self.linear_out(context)
        return output


class FeedForward(nn.Module):
    """Implements the position-wise feed-forward network."""
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1) -> None:
        """
        Args:
            d_model (int): Model dimension.
            d_ff (int): Inner-layer dimension of the feed-forward network.
            dropout (float): Dropout rate applied to the feed-forward network.
        """
        super(FeedForward, self).__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(p=dropout)
    
    def forward(self, x: Tensor) -> Tensor:
        """
        Args:
            x (Tensor): Input tensor of shape (batch_size, seq_len, d_model).
        
        Returns:
            Tensor: Output tensor of shape (batch_size, seq_len, d_model).
        """
        x = self.linear1(x)
        x = F.relu(x)
        x = self.dropout(x)
        x = self.linear2(x)
        return x


class EncoderLayer(nn.Module):
    """Implements one encoder layer of the Transformer."""
    def __init__(self, d_model: int, num_heads: int, d_k: int, d_v: int, d_ff: int, dropout: float = 0.1) -> None:
        super(EncoderLayer, self).__init__()
        self.self_attn = MultiHeadAttention(d_model, num_heads, d_k, d_v, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, x: Tensor, mask: Optional[Tensor] = None) -> Tensor:
        """
        Args:
            x (Tensor): Input tensor of shape (batch_size, seq_length, d_model).
            mask (Optional[Tensor]): Attention mask.

        Returns:
            Tensor: Output tensor of shape (batch_size, seq_length, d_model).
        """
        # Multi-head self-attention sub-layer with residual connection and layer normalization
        attn_output = self.self_attn(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        # Feed-forward sub-layer with residual connection and layer normalization
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        return x


class DecoderLayer(nn.Module):
    """Implements one decoder layer of the Transformer."""
    def __init__(self, d_model: int, num_heads: int, d_k: int, d_v: int, d_ff: int, dropout: float = 0.1) -> None:
        super(DecoderLayer, self).__init__()
        self.self_attn = MultiHeadAttention(d_model, num_heads, d_k, d_v, dropout)
        self.enc_dec_attn = MultiHeadAttention(d_model, num_heads, d_k, d_v, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, x: Tensor, encoder_output: Tensor, tgt_mask: Optional[Tensor] = None, src_tgt_mask: Optional[Tensor] = None) -> Tensor:
        """
        Args:
            x (Tensor): Input tensor (target sequence embeddings) of shape (batch_size, tgt_seq_len, d_model).
            encoder_output (Tensor): Encoder output tensor of shape (batch_size, src_seq_len, d_model).
            tgt_mask (Optional[Tensor]): Mask for target self-attention.
            src_tgt_mask (Optional[Tensor]): Mask for encoder-decoder attention.

        Returns:
            Tensor: Output tensor of shape (batch_size, tgt_seq_len, d_model).
        """
        # Masked multi-head self-attention sub-layer
        self_attn_output = self.self_attn(x, x, x, tgt_mask)
        x = self.norm1(x + self.dropout(self_attn_output))
        # Encoder-decoder attention sub-layer
        enc_dec_attn_output = self.enc_dec_attn(x, encoder_output, encoder_output, src_tgt_mask)
        x = self.norm2(x + self.dropout(enc_dec_attn_output))
        # Feed-forward sub-layer
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout(ff_output))
        return x


# --------------------------- Main Transformer Model ------------------------------

class TransformerModel(nn.Module):
    """Implements the complete Transformer model with encoder and decoder stacks.

    Public Methods:
        forward(src: Tensor, tgt: Tensor) -> Tensor
        encode(src: Tensor) -> Tensor
        decode(tgt: Tensor, encoder_output: Tensor) -> Tensor
    """
    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Args:
            config (Dict[str, Any]): Configuration dictionary containing model and training parameters.
        """
        super(TransformerModel, self).__init__()
        # Get model parameters from configuration with defaults from config.yaml
        model_config = config.get("model", {})
        training_config = config.get("training", {})

        self.d_model: int = int(model_config.get("d_model", 512))
        self.d_ff: int = int(model_config.get("d_ff", 2048))
        self.num_layers: int = int(model_config.get("num_layers", 6))
        self.num_heads: int = int(model_config.get("num_heads", 8))
        self.d_k: int = int(model_config.get("d_k", 64))
        self.d_v: int = int(model_config.get("d_v", 64))
        self.positional_encoding_type: str = model_config.get("positional_encoding", "sinusoidal")
        self.dropout_rate: float = float(training_config.get("dropout", 0.1))

        # Shared token embedding layer, used for both encoder and decoder with weight tying.
        # Assume vocabulary size is provided externally when model is instantiated and set later.
        # For now, set a default vocab size; in practice, it will be overwritten via weight sharing.
        default_vocab_size: int = 50000
        self.embedding = nn.Embedding(default_vocab_size, self.d_model)
        self.embedding_scale = math.sqrt(self.d_model)

        # Positional Encoding module
        if self.positional_encoding_type == "sinusoidal":
            self.pos_encoding = PositionalEncoding(self.d_model, dropout=self.dropout_rate)
        else:
            # If learned positional encoding is desired, can be implemented here.
            # Defaulting to sinusoidal if not specified.
            self.pos_encoding = PositionalEncoding(self.d_model, dropout=self.dropout_rate)

        # Encoder Stack: A stack of EncoderLayers.
        self.encoder_layers = nn.ModuleList([
            EncoderLayer(self.d_model, self.num_heads, self.d_k, self.d_v, self.d_ff, dropout=self.dropout_rate)
            for _ in range(self.num_layers)
        ])

        # Decoder Stack: A stack of DecoderLayers.
        self.decoder_layers = nn.ModuleList([
            DecoderLayer(self.d_model, self.num_heads, self.d_k, self.d_v, self.d_ff, dropout=self.dropout_rate)
            for _ in range(self.num_layers)
        ])

        # Final linear projection (tied with embedding weights for weight sharing)
        self.out_projection = nn.Linear(self.d_model, default_vocab_size, bias=False)
        # Tie weights with embedding. The weights are shared.
        self.out_projection.weight = self.embedding.weight

    def forward(self, src: Tensor, tgt: Tensor, src_mask: Optional[Tensor] = None, tgt_mask: Optional[Tensor] = None) -> Tensor:
        """
        Implements the complete forward pass of the Transformer model.
        
        Args:
            src (Tensor): Source sequence indices [batch_size, src_seq_len].
            tgt (Tensor): Target sequence indices [batch_size, tgt_seq_len].
            src_mask (Optional[Tensor]): Mask for the source sequences.
            tgt_mask (Optional[Tensor]): Mask for the target sequences (should include subsequent mask for auto-regression).
        
        Returns:
            Tensor: Logits of shape [batch_size, tgt_seq_len, vocab_size].
        """
        # Encode the source sequence.
        encoder_output = self.encode(src, src_mask)

        # Decode target sequence given encoder output.
        decoder_output = self.decode(tgt, encoder_output, tgt_mask, src_mask)

        # Project decoder output to vocabulary space using tied weights.
        logits = self.out_projection(decoder_output)
        return logits

    def encode(self, src: Tensor, src_mask: Optional[Tensor] = None) -> Tensor:
        """
        Encodes the source sequence using the encoder stack.
        
        Args:
            src (Tensor): Source sequence indices [batch_size, src_seq_len].
            src_mask (Optional[Tensor]): Optional mask for the source sequences.
        
        Returns:
            Tensor: Encoder output representations [batch_size, src_seq_len, d_model].
        """
        # Get token embeddings and scale.
        x = self.embedding(src) * self.embedding_scale
        # Add positional encoding.
        x = self.pos_encoding(x)
        # Pass through each encoder layer.
        for layer in self.encoder_layers:
            x = layer(x, src_mask)
        return x

    def decode(self, tgt: Tensor, encoder_output: Tensor, tgt_mask: Optional[Tensor] = None, src_mask: Optional[Tensor] = None) -> Tensor:
        """
        Decodes the target sequence using the decoder stack.
        
        Args:
            tgt (Tensor): Target sequence indices [batch_size, tgt_seq_len].
            encoder_output (Tensor): Encoder outputs [batch_size, src_seq_len, d_model].
            tgt_mask (Optional[Tensor]): Mask for target self-attention.
            src_mask (Optional[Tensor]): Mask for encoder-decoder attention.
        
        Returns:
            Tensor: Decoder output representations [batch_size, tgt_seq_len, d_model].
        """
        # Get target embeddings and scale.
        x = self.embedding(tgt) * self.embedding_scale
        # Add positional encoding.
        x = self.pos_encoding(x)
        # Pass through each decoder layer.
        for layer in self.decoder_layers:
            x = layer(x, encoder_output, tgt_mask, src_mask)
        return x

    @staticmethod
    def generate_square_subsequent_mask(sz: int) -> Tensor:
        """
        Generates an upper-triangular matrix of -inf, with zeros on diag.
        This mask is used in the decoder to prevent positions from attending to subsequent positions.
        
        Args:
            sz (int): Size of the square mask (seq_len).
    
        Returns:
            Tensor: A mask tensor of shape [sz, sz].
        """
        mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
        mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
        return mask

# For testing purposes (if running this file directly)
if __name__ == "__main__":
    # Create a dummy config dictionary with default values
    dummy_config = {
        "model": {
            "d_model": 512,
            "d_ff": 2048,
            "num_layers": 6,
            "num_heads": 8,
            "d_k": 64,
            "d_v": 64,
            "positional_encoding": "sinusoidal"
        },
        "training": {
            "dropout": 0.1
        }
    }
    # Instantiate model.
    model = TransformerModel(dummy_config)
    # Create dummy inputs: assume batch_size=2, src_seq_len=10, tgt_seq_len=8.
    src_dummy = torch.randint(0, 50000, (2, 10))  # using default vocab size 50000
    tgt_dummy = torch.randint(0, 50000, (2, 8))
    # Generate mask for target (subsequent mask)
    tgt_mask = TransformerModel.generate_square_subsequent_mask(tgt_dummy.size(1)).to(src_dummy.device)
    # Forward pass
    logits = model(src_dummy, tgt_dummy, src_mask=None, tgt_mask=tgt_mask)
    print("Logits shape:", logits.shape)  # Expected shape: [2, 8, 50000]
