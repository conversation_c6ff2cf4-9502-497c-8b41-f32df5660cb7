## config.yaml
training:
  optimizer: "<PERSON>"                # Adam optimizer with provided hyperparameters
  beta1: 0.9                       # First moment decay rate
  beta2: 0.98                      # Second moment decay rate
  epsilon: 1e-9                    # Small constant for numerical stability
  warmup_steps: 4000               # Number of warmup steps for learning rate schedule
  total_steps: 100000              # Total training steps for the base model (12 hours on 8 GPUs)
  batch_size_tokens: 25000         # Approximate number of tokens per batch (for both source and target)
  dropout: 0.1                     # Dropout rate applied in sub-layers and embeddings
  label_smoothing: 0.1             # Label smoothing value
  learning_rate_schedule: "lrate = d_model^-0.5 * min(step_num^-0.5, step_num * warmup_steps^-1.5)"  # Learning rate formula as defined in the paper

model:
  d_model: 512                   # Embedding and model dimension for base model
  d_ff: 2048                     # Dimensionality of the inner feed-forward layer
  num_layers: 6                  # Number of encoder and decoder layers
  num_heads: 8                   # Number of attention heads
  d_k: 64                        # Dimension of key vectors for multi-head attention
  d_v: 64                        # Dimension of value vectors for multi-head attention
  positional_encoding: "sinusoidal"  # Type of positional encoding used (sinusoidal functions)

evaluation:
  beam_size: 4                 # Beam size used during beam search decoding
  length_penalty: 0.6          # Length penalty used in beam search
  max_output_length: "input_length + 50"  # Maximum allowable output length during inference

hardware:
  num_gpus: 8                # Number of GPUs used for training
  gpu_type: "NVIDIA P100"      # Type of GPU used

translation:
  dataset: "WMT 2014 English-German"  # Dataset for machine translation experiments
  vocabulary_size: 37000              # Shared source-target vocabulary size (using byte-pair encoding)

parsing:
  dataset: "WSJ Penn Treebank"         # Dataset for English constituency parsing
  vocabulary_size: 16000               # Vocabulary size for WSJ-only experiments