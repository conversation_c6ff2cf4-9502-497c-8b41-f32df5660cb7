"""dataset_loader.py

This module implements the DatasetLoader class for loading, tokenizing, and batching
datasets for both translation (WMT 2014 English-German) and parsing (WSJ Penn Treebank)
tasks. The implemented methods assume that raw data files are stored in predetermined paths.
If the files do not exist, the loader logs an error.

The tokenization used here is a simple whitespace split to simulate a BPE-like process.
Vocabulary construction is based on word frequency up to a given maximum size and includes
special tokens for padding and unknown words.

Usage:
    config = load_config("config.yaml")  # Load configuration from YAML
    loader = DatasetLoader(config)
    translation_data = loader.load_translation_data()
    parsing_data = loader.load_parsing_data()
"""

import os
import logging
import yaml
import torch
import numpy as np
from collections import Counter
from typing import List, Tuple, Dict, Any, Optional

# Setup basic logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Special tokens
PAD_TOKEN: str = "<PAD>"
UNK_TOKEN: str = "<UNK>"

# Default file paths if not provided externally.
DEFAULT_TRANSLATION_PATHS: Dict[str, Dict[str, str]] = {
    "train": {"src": "data/translation/train.en", "tgt": "data/translation/train.de"},
    "dev": {"src": "data/translation/dev.en", "tgt": "data/translation/dev.de"},
    "test": {"src": "data/translation/test.en", "tgt": "data/translation/test.de"}
}

DEFAULT_PARSING_PATHS: Dict[str, str] = {
    "train": "data/parsing/train.txt",
    "dev": "data/parsing/dev.txt",
    "test": "data/parsing/test.txt"
}


class DatasetLoader:
    """Class for loading and preprocessing datasets for both translation and parsing tasks.

    Attributes:
        config (dict): Configuration dictionary containing training, model, translation,
                       and parsing settings.
        batch_size_tokens (int): Approximate number of tokens per batch as specified in config.
        translation_vocab_size (int): Maximum vocabulary size for translation tasks.
        parsing_vocab_size (int): Maximum vocabulary size for parsing tasks.
    """

    def __init__(self, config: dict) -> None:
        """
        Initializes the DatasetLoader with the given configuration.

        Args:
            config (dict): Configuration dictionary.
        """
        self.config: dict = config
        self.batch_size_tokens: int = int(config.get("training", {}).get("batch_size_tokens", 25000))
        # Translation config
        translation_config: dict = config.get("translation", {})
        self.translation_dataset_name: str = translation_config.get("dataset", "WMT 2014 English-German")
        self.translation_vocab_size: int = int(translation_config.get("vocabulary_size", 37000))
        # Parsing config
        parsing_config: dict = config.get("parsing", {})
        self.parsing_dataset_name: str = parsing_config.get("dataset", "WSJ Penn Treebank")
        self.parsing_vocab_size: int = int(parsing_config.get("vocabulary_size", 16000))
        # Internal vocabularies (populated during dataset loading)
        self.translation_vocab: Optional[Dict[str, int]] = None
        self.parsing_vocab: Optional[Dict[str, int]] = None

    # --------------------- Translation Data Methods --------------------- #

    def load_translation_data(self) -> Tuple[List[Any], List[Any], List[Any]]:
        """
        Loads, tokenizes, and batches the translation dataset.

        Returns:
            tuple: A tuple of (train_batches, dev_batches, test_batches), where each element is
                   a list of batches. Each batch is a tuple (src_tensor, tgt_tensor) of PyTorch tensors.
        """
        logging.info("Loading translation dataset: %s", self.translation_dataset_name)

        # Read raw data files
        train_src_lines = self._read_file(DEFAULT_TRANSLATION_PATHS["train"]["src"])
        train_tgt_lines = self._read_file(DEFAULT_TRANSLATION_PATHS["train"]["tgt"])
        dev_src_lines = self._read_file(DEFAULT_TRANSLATION_PATHS["dev"]["src"])
        dev_tgt_lines = self._read_file(DEFAULT_TRANSLATION_PATHS["dev"]["tgt"])
        test_src_lines = self._read_file(DEFAULT_TRANSLATION_PATHS["test"]["src"])
        test_tgt_lines = self._read_file(DEFAULT_TRANSLATION_PATHS["test"]["tgt"])

        # Check for consistency
        if len(train_src_lines) != len(train_tgt_lines):
            logging.error("Mismatch in number of training source and target sentences.")
            raise ValueError("Training data source and target files have different number of lines.")

        # Tokenize sentences (simple whitespace tokenization simulating BPE)
        train_src_tokens = [self._tokenize_sentence(line) for line in train_src_lines]
        train_tgt_tokens = [self._tokenize_sentence(line) for line in train_tgt_lines]
        dev_src_tokens = [self._tokenize_sentence(line) for line in dev_src_lines]
        dev_tgt_tokens = [self._tokenize_sentence(line) for line in dev_tgt_lines]
        test_src_tokens = [self._tokenize_sentence(line) for line in test_src_lines]
        test_tgt_tokens = [self._tokenize_sentence(line) for line in test_tgt_lines]

        # Build shared vocabulary using training data only
        corpus_tokens: List[str] = [token for sentence in train_src_tokens + train_tgt_tokens for token in sentence]
        self.translation_vocab = self._build_vocab(corpus_tokens, self.translation_vocab_size)
        logging.info("Translation vocabulary size: %d", len(self.translation_vocab))

        # Convert tokens to indices
        train_src_indices = [self._tokens_to_indices(sentence, self.translation_vocab) for sentence in train_src_tokens]
        train_tgt_indices = [self._tokens_to_indices(sentence, self.translation_vocab) for sentence in train_tgt_tokens]
        dev_src_indices = [self._tokens_to_indices(sentence, self.translation_vocab) for sentence in dev_src_tokens]
        dev_tgt_indices = [self._tokens_to_indices(sentence, self.translation_vocab) for sentence in dev_tgt_tokens]
        test_src_indices = [self._tokens_to_indices(sentence, self.translation_vocab) for sentence in test_src_tokens]
        test_tgt_indices = [self._tokens_to_indices(sentence, self.translation_vocab) for sentence in test_tgt_tokens]

        # Create batches with bucketing strategy (for each pair, use max(src_len, tgt_len) for token count)
        train_batches = self._create_translation_batches(train_src_indices, train_tgt_indices, self.batch_size_tokens)
        dev_batches = self._create_translation_batches(dev_src_indices, dev_tgt_indices, self.batch_size_tokens)
        test_batches = self._create_translation_batches(test_src_indices, test_tgt_indices, self.batch_size_tokens)

        logging.info("Loaded translation data: %d training batches, %d dev batches, %d test batches",
                     len(train_batches), len(dev_batches), len(test_batches))
        return train_batches, dev_batches, test_batches

    # --------------------- Parsing Data Methods --------------------- #

    def load_parsing_data(self) -> Tuple[List[Any], List[Any], List[Any]]:
        """
        Loads, tokenizes, and batches the parsing dataset.

        Expected file format (per line): sentence[TAB]parse_structure

        Returns:
            tuple: A tuple of (train_batches, dev_batches, test_batches), where each element is
                   a list of batches. Each batch is a tuple (src_tensor, parse_tensor) where src_tensor
                   is a tensor of token indices and parse_tensor is a tensor or structure representing the parse.
        """
        logging.info("Loading parsing dataset: %s", self.parsing_dataset_name)

        # Read raw parsing data files
        train_lines = self._read_file(DEFAULT_PARSING_PATHS["train"])
        dev_lines = self._read_file(DEFAULT_PARSING_PATHS["dev"])
        test_lines = self._read_file(DEFAULT_PARSING_PATHS["test"])

        # Parse each line into sentence and parse structure (assumes tab-delimited)
        train_sentences, train_parses = self._split_parsing_lines(train_lines)
        dev_sentences, dev_parses = self._split_parsing_lines(dev_lines)
        test_sentences, test_parses = self._split_parsing_lines(test_lines)

        # Tokenize sentences for parsing
        train_tokens = [self._tokenize_sentence(sentence) for sentence in train_sentences]
        dev_tokens = [self._tokenize_sentence(sentence) for sentence in dev_sentences]
        test_tokens = [self._tokenize_sentence(sentence) for sentence in test_sentences]

        # Build vocabulary for parsing using training sentences only
        corpus_tokens = [token for sentence in train_tokens for token in sentence]
        self.parsing_vocab = self._build_vocab(corpus_tokens, self.parsing_vocab_size)
        logging.info("Parsing vocabulary size: %d", len(self.parsing_vocab))

        # Convert tokens to indices
        train_indices = [self._tokens_to_indices(sentence, self.parsing_vocab) for sentence in train_tokens]
        dev_indices = [self._tokens_to_indices(sentence, self.parsing_vocab) for sentence in dev_tokens]
        test_indices = [self._tokens_to_indices(sentence, self.parsing_vocab) for sentence in test_tokens]

        # For parsing, we simply return the token indices along with the parse structures (as raw strings)
        train_batches = self._create_parsing_batches(train_indices, train_parses, self.batch_size_tokens)
        dev_batches = self._create_parsing_batches(dev_indices, dev_parses, self.batch_size_tokens)
        test_batches = self._create_parsing_batches(test_indices, test_parses, self.batch_size_tokens)

        logging.info("Loaded parsing data: %d training batches, %d dev batches, %d test batches",
                     len(train_batches), len(dev_batches), len(test_batches))
        return train_batches, dev_batches, test_batches

    # --------------------- Internal Utility Methods --------------------- #

    def _read_file(self, file_path: str) -> List[str]:
        """
        Reads a file and returns the list of lines.

        Args:
            file_path (str): Path to the file.

        Returns:
            List[str]: List of lines.
        """
        if not os.path.exists(file_path):
            logging.error("Data file not found: %s", file_path)
            raise FileNotFoundError(f"Data file not found: {file_path}")
        with open(file_path, "r", encoding="utf-8") as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        return lines

    def _tokenize_sentence(self, sentence: str) -> List[str]:
        """
        Tokenizes a sentence using a simple whitespace split.

        Args:
            sentence (str): A sentence string.

        Returns:
            List[str]: List of tokens.
        """
        return sentence.strip().split()

    def _build_vocab(self, tokens: List[str], vocab_size: int) -> Dict[str, int]:
        """
        Builds a vocabulary of the most frequent tokens up to vocab_size.
        Always includes PAD and UNK tokens with indices 0 and 1.

        Args:
            tokens (List[str]): List of tokens from the corpus.
            vocab_size (int): Maximum vocabulary size.

        Returns:
            Dict[str, int]: Mapping from token to index.
        """
        counter = Counter(tokens)
        # Reserve two spots for PAD and UNK
        most_common = counter.most_common(vocab_size - 2)
        vocab = {PAD_TOKEN: 0, UNK_TOKEN: 1}
        index = 2
        for token, _ in most_common:
            if token not in vocab:
                vocab[token] = index
                index += 1
        return vocab

    def _tokens_to_indices(self, tokens: List[str], vocab: Dict[str, int]) -> List[int]:
        """
        Converts a list of tokens into their corresponding indices using the given vocabulary.

        Args:
            tokens (List[str]): List of tokens.
            vocab (Dict[str, int]): Vocabulary mapping tokens to indices.

        Returns:
            List[int]: List of token indices.
        """
        return [vocab.get(token, vocab.get(UNK_TOKEN)) for token in tokens]

    def _pad_batch(self, batch: List[List[int]]) -> torch.Tensor:
        """
        Pads a batch of token index lists to the same length.

        Args:
            batch (List[List[int]]): List of tokenized sentences (as lists of indices).

        Returns:
            torch.Tensor: Padded tensor of shape (batch_size, max_seq_length).
        """
        batch_size = len(batch)
        max_length = max(len(seq) for seq in batch)
        padded_batch = np.full((batch_size, max_length), fill_value=0, dtype=np.int64)  # 0 corresponds to PAD_TOKEN
        for i, seq in enumerate(batch):
            padded_batch[i, :len(seq)] = np.array(seq, dtype=np.int64)
        return torch.tensor(padded_batch, dtype=torch.long)

    def _create_translation_batches(self, src_data: List[List[int]], tgt_data: List[List[int]], batch_token_limit: int
                                    ) -> List[Tuple[torch.Tensor, torch.Tensor]]:
        """
        Creates batches for translation data such that each batch has approximately batch_token_limit tokens,
        where the token count for a pair is defined as max(src_len, tgt_len).

        Args:
            src_data (List[List[int]]): List of source sentences (as token indices).
            tgt_data (List[List[int]]): List of target sentences (as token indices).
            batch_token_limit (int): Maximum total tokens per batch.

        Returns:
            List[Tuple[torch.Tensor, torch.Tensor]]: List of tuples (src_batch, tgt_batch) as PyTorch tensors.
        """
        assert len(src_data) == len(tgt_data), "Source and target data must have the same length."
        data_pairs = list(zip(src_data, tgt_data))
        # Sort pairs by max length to facilitate bucketing
        data_pairs.sort(key=lambda pair: max(len(pair[0]), len(pair[1])))
        batches = []
        current_src_batch = []
        current_tgt_batch = []
        current_tokens = 0
        for src_seq, tgt_seq in data_pairs:
            seq_len = max(len(src_seq), len(tgt_seq))
            if current_tokens + seq_len > batch_token_limit and current_src_batch:
                # Create batch
                src_tensor = self._pad_batch(current_src_batch)
                tgt_tensor = self._pad_batch(current_tgt_batch)
                batches.append((src_tensor, tgt_tensor))
                # Reset batch accumulators
                current_src_batch = []
                current_tgt_batch = []
                current_tokens = 0
            current_src_batch.append(src_seq)
            current_tgt_batch.append(tgt_seq)
            current_tokens += seq_len
        # Add any remaining sequences as the last batch
        if current_src_batch:
            src_tensor = self._pad_batch(current_src_batch)
            tgt_tensor = self._pad_batch(current_tgt_batch)
            batches.append((src_tensor, tgt_tensor))
        return batches

    def _split_parsing_lines(self, lines: List[str]) -> Tuple[List[str], List[str]]:
        """
        Splits each line of the parsing dataset into a sentence and its parse structure.
        Expects each line to contain a sentence and parse separated by a tab.

        Args:
            lines (List[str]): Lines from the parsing data file.

        Returns:
            Tuple[List[str], List[str]]: Two lists, one with sentences and another with parse structures.
        """
        sentences = []
        parses = []
        for line in lines:
            parts = line.split("\t")
            if len(parts) != 2:
                logging.warning("Line in parsing dataset does not have two tab-separated parts: %s", line)
                continue
            sentence, parse = parts[0].strip(), parts[1].strip()
            sentences.append(sentence)
            parses.append(parse)
        return sentences, parses

    def _create_parsing_batches(self, tokenized_sentences: List[List[int]], parses: List[str], batch_token_limit: int
                                  ) -> List[Tuple[torch.Tensor, List[str]]]:
        """
        Creates batches for parsing data such that each batch has approximately batch_token_limit tokens.
        Batching is performed based on the length of the tokenized sentences.

        Args:
            tokenized_sentences (List[List[int]]): List of sentences (as token indices).
            parses (List[str]): List of string representations of parse trees.
            batch_token_limit (int): Maximum total tokens per batch.

        Returns:
            List[Tuple[torch.Tensor, List[str]]]: List of tuples (src_batch, parse_batch), where src_batch
                                                  is a padded tensor and parse_batch is a list of parse strings.
        """
        assert len(tokenized_sentences) == len(parses), "Mismatch in number of sentences and parse structures."
        data_items = list(zip(tokenized_sentences, parses))
        # Sort by sentence length
        data_items.sort(key=lambda item: len(item[0]))
        batches = []
        current_batch = []
        current_parses = []
        current_tokens = 0
        for tokens, parse in data_items:
            length = len(tokens)
            if current_tokens + length > batch_token_limit and current_batch:
                # Create batch
                batch_tensor = self._pad_batch(current_batch)
                batches.append((batch_tensor, current_parses))
                current_batch = []
                current_parses = []
                current_tokens = 0
            current_batch.append(tokens)
            current_parses.append(parse)
            current_tokens += length
        if current_batch:
            batch_tensor = self._pad_batch(current_batch)
            batches.append((batch_tensor, current_parses))
        return batches


def load_config(config_path: str = "config.yaml") -> dict:
    """
    Loads a YAML configuration file.

    Args:
        config_path (str, optional): Path to the configuration file. Defaults to "config.yaml".

    Returns:
        dict: Configuration dictionary.
    """
    if not os.path.exists(config_path):
        logging.error("Configuration file not found: %s", config_path)
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    return config


if __name__ == "__main__":
    # For testing purposes
    try:
        config_dict = load_config("config.yaml")
        dataset_loader = DatasetLoader(config_dict)
        # Attempt to load translation data (this will raise error if default files are missing)
        _ = dataset_loader.load_translation_data()
        # Attempt to load parsing data
        _ = dataset_loader.load_parsing_data()
        logging.info("DatasetLoader module loaded and data processed successfully.")
    except Exception as e:
        logging.error("Error in DatasetLoader: %s", e)
