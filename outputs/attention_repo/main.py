"""main.py

This module is the main entry point for running Transformer experiments.
It loads configuration from config.yaml, initializes the DatasetLoader, TransformerModel,
Trainer, and Evaluation classes, and then orchestrates the training and evaluation workflow.
"""

import sys
import logging
from dataset_loader import DatasetLoader, load_config
from model import TransformerModel
from trainer import Trainer
from evaluation import Evaluation

class App:
    """Main application class that orchestrates the experiment workflow.

    Attributes:
        config (dict): The configuration dictionary loaded from config.yaml.
    """

    def __init__(self, config: dict) -> None:
        """
        Initializes the App with the given configuration.

        Args:
            config (dict): Configuration dictionary loaded from config.yaml.
        """
        self.config: dict = config
        logging.info("App initialized with configuration.")

        # Sanity checks for essential configuration parameters.
        required_training_keys = ["total_steps", "warmup_steps", "dropout", "batch_size_tokens", "label_smoothing"]
        for key in required_training_keys:
            if key not in self.config.get("training", {}):
                logging.warning("Missing training config key: %s. Using default if applicable.", key)

        required_model_keys = ["d_model", "d_ff", "num_layers", "num_heads", "d_k", "d_v", "positional_encoding"]
        for key in required_model_keys:
            if key not in self.config.get("model", {}):
                logging.warning("Missing model config key: %s. Using default if applicable.", key)

    def run_experiment(self) -> None:
        """
        Coordinates the full experiment pipeline:
          - Loads datasets for translation and parsing.
          - Instantiates the TransformerModel.
          - Runs the training loop.
          - Evaluates the model on translation and parsing tasks.
        """
        try:
            logging.info("Starting dataset loading ...")
            dataset_loader: DatasetLoader = DatasetLoader(self.config)
            
            # Load translation and parsing datasets.
            translation_train, translation_dev, translation_test = dataset_loader.load_translation_data()
            parsing_train, parsing_dev, parsing_test = dataset_loader.load_parsing_data()
            logging.info("Dataset loading completed. Translation: train=%d, dev=%d, test=%d; Parsing: train=%d, dev=%d, test=%d",
                         len(translation_train), len(translation_dev), len(translation_test),
                         len(parsing_train), len(parsing_dev), len(parsing_test))
            
            logging.info("Instantiating Transformer model ...")
            transformer_model: TransformerModel = TransformerModel(self.config)
            logging.info("Transformer model instantiated.")

            # Set up Trainer with translation training data.
            logging.info("Initializing Trainer ...")
            trainer_instance: Trainer = Trainer(transformer_model, (translation_train, translation_dev, translation_test), self.config)
            logging.info("Starting training ...")
            trainer_instance.train()
            logging.info("Training completed at global step: %d", trainer_instance.global_step)

            # Set up Evaluation (using dev sets for both translation and parsing)
            logging.info("Initializing Evaluation ...")
            # Evaluation data: Use translation_dev for translation and parsing_dev for parsing.
            eval_data = (translation_dev, parsing_dev)
            evaluator: Evaluation = Evaluation(transformer_model, eval_data, self.config)
            logging.info("Running translation evaluation ...")
            translation_metrics = evaluator.evaluate_translation()
            logging.info("Translation Evaluation Metrics: %s", translation_metrics)
            logging.info("Running parsing evaluation ...")
            parsing_metrics = evaluator.evaluate_parsing()
            logging.info("Parsing Evaluation Metrics: %s", parsing_metrics)
            logging.info("Experiment run complete.")

        except Exception as error:
            logging.error("An error occurred during the experiment: %s", str(error))
            sys.exit(1)


def main() -> None:
    """
    Main function that loads the configuration, creates an App instance,
    and runs the experiment workflow.
    """
    try:
        config = load_config("config.yaml")
        logging.info("Configuration loaded successfully from 'config.yaml'.")
    except Exception as error:
        logging.error("Failed to load configuration: %s", str(error))
        sys.exit(1)

    app = App(config)
    app.run_experiment()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
    main()
