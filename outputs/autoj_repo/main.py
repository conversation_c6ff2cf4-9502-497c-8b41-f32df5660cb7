#!/usr/bin/env python3
"""
main.py

Entry point for reproducing the experiments from the paper "GENERATIVE JUDGE FOR EVALUATING ALIGNMENT."

This module orchestrates the entire experimental workflow:
  1. Loads configuration settings from config.py (config.yaml based).
  2. Uses DatasetLoader to ingest and preprocess training data for pairwise and single-response protocols.
  3. Initializes the ModelWrapper which loads the pre-trained LLaMA-2-13B-chat model and integrates DeepSpeed.
  4. Trains the model using Trainer, which runs the training loop (5 epochs / 675 update steps) while saving checkpoints.
  5. Loads evaluation datasets (for Eval-P, Eval-C, and Eval-R) and runs evaluation via Evaluator.
  6. Logs and prints out evaluation metrics (agreement rate, consistency, correlation coefficients, etc.).

All hyperparameters and paths are set via the configuration in config.yaml.
This file adheres strictly to the design and interfaces defined in the project.

Author: [Your Name]
Date: [Date]
"""

import os
import logging
import pprint

# Import configuration and modules from our project
from config import CONFIG
from dataset_loader import <PERSON><PERSON><PERSON>oader
from model import ModelWrapper
from trainer import Trainer
from evaluation import Evaluator
from utils import read_json, setup_logger, timestamp

# Setup a global logger for main
LOGGER = setup_logger("Main", log_file=None)


def load_evaluation_data() -> dict:
    """
    Load evaluation datasets for the three evaluation tasks: Eval-P, Eval-C, and Eval-R.
    This function attempts to load JSON files from the 'data' directory:
      - data/eval_pairwise.json
      - data/eval_single_response.json
      - data/eval_overall.json
    If the files do not exist, it uses dummy evaluation data for demonstration.

    Returns:
        dict: A dictionary with keys "pairwise", "single_response", "overall_rating".
    """
    eval_data = {}

    # Define file paths for evaluation datasets
    eval_pairwise_path = os.path.join("data", "eval_pairwise.json")
    eval_single_response_path = os.path.join("data", "eval_single_response.json")
    eval_overall_path = os.path.join("data", "eval_overall.json")

    # Load pairwise evaluation data
    if os.path.exists(eval_pairwise_path):
        try:
            eval_data["pairwise"] = read_json(eval_pairwise_path)
            LOGGER.info(f"Loaded Eval-P data from {eval_pairwise_path}.")
        except Exception as e:
            LOGGER.error(f"Error reading Eval-P data: {e}")
            eval_data["pairwise"] = []
    else:
        LOGGER.warning(f"Eval-P file not found at {eval_pairwise_path}. Using dummy sample.")
        eval_data["pairwise"] = [
            {
                "query": "What is the capital of France?",
                "response1": "The capital of France is Paris.",
                "response2": "Paris is the capital of France.",
                "label": "Response 1 Preferred"
            }
        ]

    # Load single-response evaluation data
    if os.path.exists(eval_single_response_path):
        try:
            eval_data["single_response"] = read_json(eval_single_response_path)
            LOGGER.info(f"Loaded Eval-C data from {eval_single_response_path}.")
        except Exception as e:
            LOGGER.error(f"Error reading Eval-C data: {e}")
            eval_data["single_response"] = []
    else:
        LOGGER.warning(f"Eval-C file not found at {eval_single_response_path}. Using dummy sample.")
        eval_data["single_response"] = [
            {
                "query": "Explain the theory of relativity.",
                "response": "The theory of relativity was developed by Einstein. Rating: [[8]]"
            }
        ]

    # Load overall rating evaluation data
    if os.path.exists(eval_overall_path):
        try:
            eval_data["overall_rating"] = read_json(eval_overall_path)
            LOGGER.info(f"Loaded Eval-R data from {eval_overall_path}.")
        except Exception as e:
            LOGGER.error(f"Error reading Eval-R data: {e}")
            eval_data["overall_rating"] = []
    else:
        LOGGER.warning(f"Eval-R file not found at {eval_overall_path}. Using dummy sample.")
        eval_data["overall_rating"] = [
            {
                "query": "Describe the process of photosynthesis.",
                "candidates": [
                    "Photosynthesis is the process by which plants convert sunlight into energy.",
                    "It is the method of converting sunlight into chemical energy."
                ],
                "gpt4_ratings": [9.0, 7.5]
            }
        ]

    return eval_data


def main() -> None:
    """
    Main function to orchestrate the reproducibility workflow.
    Steps:
      1. Load configuration.
      2. Load and preprocess training data using DatasetLoader.
      3. Initialize the ModelWrapper and load the pre-trained model.
      4. Train the model using Trainer.
      5. Load evaluation datasets.
      6. Evaluate the trained model using Evaluator.
      7. Log and print evaluation metrics.
    """
    LOGGER.info("Experiment started.")
    LOGGER.info("Loading configuration from config.py.")
    config = CONFIG  # Global configuration dictionary loaded via config.py

    # ----------------------- Data Loading -----------------------
    LOGGER.info("Initializing DatasetLoader and loading training data.")
    dataset_loader = DatasetLoader(config)
    pairwise_data, pairwise_original_count = dataset_loader.load_pairwise_data()
    single_response_data = dataset_loader.load_single_response_data()
    LOGGER.info(
        f"Pairwise training data: {len(pairwise_data)} samples (from {pairwise_original_count} original valid samples, augmented)."
    )
    LOGGER.info(f"Single-response training data: {len(single_response_data)} samples.")

    # Combine training data into a dictionary.
    train_data = {
        "pairwise": pairwise_data,
        "single_response": single_response_data
    }

    # ----------------------- Model Initialization -----------------------
    LOGGER.info("Initializing ModelWrapper with model configuration.")
    model_config = config.get("model", {})
    model_wrapper = ModelWrapper(model_config)
    LOGGER.info("ModelWrapper instantiated and model loaded successfully.")

    # ----------------------- Training -----------------------
    LOGGER.info("Initializing Trainer with training data and configuration.")
    trainer = Trainer(model_wrapper, train_data, config)
    LOGGER.info("Starting training process.")
    trainer.train()
    LOGGER.info("Training complete.")

    # ----------------------- Evaluation -----------------------
    LOGGER.info("Loading evaluation datasets for Eval-P, Eval-C, and Eval-R.")
    eval_data = load_evaluation_data()

    LOGGER.info("Initializing Evaluator with evaluation data and configuration.")
    evaluator = Evaluator(model_wrapper, eval_data, config)

    LOGGER.info("Evaluating pairwise response comparisons (Eval-P).")
    pairwise_metrics = evaluator.evaluate_pairwise()
    LOGGER.info("Pairwise Evaluation Metrics:")
    pprint.pprint(pairwise_metrics)

    LOGGER.info("Evaluating single-response critique generation (Eval-C).")
    single_response_metrics = evaluator.evaluate_single_response()
    LOGGER.info("Single-Response Evaluation Metrics:")
    pprint.pprint(single_response_metrics)

    LOGGER.info("Evaluating overall rating selection (Eval-R) using Best-of-N selection.")
    overall_rating_metrics = evaluator.evaluate_overall_rating()
    LOGGER.info("Overall Rating Evaluation Metrics:")
    pprint.pprint(overall_rating_metrics)

    LOGGER.info("Experiment finished successfully.")


if __name__ == "__main__":
    main()
