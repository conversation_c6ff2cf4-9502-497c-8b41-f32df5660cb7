#!/usr/bin/env python3
"""
model.py

This module implements the ModelWrapper class which wraps a pre-trained LLaMA-2-13B-chat model with advanced DeepSpeed
optimization features including ZeRO Stage 3, gradient checkpointing, and FlashAttention. It also configures the model for
mixed precision (BF16/TF32) training/inference.

The class provides:
    - __init__(model_config: dict): Initializes the ModelWrapper using the provided model configuration.
    - load_model() -> torch.nn.Module: Loads the pre-trained model and integrates it with DeepSpeed.
    - forward(input: Dict[str, Tensor]) -> Tensor: Performs a forward pass in mixed precision using torch.autocast.

Dependencies:
    - torch (for tensor computation and model operations)
    - transformers (for loading the pre-trained causal LM)
    - deepspeed (for distributed training and optimization)
    - config.py for accessing configuration parameters
    - logging for runtime logs

Author: [Your Name]
Date: [Date]
"""

import os
import logging
from typing import Any, Dict

import torch
from torch import Tensor
from transformers import AutoModelForCausalLM, AutoTokenizer

import deepspeed

# Import configuration from config.py
from config import CONFIG

# Set up module-level logger.
logger = logging.getLogger("ModelWrapper")
if not logger.hasHandlers():
    logger.setLevel(logging.INFO)
    stream_handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s",
                                  datefmt="%Y-%m-%d %H:%M:%S")
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)


class ModelWrapper:
    """
    ModelWrapper class wraps the pre-trained LLaMA-2-13B-chat model, integrates DeepSpeed for optimization
    (e.g. ZeRO Stage 3, gradient checkpointing, flash attention), and configures mixed precision (BF16/TF32).
    It provides the load_model() method to load and initialize the model, and forward() to process the unified inputs.
    """

    def __init__(self, model_config: Dict[str, Any]) -> None:
        """
        Initialize the ModelWrapper with the given model_config dictionary.

        Args:
            model_config (Dict[str, Any]): Dictionary containing model related configurations.
                                          Expected to include key "base_model", e.g., "LLaMA-2-13B-chat".
        """
        self.model_config: Dict[str, Any] = model_config
        self.base_model_name: str = self.model_config.get("base_model", "LLaMA-2-13B-chat")
        self.ds_engine: Any = None  # DeepSpeed engine wrapping the model will be stored here.
        self.model: torch.nn.Module = None  # The underlying Huggingface model.
        self.tokenizer = None  # Optional: tokenizer can be loaded if needed.

        # Load model and integrate with DeepSpeed.
        self.load_model()

    def load_model(self) -> torch.nn.Module:
        """
        Loads the pre-trained LLaMA-2-13B-chat model and integrates DeepSpeed optimizations.

        Steps:
            - Load the pre-trained model using AutoModelForCausalLM.
            - Optionally load the tokenizer (if needed for further processing).
            - Enable gradient checkpointing if configured.
            - Create the DeepSpeed configuration dictionary from CONFIG.
            - Initialize DeepSpeed to wrap the model, using ZeRO Stage 3, gradient checkpointing,
              and flash attention if enabled.
            - Set the model to the appropriate device (GPU).

        Returns:
            torch.nn.Module: The DeepSpeed engine wrapping the pre-trained model.
        """
        logger.info(f"Loading pre-trained model: {self.base_model_name}")
        try:
            # Load the pre-trained causal LM.
            self.model = AutoModelForCausalLM.from_pretrained(self.base_model_name, torch_dtype=torch.bfloat16)
            # Optionally, load the tokenizer (not strictly needed if tokenization is handled elsewhere)
            self.tokenizer = AutoTokenizer.from_pretrained(self.base_model_name, use_fast=False)

            # Enable gradient checkpointing if enabled in DeepSpeed configuration.
            ds_config_config: Dict[str, Any] = CONFIG.get("hardware", {}).get("deepspeed", {})
            if ds_config_config.get("gradient_checkpointing", False):
                logger.info("Enabling gradient checkpointing on the model.")
                self.model.gradient_checkpointing_enable()

            # Prepare a DeepSpeed configuration dictionary.
            # Here, we setup minimal DS config using parameters from CONFIG.
            ds_config: Dict[str, Any] = {
                "train_batch_size": CONFIG.get("training", {}).get("batch_size", 64),
                "gradient_accumulation_steps": 1,
                "fp16": {
                    # Although our config mixes BF16 and TF32, we opt for BF16 if available.
                    "enabled": False  # We use torch.autocast for mixed precision below.
                },
                "bf16": {
                    "enabled": True
                },
                "zero_optimization": {
                    "stage": ds_config_config.get("stage", 3)
                },
                "gradient_checkpointing": ds_config_config.get("gradient_checkpointing", False),
                # FlashAttention is assumed to be enabled via integration-specific settings.
                "activation_checkpointing": {
                    "partition_activations": False,
                    "contiguous_memory_optimization": True
                }
            }
            logger.info(f"DeepSpeed configuration: {ds_config}")

            # Initialize DeepSpeed: note that deepspeed.initialize returns (engine, optimizer, _, _)
            model_parameters = filter(lambda p: p.requires_grad, self.model.parameters())
            self.ds_engine, optimizer, _, _ = deepspeed.initialize(
                model=self.model,
                model_parameters=model_parameters,
                config=ds_config
            )
            logger.info("DeepSpeed engine created and model wrapped with DeepSpeed.")
            return self.ds_engine
        except Exception as e:
            logger.error(f"Failed to load and initialize the model: {e}")
            raise RuntimeError(f"Model loading failed: {e}")

    def forward(self, inputs: Dict[str, Any]) -> Any:
        """
        Forward pass through the model using the unified input.

        Args:
            inputs (Dict[str, Any]): Dictionary containing input tensors such as 'input_ids',
                                     'attention_mask', and optional others (e.g., token_type_ids).

        Returns:
            Any: Model outputs (e.g., logits) as returned by the DeepSpeed engine.
        """
        try:
            # Ensure that all input tensors are on the correct device.
            device = next(self.ds_engine.parameters()).device
            for key, value in inputs.items():
                if isinstance(value, torch.Tensor):
                    inputs[key] = value.to(device)

            # Use torch.autocast for mixed precision using BF16 as specified in the configuration.
            # TF32 is enabled by default on NVIDIA A100 GPUs when using torch.cuda.amp.autocast.
            with torch.autocast(device_type="cuda", dtype=torch.bfloat16):
                outputs = self.ds_engine(**inputs)

            return outputs
        except Exception as e:
            logger.error(f"Error during forward pass: {e}")
            raise RuntimeError(f"Forward pass failed: {e}")


# For testing purposes when running this file directly.
if __name__ == "__main__":
    # Define a dummy input dictionary matching the unified input format.
    dummy_input = {
        "input_ids": torch.randint(0, 1000, (1, 128), dtype=torch.long),  # Dummy tensor shape (batch, seq_len)
        "attention_mask": torch.ones((1, 128), dtype=torch.long)
    }

    # Create a dummy model configuration from CONFIG.
    dummy_model_config = CONFIG.get("model", {})

    # Instantiate the ModelWrapper.
    model_wrapper = ModelWrapper(dummy_model_config)
    logger.info("ModelWrapper instantiated successfully.")

    # Run a forward pass with the dummy input.
    try:
        output = model_wrapper.forward(dummy_input)
        logger.info("Forward pass completed successfully.")
        # For demonstration, print the type of output.
        print(f"Output type: {type(output)}")
    except Exception as error:
        logger.error(f"Forward pass failed: {error}")
