#!/usr/bin/env python3
"""
dataset_loader.py

This module implements the DatasetLoader class which is responsible for:
    - Ingesting raw query–response pairs from multiple public datasets.
    - Filtering and preprocessing the samples (e.g., language filtering, query truncation,
      multi-turn dialogue reduction).
    - Assigning scenario labels via an integrated (dummy) scenario classifier.
    - Standardizing each sample into a unified format for both pairwise and single-response
      evaluation protocols.
    - Augmenting pairwise samples by swapping responses to reduce positional bias.
    - Duplicating single-response samples to balance the training batches.

This design strictly adheres to the specified Data structures and interfaces.

Dependencies are managed via config.py (configuration) and utils.py (helper functions).
All default settings are set via configuration (e.g., config.yaml).

Author: [Your Name]
Date: [Date]
"""

import os
import logging
from typing import Any, Dict, List, Tuple

from config import CONFIG
from utils import (
    truncate_text,
    swap_responses,
    format_unified_input,
    read_json,
    setup_logger,
)

# Initialize logger for this module.
logger: logging.Logger = setup_logger("DatasetLoader", log_file=None)


class DatasetLoader:
    """
    The DatasetLoader class handles the ingestion, filtering, preprocessing, scenario classification,
    and augmentation of raw query–response pairs to generate training examples for both pairwise and
    single-response evaluation protocols.
    """

    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Initialize the DatasetLoader with a configuration dictionary.

        Args:
            config (Dict[str, Any]): Global configuration dictionary loaded from config.yaml.
        """
        self.config: Dict[str, Any] = config

        # Raw data file paths; use defaults if not provided in config.
        self.pairwise_data_path: str = self.config.get("data", {}).get(
            "pairwise_data_path", "data/pairwise.json"
        )
        self.single_response_data_path: str = self.config.get("data", {}).get(
            "single_response_data_path", "data/single_response.json"
        )
        # Maximum length allowed for query text; subtract 50 tokens as specified.
        self.max_query_length: int = self.config.get("training", {}).get(
            "max_sequence_length", 4096
        ) - 50

        # Target dataset sizes from config.
        self.pairwise_dataset_target: int = self.config.get("data", {}).get(
            "pairwise_dataset_size", 3436
        )
        self.single_response_dataset_target: int = self.config.get("data", {}).get(
            "single_response_dataset_size", 960
        )

    def _is_english(self, text: str) -> bool:
        """
        Basic check to filter out non-English samples.
        Computes the ratio of ASCII characters to the total length.
        If ratio is above 0.9, the text is assumed to be English.

        Args:
            text (str): String to be checked.

        Returns:
            bool: True if text is likely English, False otherwise.
        """
        if not text:
            return False
        ascii_count: int = sum(1 for c in text if ord(c) < 128)
        ratio: float = ascii_count / len(text)
        return ratio > 0.9

    def _predict_scenario(self, query: str) -> str:
        """
        Dummy scenario classifier function.
        In practice, this would call a trained scenario classifier.
        For now, it returns "default" for all queries.

        Args:
            query (str): The user query string.

        Returns:
            str: Predicted scenario label.
        """
        return "default"

    def _preprocess_query(self, query: str) -> str:
        """
        Preprocess the query by:
            - Keeping only the first turn in multi-turn dialogues.
            - Truncating the text if it exceeds the maximum allowed length.

        Args:
            query (str): The raw query text.

        Returns:
            str: The preprocessed query.
        """
        # Keep only the first turn if multiple turns (assume newline-separated).
        first_turn: str = query.split("\n")[0]
        # Truncate the text if needed using the helper function.
        truncated: str = truncate_text(first_turn, self.max_query_length)
        return truncated.strip()

    def _preprocess_sample_pairwise(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        Preprocess a raw pairwise sample by:
            - Verifying required keys: "query", "response1", "response2", "label".
            - Processing the query (truncation, filtering).
            - Standardizing responses.
            - Performing scenario classification.
            - Formatting the sample into a unified input format.

        Args:
            sample (Dict[str, Any]): Raw pairwise sample.

        Returns:
            Dict[str, Any]: Preprocessed sample in unified format with an extra scenario field.

        Raises:
            ValueError: If required keys are missing or sample fails filtering.
        """
        required_keys: List[str] = ["query", "response1", "response2", "label"]
        for key in required_keys:
            if key not in sample:
                logger.warning(f"Sample missing required key '{key}'. Skipping sample.")
                raise ValueError(f"Missing key '{key}' in sample.")

        # Preprocess query.
        query: str = self._preprocess_query(sample["query"])
        if not self._is_english(query):
            raise ValueError("Non-English sample detected.")

        # Standardize responses.
        response1: str = sample["response1"].strip()
        response2: str = sample["response2"].strip()

        # Create unified input using the utility function.
        unified_input: Dict[str, Any] = format_unified_input(
            {
                "query": query,
                "response1": response1,
                "response2": response2,
                "context": sample.get("context", ""),
            },
            protocol="pairwise",
        )

        # Attach predicted scenario label.
        unified_input["scenario"] = self._predict_scenario(query)

        # Normalize and assign label.
        label: str = sample["label"].strip().lower()
        if label == "response 1 preferred":
            unified_input["label"] = "Response 1 Preferred"
        elif label == "response 2 preferred":
            unified_input["label"] = "Response 2 Preferred"
        else:
            unified_input["label"] = "Tie"

        return unified_input

    def load_pairwise_data(self) -> Tuple[List[Dict[str, Any]], int]:
        """
        Load, preprocess, and augment pairwise training examples.

        Steps:
            1. Load raw data from the specified pairwise data file.
            2. For each sample, preprocess by filtering non-English text,
               truncating queries, and assigning scenario labels.
            3. Augment each valid sample by swapping responses using the utility function.
            4. Limit the total number of samples to the target dataset size if necessary.

        Returns:
            Tuple[List[Dict[str, Any]], int]:
                - A list of processed pairwise samples (original plus augmented).
                - The number of original valid samples before augmentation.
        """
        processed_samples: List[Dict[str, Any]] = []
        raw_samples: List[Dict[str, Any]] = []

        if os.path.exists(self.pairwise_data_path):
            try:
                raw_samples = read_json(self.pairwise_data_path)
            except Exception as e:
                logger.error(f"Error reading pairwise data file: {e}")
                raw_samples = []
        else:
            logger.warning(f"Pairwise data file not found at: {self.pairwise_data_path}")
            return ([], 0)

        original_valid_count: int = 0
        for sample in raw_samples:
            try:
                processed_sample = self._preprocess_sample_pairwise(sample)
                processed_samples.append(processed_sample)
                original_valid_count += 1
            except Exception as e:
                logger.warning(f"Skipping sample due to error: {e}")

        logger.info(f"Loaded {original_valid_count} valid pairwise samples before augmentation.")

        # Augment by adding a swapped version for each valid sample.
        augmented_samples: List[Dict[str, Any]] = []
        for sample in processed_samples:
            augmented_samples.append(sample)
            try:
                swapped_sample = swap_responses(sample)
                augmented_samples.append(swapped_sample)
            except Exception as e:
                logger.warning(f"Skipping augmentation for sample due to error: {e}")

        logger.info(f"After augmentation, total pairwise samples: {len(augmented_samples)}.")

        # Limit to the target dataset size if total samples exceed configured limit.
        if len(augmented_samples) > self.pairwise_dataset_target:
            augmented_samples = augmented_samples[: self.pairwise_dataset_target]
            logger.info(f"Limited pairwise samples to target size: {self.pairwise_dataset_target}.")

        return augmented_samples, original_valid_count

    def _preprocess_sample_single(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        Preprocess a raw single-response sample by:
            - Verifying required keys: "query", "response".
            - Processing the query (truncation, filtering).
            - Formatting the sample into a unified input format.

        Args:
            sample (Dict[str, Any]): Raw single-response sample.

        Returns:
            Dict[str, Any]: Preprocessed sample in unified format with an extra scenario field.

        Raises:
            ValueError: If required keys are missing or sample fails filtering.
        """
        required_keys: List[str] = ["query", "response"]
        for key in required_keys:
            if key not in sample:
                logger.warning(f"Single-response sample missing required key '{key}'. Skipping sample.")
                raise ValueError(f"Missing key '{key}' in single-response sample.")

        query: str = self._preprocess_query(sample["query"])
        if not self._is_english(query):
            raise ValueError("Non-English sample detected.")

        response: str = sample["response"].strip()
        unified_input: Dict[str, Any] = format_unified_input(
            {
                "query": query,
                "response": response,
                "context": sample.get("context", ""),
            },
            protocol="single-response",
        )
        unified_input["scenario"] = self._predict_scenario(query)
        return unified_input

    def load_single_response_data(self) -> List[Dict[str, Any]]:
        """
        Load and preprocess single-response training examples.

        Steps:
            1. Load raw data from the specified single-response data file.
            2. For each sample, preprocess by filtering non-English text,
               truncating queries, and assigning scenario labels.
            3. Duplicate the processed samples to balance training batches.
            4. Limit the total number of samples to the target dataset size if necessary.

        Returns:
            List[Dict[str, Any]]: A list of preprocessed (and duplicated) single-response samples.
        """
        processed_samples: List[Dict[str, Any]] = []
        raw_samples: List[Dict[str, Any]] = []

        if os.path.exists(self.single_response_data_path):
            try:
                raw_samples = read_json(self.single_response_data_path)
            except Exception as e:
                logger.error(f"Error reading single-response data file: {e}")
                raw_samples = []
        else:
            logger.warning(
                f"Single-response data file not found at: {self.single_response_data_path}"
            )
            return []

        valid_single_count: int = 0
        for sample in raw_samples:
            try:
                processed_sample = self._preprocess_sample_single(sample)
                processed_samples.append(processed_sample)
                valid_single_count += 1
            except Exception as e:
                logger.warning(f"Skipping single-response sample due to error: {e}")

        logger.info(f"Loaded {valid_single_count} valid single-response samples.")

        # Duplicate samples for balanced training.
        duplicated_samples: List[Dict[str, Any]] = processed_samples + processed_samples.copy()
        logger.info(f"After duplication, total single-response samples: {len(duplicated_samples)}.")

        # Limit to the target size if necessary.
        if len(duplicated_samples) > self.single_response_dataset_target:
            duplicated_samples = duplicated_samples[: self.single_response_dataset_target]
            logger.info(
                f"Limited single-response samples to target size: {self.single_response_dataset_target}."
            )

        return duplicated_samples


# For testing purposes when executing this file directly.
if __name__ == "__main__":
    import pprint

    # Instantiate the DatasetLoader using the global CONFIG from config.py.
    dataset_loader = DatasetLoader(CONFIG)
    pairwise_data, original_count = dataset_loader.load_pairwise_data()
    logger.info(
        f"Pairwise data loaded: {len(pairwise_data)} samples (from {original_count} original valid samples, augmented)."
    )
    single_response_data = dataset_loader.load_single_response_data()
    logger.info(f"Single-response data loaded: {len(single_response_data)} samples.")

    # Print a sample from each to verify formatting.
    if pairwise_data:
        pprint.pprint(pairwise_data[0])
    if single_response_data:
        pprint.pprint(single_response_data[0])
