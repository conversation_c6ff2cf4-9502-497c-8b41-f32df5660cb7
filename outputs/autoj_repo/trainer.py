#!/usr/bin/env python3
"""
trainer.py

This module implements the Trainer class which orchestrates the training loop for AUTO-J.
It integrates with ModelWrapper (from model.py), loads the combined training data (from DatasetLoader),
sets up the optimizer and learning rate scheduler per configuration (from config.yaml via config.py),
and performs the training loop over a total of update steps (675 by default), saving checkpoints
every checkpoint_interval steps (50 by default).

The Trainer handles data batching, tokenization, forward pass, loss computation (calculating loss only on the output tokens),
backward propagation, optimizer step, scheduler update, logging of metrics, and checkpoint saving.

Author: [Your Name]
Date: [Date]
"""

import os
import math
import logging
import time
from typing import Any, Dict, List, Optional

import torch
from torch.utils.data import DataLoader, Dataset
from torch.nn import CrossEntropyLoss
from torch.optim.lr_scheduler import LambdaLR

from config import CONFIG
from utils import setup_logger, timestamp
# No circular imports: We assume ModelWrapper is imported from model.py
from model import ModelWrapper

# Set up logger for the trainer module
logger: logging.Logger = setup_logger("Trainer", log_file=None)

# ------------------------- Custom Dataset Class -------------------------

class TrainingDataset(Dataset):
    """
    A custom dataset that wraps a list of training samples.
    Each sample is a dictionary in a unified format for either pairwise or single-response protocol.
    
    For pairwise samples, the dictionary contains keys: "query", "response1", "response2", "label", "scenario", "context".
    For single-response samples, the dictionary contains keys: "query", "response", "scenario", "context".
    
    The __getitem__ method transforms the sample into a unified text prompt string ready for tokenization.
    """
    def __init__(self, samples: List[Dict[str, Any]]) -> None:
        super().__init__()
        self.samples: List[Dict[str, Any]] = samples

    def __len__(self) -> int:
        return len(self.samples)

    def __getitem__(self, idx: int) -> Dict[str, str]:
        sample: Dict[str, Any] = self.samples[idx]
        # Build a unified training text based on the protocol.
        if "response" in sample:
            # Single-response protocol format.
            # Format: "Query: {query}\nResponse: {response}"
            training_text: str = f"Query: {sample['query'].strip()}\nResponse: {sample['response'].strip()}"
        elif "response1" in sample and "response2" in sample:
            # Pairwise protocol format.
            # Format: "Query: {query}\nResponse1: {response1}\nResponse2: {response2}"
            training_text = (f"Query: {sample['query'].strip()}\n"
                             f"Response1: {sample['response1'].strip()}\n"
                             f"Response2: {sample['response2'].strip()}")
        else:
            raise ValueError("Sample does not conform to recognized protocol format.")
        
        # Optionally append context if available.
        if sample.get("context", ""):
            training_text += "\nContext: " + sample["context"].strip()
        return {"text": training_text}

# ------------------------- Collate Function -------------------------

def collate_fn(batch: List[Dict[str, str]], tokenizer: Any, max_length: int) -> Dict[str, torch.Tensor]:
    """
    Collate function to collate a list of training samples into a batch.
    Each sample in the batch is a dictionary with key "text".
    
    The function tokenizes the batch of texts, pads them to max_length,
    and creates labels that have -100 for the prompt tokens (to compute loss only on the output tokens).
    
    It assumes that each text has a structure where the query part is followed by a marker "\nResponse:".
    The tokens before the first occurrence of "\nResponse:" are considered prompt tokens and are set to -100 in labels.
    
    Args:
        batch (List[Dict[str, str]]): List of samples where each sample is a dict with key "text".
        tokenizer: A tokenizer object with a 'batch_encode_plus' method.
        max_length (int): Maximum sequence length (e.g., 4096).
    
    Returns:
        Dict[str, torch.Tensor]: Batch dictionary containing:
            - input_ids: tensor of token ids.
            - attention_mask: tensor of attention masks.
            - labels: tensor for loss computation.
    """
    texts: List[str] = [sample["text"] for sample in batch]
    encoding = tokenizer.batch_encode_plus(
        texts,
        max_length=max_length,
        truncation=True,
        padding="longest",
        return_tensors="pt"
    )
    
    input_ids: torch.Tensor = encoding["input_ids"]
    attention_mask: torch.Tensor = encoding["attention_mask"]
    batch_size, seq_length = input_ids.shape

    # Create labels: copy of input_ids
    labels: torch.Tensor = input_ids.clone()

    # For each sample, find the boundary for the output tokens.
    for i, text in enumerate(texts):
        # Look for marker "\nResponse:" (works for both protocols)
        marker: str = "\nResponse:"
        marker_index: int = text.find(marker)
        if marker_index == -1:
            # If marker not found, set all tokens to be considered output (i.e., no prompt)
            prompt_token_count: int = 0
        else:
            # Tokenize the prompt part (everything before the marker)
            prompt_text: str = text[:marker_index]
            # Include the newline character before "Response:" if desired. Here we use prompt_text as is.
            prompt_encoding = tokenizer.encode(prompt_text, add_special_tokens=False)
            prompt_token_count = len(prompt_encoding)
        # Set tokens before prompt_token_count to -100 in labels.
        if prompt_token_count > 0:
            labels[i, :prompt_token_count] = -100

    return {
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "labels": labels,
    }

# ------------------------- Trainer Class -------------------------

class Trainer:
    """
    Trainer class orchestrates the training loop.
    
    Attributes:
        model_wrapper (ModelWrapper): The wrapped pre-trained model with DeepSpeed integration.
        train_data (Dict[str, List[Dict[str, Any]]]): Dictionary containing training examples.
            Expected keys: "pairwise" and "single_response", each being a list of samples.
        config (Dict[str, Any]): Configuration dictionary from config.yaml.
    """

    def __init__(
        self,
        model_wrapper: ModelWrapper,
        train_data: Dict[str, List[Dict[str, Any]]],
        config: Dict[str, Any]
    ) -> None:
        """
        Initialize the Trainer with model, training data, and configuration.
        
        Args:
            model_wrapper (ModelWrapper): Instance wrapping the pre-trained model.
            train_data (Dict[str, List[Dict[str, Any]]]): Training examples of both protocols.
            config (Dict[str, Any]): Configuration dictionary with hyperparameters and paths.
        """
        self.model_wrapper: ModelWrapper = model_wrapper
        self.config: Dict[str, Any] = config

        # Combine pairwise and single-response data if both provided.
        pairwise_data: List[Dict[str, Any]] = train_data.get("pairwise", [])
        single_response_data: List[Dict[str, Any]] = train_data.get("single_response", [])
        self.combined_data: List[Dict[str, Any]] = pairwise_data + single_response_data

        # Set training parameters from config.
        training_config: Dict[str, Any] = self.config.get("training", {})
        self.epochs: int = training_config.get("epochs", 5)
        self.total_update_steps: int = training_config.get("total_update_steps", 675)
        self.checkpoint_interval: int = training_config.get("checkpoint_interval", 50)
        self.batch_size: int = training_config.get("batch_size", 64)
        self.max_sequence_length: int = training_config.get("max_sequence_length", 4096)

        # Optimizer hyperparameters.
        lr_config: Dict[str, Any] = training_config.get("learning_rate", {})
        self.peak_lr: float = lr_config.get("peak", 1e-5)
        self.warmup_percentage: float = lr_config.get("warmup_percentage", 0.03)

        # Instantiate the loss function (CrossEntropyLoss with ignoring -100 indices).
        self.criterion = CrossEntropyLoss(ignore_index=-100)

        # Initialize the tokenizer from the model wrapper.
        if self.model_wrapper.tokenizer is None:
            raise ValueError("Tokenizer is not loaded in ModelWrapper.")
        self.tokenizer = self.model_wrapper.tokenizer

        # Create a DataLoader for the combined training data.
        self.train_dataset: TrainingDataset = TrainingDataset(self.combined_data)
        self.data_loader: DataLoader = DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            collate_fn=lambda batch: collate_fn(batch, self.tokenizer, self.max_sequence_length)
        )

        # Retrieve the DeepSpeed-wrapped optimizer from the model wrapper.
        self.optimizer = self.model_wrapper.ds_engine.optimizer

        # Set up the learning rate scheduler with warmup and cosine decay.
        self.scheduler = self._create_scheduler(total_steps=self.total_update_steps, optimizer=self.optimizer)

        # Determine checkpoint directory.
        self.checkpoint_dir: str = os.path.join("checkpoints", timestamp())
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        logger.info(f"Checkpoint directory set to: {self.checkpoint_dir}")

    def _create_scheduler(self, total_steps: int, optimizer: torch.optim.Optimizer) -> LambdaLR:
        """
        Create a learning rate scheduler with a warmup phase and cosine decay.
        
        Warmup steps = warmup_percentage * total_steps.
        
        Args:
            total_steps (int): Total number of update steps planned.
            optimizer (torch.optim.Optimizer): Optimizer whose learning rate will be scheduled.
        
        Returns:
            LambdaLR: The learning rate scheduler.
        """
        warmup_steps: int = max(1, int(total_steps * self.warmup_percentage))
        logger.info(f"Total steps: {total_steps}, Warmup steps: {warmup_steps}")

        def lr_lambda(current_step: int) -> float:
            if current_step < warmup_steps:
                return float(current_step) / float(max(1, warmup_steps))
            # Cosine decay after warmup.
            progress = float(current_step - warmup_steps) / float(max(1, total_steps - warmup_steps))
            return 0.5 * (1.0 + math.cos(math.pi * progress))

        scheduler: LambdaLR = LambdaLR(optimizer, lr_lambda=lr_lambda)
        return scheduler

    def _save_checkpoint(self, step: int) -> None:
        """
        Save a checkpoint using DeepSpeed's checkpointing functionality.
        
        Checkpoints include the model state, optimizer, and scheduler state.
        
        Args:
            step (int): Current update step number to be used in the checkpoint tag.
        """
        checkpoint_tag: str = f"step_{step}"
        # DeepSpeed's save_checkpoint saves the model state and other engine states.
        self.model_wrapper.ds_engine.save_checkpoint(self.checkpoint_dir, tag=checkpoint_tag)
        logger.info(f"Checkpoint saved at step {step} with tag '{checkpoint_tag}'.")

    def train(self) -> None:
        """
        Run the training loop over the provided training data.
        Iterates over epochs and batches until total update steps are reached.
        For each batch:
            - Tokenizes input.
            - Performs a forward pass using ModelWrapper.
            - Computes loss only on the output tokens (ignoring prompt tokens).
            - Backpropagates, performs an optimizer step via DeepSpeed, and updates the scheduler.
            - Logs metrics and saves checkpoints every checkpoint_interval steps.
        """
        logger.info("Starting training process.")
        global_step: int = 0
        epoch: int = 0
        training_loss_sum: float = 0.0
        num_batches: int = 0

        start_time = time.time()
        while global_step < self.total_update_steps and epoch < self.epochs:
            logger.info(f"Starting epoch {epoch+1}/{self.epochs}.")
            for batch in self.data_loader:
                if global_step >= self.total_update_steps:
                    break

                self.model_wrapper.ds_engine.train()
                # Forward pass
                try:
                    outputs = self.model_wrapper.forward(batch)
                except Exception as e:
                    logger.error(f"Error during forward pass at step {global_step}: {e}")
                    continue

                # Assume outputs contain "logits" of shape (batch, seq_len, vocab_size)
                logits: torch.Tensor = outputs.logits

                # Reshape logits and labels for loss computation.
                # Flatten the batch dimension and sequence length.
                loss = self.criterion(
                    logits.view(-1, logits.size(-1)),
                    batch["labels"].view(-1)
                )

                # Backpropagation using DeepSpeed
                self.model_wrapper.ds_engine.backward(loss)
                self.model_wrapper.ds_engine.step()

                # Update scheduler
                self.scheduler.step()

                # Logging metrics
                batch_loss: float = loss.item()
                training_loss_sum += batch_loss
                num_batches += 1
                global_step += 1

                if global_step % 10 == 0:
                    avg_loss = training_loss_sum / num_batches
                    current_lr = self.optimizer.param_groups[0]['lr']
                    logger.info(f"Step {global_step}/{self.total_update_steps} - Loss: {avg_loss:.4f} - LR: {current_lr:.8f}")

                # Save checkpoint at specified intervals
                if global_step % self.checkpoint_interval == 0:
                    self._save_checkpoint(global_step)

                # Clear gradients (DeepSpeed internally clears gradients after step)
            epoch += 1

        total_time = time.time() - start_time
        logger.info(f"Training completed in {total_time:.2f} seconds over {global_step} steps.")
