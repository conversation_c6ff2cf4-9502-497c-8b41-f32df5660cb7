#!/usr/bin/env python3
"""
evaluation.py

This module implements the Evaluator class which performs evaluation of AUTO-J on three tasks:
  - Pairwise Response Comparison (Eval-P)
  - Single-Response Critique Generation (Eval-C)
  - Overall Rating for Single Response (Eval-R)

The class uses ModelWrapper for inference (by calling its generate() function implemented here)
and applies unified prompt formats (Tabs 16 and 17) to process evaluation samples. It computes metrics
such as agreement rate, consistency rate, and correlation coefficients (<PERSON> and Spearman)
based on outputs compared to human or GPT-4 annotations.

All configuration values are obtained from config.py (loaded from config.yaml),
and helper functions from utils.py are used for data formatting, logging, and correlation computation.

Author: [Your Name]
Date: [Date]
"""

import os
import re
import logging
import torch
import numpy as np
from typing import Any, Dict, List, Optional, Tuple

from config import CONFIG
from utils import setup_logger, compute_correlation, timestamp, format_unified_input, swap_responses

# Set up logger for Evaluator
logger: logging.Logger = setup_logger("Evaluator", log_file=None)

# ------------------------- Helper Functions for Generation and Parsing -------------------------

def generate_text(model_wrapper: Any, prompt: str, max_length: int = 1024) -> str:
    """
    Generate text output from the model using a prompt.
    This function leverages the underlying Huggingface model's generate() method.
    
    Args:
        model_wrapper (Any): An instance of ModelWrapper which includes 'model' and 'tokenizer'.
        prompt (str): The input prompt string.
        max_length (int): Maximum length of the generated sequence. Default is 1024.
    
    Returns:
        str: The generated text decoded into a string.
    """
    try:
        # Tokenize prompt; use model_wrapper.tokenizer
        input_ids = model_wrapper.tokenizer.encode(prompt, return_tensors="pt")
        device = next(model_wrapper.model.parameters()).device
        input_ids = input_ids.to(device)
        
        # Generate text with default parameters (greedy decoding for simplicity)
        with torch.no_grad():
            # Use autocast for mixed precision if available.
            with torch.autocast(device_type="cuda", dtype=torch.bfloat16):
                generated_ids = model_wrapper.model.generate(
                    input_ids,
                    max_length=max_length,
                    do_sample=False,
                    num_return_sequences=1,
                    eos_token_id=model_wrapper.tokenizer.eos_token_id
                )
        # Decode generated tokens
        output_text = model_wrapper.tokenizer.decode(generated_ids[0], skip_special_tokens=True)
        return output_text
    except Exception as e:
        logger.error(f"Error during text generation: {e}")
        return ""

def extract_pairwise_decision(output_text: str) -> Optional[str]:
    """
    Extract the final decision from the model's output text for pairwise evaluation.
    The expected decision should contain one of the phrases: "Response 1 Preferred", "Response 2 Preferred", or "Tie".
    
    Args:
        output_text (str): Generated output text from the model.
    
    Returns:
        Optional[str]: Extracted decision (normalized to title case) if found; otherwise, None.
    """
    decision_patterns = [
        r"Response\s*1\s*Preferred",
        r"Response\s*2\s*Preferred",
        r"Tie"
    ]
    for pat in decision_patterns:
        match = re.search(pat, output_text, re.IGNORECASE)
        if match:
            # Normalize decision string: remove extra spaces and use title case.
            decision = match.group(0).strip().title()
            return decision
    return None

def invert_decision(decision: str) -> Optional[str]:
    """
    Invert the decision for a swapped pair sample.
    For example, if original decision is "Response 1 Preferred", inverted decision is "Response 2 Preferred".
    If decision is "Tie", then inverted decision remains "Tie".
    
    Args:
        decision (str): Original decision string.
    
    Returns:
        Optional[str]: Inverted decision string.
    """
    if decision.lower() == "response 1 preferred":
        return "Response 2 Preferred"
    elif decision.lower() == "response 2 preferred":
        return "Response 1 Preferred"
    elif decision.lower() == "tie":
        return "Tie"
    else:
        return None

def extract_rating_from_text(output_text: str) -> Optional[float]:
    """
    Extract the numeric rating from the model's output text for single-response evaluation.
    The expected format is a pattern like "Rating: [[<number>]]", e.g., "Rating: [[5]]".
    
    Args:
        output_text (str): Generated output text.
    
    Returns:
        Optional[float]: The extracted numeric rating if found, else None.
    """
    rating_pattern = r"Rating:\s*\[\[\s*(\d+(?:\.\d+)?)\s*\]\]"
    match = re.search(rating_pattern, output_text, re.IGNORECASE)
    if match:
        try:
            rating_value = float(match.group(1))
            return rating_value
        except ValueError:
            return None
    return None

# ------------------------- Evaluator Class -------------------------

class Evaluator:
    """
    The Evaluator class executes evaluation tasks as defined in the paper:
      - evaluate_pairwise(): For pairwise response comparisons (Eval-P)
      - evaluate_single_response(): For single-response critique generation (Eval-C)
      - evaluate_overall_rating(): For overall rating selection using Best-of-N (Eval-R)
    
    It requires a ModelWrapper instance, evaluation data (in a standardized format),
    and configuration parameters from config.yaml (via CONFIG).
    """
    def __init__(self, model_wrapper: Any, eval_data: Dict[str, Any], config: Dict[str, Any]) -> None:
        """
        Initialize the Evaluator.
        
        Args:
            model_wrapper (Any): An instance of ModelWrapper.
            eval_data (Dict[str, Any]): Dictionary containing evaluation datasets.
                Expected keys:
                    "pairwise": List of samples for pairwise evaluation.
                    "single_response": List of samples for single-response evaluation.
                    "overall_rating": List of samples for overall rating evaluation.
            config (Dict[str, Any]): Global configuration dictionary.
        """
        self.model_wrapper: Any = model_wrapper
        self.eval_data: Dict[str, Any] = eval_data
        self.config: Dict[str, Any] = config
        # Set generation max_length from configuration or default.
        self.gen_max_length: int = self.config.get("evaluation", {}).get("gen_max_length", 1024)
    
    def evaluate_pairwise(self) -> Dict[str, float]:
        """
        Evaluate pairwise response comparisons (Eval-P).
        
        Process:
          1. For each sample:
             - Use the unified input format (Tab. 16) to construct a prompt.
             - Generate output for the original sample and for the swapped sample.
             - Extract decisions from outputs.
             - Check consistency (in swapped order, decision should be the inverse).
             - If decisions are consistent, compare with human annotation.
          2. Compute:
             - Agreement rate: correct predictions / number of valid samples (with consistent outputs).
             - Consistency rate: number of samples with consistent predictions / total samples evaluated.
        
        Returns:
            Dict[str, float]: Dictionary with keys "agreement_rate" and "consistency_rate".
        """
        pairwise_samples: List[Dict[str, Any]] = self.eval_data.get("pairwise", [])
        total_samples: int = len(pairwise_samples)
        valid_count: int = 0
        correct_count: int = 0
        inconsistent_count: int = 0

        logger.info(f"Starting pairwise evaluation on {total_samples} samples.")
        for idx, sample in enumerate(pairwise_samples):
            try:
                # Construct prompt for original order.
                # Unified input format is assumed to be:
                # "Query: {query}\nResponse1: {response1}\nResponse2: {response2}"
                prompt_original = (f"Query: {sample['query'].strip()}\n"
                                   f"Response1: {sample['response1'].strip()}\n"
                                   f"Response2: {sample['response2'].strip()}")
                # Generate output for original order.
                output_original = generate_text(self.model_wrapper, prompt_original, self.gen_max_length)
                decision_original = extract_pairwise_decision(output_original)
                
                # Create swapped prompt using swap_responses utility.
                swapped_sample = swap_responses(sample)
                prompt_swapped = (f"Query: {swapped_sample['query'].strip()}\n"
                                  f"Response1: {swapped_sample['response1'].strip()}\n"
                                  f"Response2: {swapped_sample['response2'].strip()}")
                output_swapped = generate_text(self.model_wrapper, prompt_swapped, self.gen_max_length)
                decision_swapped = extract_pairwise_decision(output_swapped)
                
                # Invert the original decision to get expected swapped decision.
                if decision_original is None or decision_swapped is None:
                    logger.warning(f"Sample {idx}: Failed to extract decision from outputs; skipping sample.")
                    continue
                
                expected_swapped = invert_decision(decision_original)
                if expected_swapped is None:
                    logger.warning(f"Sample {idx}: Unexpected decision format; skipping sample.")
                    continue

                if decision_swapped == expected_swapped:
                    # Consistent sample.
                    valid_count += 1
                    human_label: str = sample.get("label", "").strip().title()
                    # For comparison, we take the original decision.
                    if decision_original == human_label:
                        correct_count += 1
                else:
                    inconsistent_count += 1
                    logger.info(f"Sample {idx}: Inconsistent outputs (Original: {decision_original}, Swapped: {decision_swapped}).")
            except Exception as e:
                logger.error(f"Error processing pairwise sample {idx}: {e}")
                continue

        if valid_count == 0:
            agreement_rate = 0.0
        else:
            agreement_rate = correct_count / valid_count * 100.0
        consistency_rate = valid_count / total_samples * 100.0 if total_samples > 0 else 0.0

        metrics: Dict[str, float] = {
            "agreement_rate": agreement_rate,
            "consistency_rate": consistency_rate,
            "inconsistent_count": float(inconsistent_count)
        }
        logger.info(f"Pairwise evaluation metrics: {metrics}")
        return metrics

    def evaluate_single_response(self) -> Dict[str, Any]:
        """
        Evaluate single-response critique generation (Eval-C).
        
        Process:
          1. For each sample in the single-response evaluation dataset:
             - Construct a prompt using the unified input format (Tab. 17), e.g.,
               "Query: {query}\nResponse: {response}".
             - Generate output from the model which should include a natural language critique and a final numeric rating.
             - Parse the output to extract the numeric rating using a pattern like "Rating: [[<numeric>]]".
          2. Collect ratings and compute statistics such as average rating.
             (Additional critique quality metrics can be computed if human/GPT-4 critique data is available.)
        
        Returns:
            Dict[str, Any]: Dictionary with metrics such as "average_rating" and "samples_processed" count.
        """
        single_samples: List[Dict[str, Any]] = self.eval_data.get("single_response", [])
        total_samples: int = len(single_samples)
        rating_list: List[float] = []
        processed_count: int = 0

        logger.info(f"Starting single-response evaluation on {total_samples} samples.")
        for idx, sample in enumerate(single_samples):
            try:
                prompt = f"Query: {sample['query'].strip()}\nResponse: {sample['response'].strip()}"
                output = generate_text(self.model_wrapper, prompt, self.gen_max_length)
                rating = extract_rating_from_text(output)
                if rating is not None:
                    rating_list.append(rating)
                    processed_count += 1
                else:
                    logger.warning(f"Sample {idx}: Rating not found in output.")
            except Exception as e:
                logger.error(f"Error processing single-response sample {idx}: {e}")
                continue

        average_rating: float = float(np.mean(rating_list)) if rating_list else 0.0
        metrics: Dict[str, Any] = {
            "average_rating": average_rating,
            "samples_processed": processed_count,
            "total_samples": total_samples
        }
        logger.info(f"Single-response evaluation metrics: {metrics}")
        return metrics

    def evaluate_overall_rating(self) -> Dict[str, Any]:
        """
        Evaluate overall rating for single response (Eval-R) using Best-of-N selection.
        
        Process:
          1. For each query in the overall rating evaluation dataset:
             - The sample should have:
                 "query": the user query.
                 "candidates": a list of candidate responses (e.g., 32 candidates).
                 "gpt4_ratings": a corresponding list of external GPT-4 ratings for each candidate.
             - For each candidate, construct a prompt using the unified input format:
               "Query: {query}\nResponse: {candidate}".
             - Generate output from the model to obtain a numeric rating.
             - Collect model ratings across all candidates.
             - Identify the candidate with the best (highest) model rating.
          2. Across all queries:
             - Compute Pearson and Spearman correlations between the model ratings (for all candidates) and the provided GPT-4 ratings.
             - Also, record the average GPT-4 rating of the best-selected responses.
        
        Returns:
            Dict[str, Any]: Dictionary with keys "pearson_correlation", "spearman_correlation", and "avg_gpt4_best_rating".
        """
        overall_samples: List[Dict[str, Any]] = self.eval_data.get("overall_rating", [])
        all_model_ratings: List[float] = []
        all_gpt4_ratings: List[float] = []
        best_gpt4_ratings: List[float] = []  # GPT-4 ratings for the candidate selected as best by model
        
        logger.info(f"Starting overall rating evaluation on {len(overall_samples)} queries.")
        for q_idx, sample in enumerate(overall_samples):
            try:
                query: str = sample["query"].strip()
                candidates: List[str] = sample["candidates"]
                gpt4_ratings: List[float] = sample["gpt4_ratings"]
                if len(candidates) != len(gpt4_ratings):
                    logger.warning(f"Query {q_idx}: Number of candidates and GPT-4 ratings do not match; skipping query.")
                    continue
                
                candidate_model_ratings: List[float] = []
                for c_idx, candidate in enumerate(candidates):
                    prompt = f"Query: {query}\nResponse: {candidate.strip()}"
                    output = generate_text(self.model_wrapper, prompt, self.gen_max_length)
                    rating = extract_rating_from_text(output)
                    if rating is not None:
                        candidate_model_ratings.append(rating)
                        all_model_ratings.append(rating)
                        all_gpt4_ratings.append(gpt4_ratings[c_idx])
                    else:
                        logger.warning(f"Query {q_idx}, Candidate {c_idx}: Rating not extracted; skipping candidate.")
                
                if candidate_model_ratings:
                    # Select index of max model rating.
                    best_index = np.argmax(candidate_model_ratings)
                    best_gpt4_ratings.append(gpt4_ratings[best_index])
            except Exception as e:
                logger.error(f"Error processing overall rating sample for query {q_idx}: {e}")
                continue
        
        pearson_corr = compute_correlation(all_model_ratings, all_gpt4_ratings, method="pearson") if all_model_ratings and all_gpt4_ratings else 0.0
        spearman_corr = compute_correlation(all_model_ratings, all_gpt4_ratings, method="spearman") if all_model_ratings and all_gpt4_ratings else 0.0
        avg_gpt4_best = float(np.mean(best_gpt4_ratings)) if best_gpt4_ratings else 0.0
        
        metrics: Dict[str, Any] = {
            "pearson_correlation": pearson_corr,
            "spearman_correlation": spearman_corr,
            "avg_gpt4_best_rating": avg_gpt4_best,
            "total_candidate_pairs": len(all_model_ratings)
        }
        logger.info(f"Overall rating evaluation metrics: {metrics}")
        return metrics

# ------------------------- Main Evaluation Block (for testing purposes) -------------------------

if __name__ == "__main__":
    import pprint
    # For demonstration/testing, load dummy evaluation data.
    # In practice, the evaluation data should be loaded from proper evaluation dataset files.

    # Create dummy evaluation data for pairwise evaluation.
    dummy_pairwise: List[Dict[str, Any]] = [
        {
            "query": "What is the capital of France?",
            "response1": "The capital of France is Paris.",
            "response2": "Paris is the capital of France.",
            "label": "Response 1 Preferred"
        },
        # Add more samples as necessary...
    ]

    # Create dummy evaluation data for single-response evaluation.
    dummy_single_response: List[Dict[str, Any]] = [
        {
            "query": "Explain the theory of relativity.",
            "response": "The theory of relativity encompasses two theories by Einstein: special and general relativity. Rating: [[8]]"
        },
        # Add more samples as necessary...
    ]

    # Create dummy evaluation data for overall rating evaluation.
    dummy_overall: List[Dict[str, Any]] = [
        {
            "query": "Describe the process of photosynthesis.",
            "candidates": [
                "Photosynthesis is the process by which green plants use sunlight to convert carbon dioxide and water into glucose.",
                "It is a method used by plants to make their food with sunlight."
            ],
            "gpt4_ratings": [9.0, 7.5]
        },
        # Add more queries as necessary...
    ]

    dummy_eval_data: Dict[str, Any] = {
        "pairwise": dummy_pairwise,
        "single_response": dummy_single_response,
        "overall_rating": dummy_overall
    }

    # We assume that a ModelWrapper instance is available.
    # For testing, we create a dummy instance using the configuration.
    from model import ModelWrapper
    dummy_model_config = CONFIG.get("model", {})
    try:
        model_wrapper = ModelWrapper(dummy_model_config)
    except Exception as e:
        logger.error(f"Failed to instantiate model: {e}")
        exit(1)

    # Instantiate Evaluator.
    evaluator = Evaluator(model_wrapper, dummy_eval_data, CONFIG)

    # Evaluate pairwise.
    pairwise_metrics = evaluator.evaluate_pairwise()
    pprint.pprint({"Pairwise Metrics": pairwise_metrics})

    # Evaluate single-response.
    single_metrics = evaluator.evaluate_single_response()
    pprint.pprint({"Single-Response Metrics": single_metrics})

    # Evaluate overall rating.
    overall_metrics = evaluator.evaluate_overall_rating()
    pprint.pprint({"Overall Rating Metrics": overall_metrics})
