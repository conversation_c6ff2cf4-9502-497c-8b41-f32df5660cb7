#!/usr/bin/env python3
"""
config.py

This module centralizes all hyperparameters, dataset sizes, hardware settings, model parameters,
and evaluation protocols that are essential for reproducing the experiments described in the paper
"GENERATIVE JUDGE FOR EVALUATING ALIGNMENT." The configuration values are defined as default values
here and can be optionally overridden by a YAML configuration file ("config.yaml").

It provides a global CONFIG dictionary accessible across modules such as dataset_loader.py,
model.py, trainer.py, and evaluation.py, ensuring consistency and reproducibility.

Author: [Your Name]
Date: [Date]
"""

import os
import yaml  # PyYAML is assumed to be available in the environment
from typing import Any, Dict

# Default configuration dictionary based on config.yaml provided and paper specifications.
_DEFAULT_CONFIG: Dict[str, Any] = {
    "training": {
        "epochs": 5,
        "total_update_steps": 675,
        "checkpoint_interval": 50,
        "batch_size": 64,
        "max_sequence_length": 4096,
        "learning_rate": {
            "peak": 1e-5,
            "warmup_percentage": 0.03,  # 3% warmup steps
            "schedule": "cosine_decay"
        },
        "optimizer": {
            "type": "AdamW",
            "beta1": 0.9,
            "beta2": 0.95,
            "weight_decay": 0.1
        }
    },
    "hardware": {
        "gpus": 8,
        "gpu_type": "NVIDIA A100",
        "precision": {
            "mix": ["BF16", "TF32"]
        },
        "deepspeed": {
            "stage": 3,
            "gradient_checkpointing": True,
            "flash_attention": True
        }
    },
    "model": {
        "base_model": "LLaMA-2-13B-chat"
    },
    "data": {
        "pairwise_dataset_size": 3436,
        "single_response_dataset_size": 960
    },
    "evaluation": {
        "eval_p": {
            "total_samples": 1392  # 58 scenarios x 24 samples per scenario
        },
        "eval_c": {
            "total_samples": 232   # 58 scenarios x 4 samples per scenario
        },
        "eval_r": {
            "num_queries": 116,     # 58 scenarios x 2 queries
            "candidates_per_query": 32
        }
    }
}


def merge_dict(default: Dict[str, Any], override: Dict[str, Any]) -> None:
    """
    Recursively merge the override dictionary into the default dictionary.
    This function updates the default dictionary in-place.
    
    Args:
        default: The base dictionary with default configuration.
        override: The dictionary with overriding values.
    """
    for key, value in override.items():
        if key in default and isinstance(default[key], dict) and isinstance(value, dict):
            merge_dict(default[key], value)
        else:
            default[key] = value


def load_config(filepath: str = "config.yaml") -> Dict[str, Any]:
    """
    Load the configuration from a YAML file and merge with the default configuration.
    
    Args:
        filepath: Path to the YAML configuration file. Defaults to "config.yaml".
    
    Returns:
        A dictionary containing the merged configuration values.
        If the file does not exist or cannot be parsed, the default configuration will be used.
    """
    config: Dict[str, Any] = _DEFAULT_CONFIG.copy()  # shallow copy of the default config
    if os.path.exists(filepath):
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                yaml_config = yaml.safe_load(f)
                if isinstance(yaml_config, dict):
                    merge_dict(config, yaml_config)
                else:
                    raise ValueError(f"Parsed YAML configuration is not a dictionary: {yaml_config}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing {filepath}: {e}") from e
        except Exception as exc:
            raise RuntimeError(f"Error loading configuration file {filepath}: {exc}") from exc
    return config


# Global CONFIG dictionary accessible across the project.
CONFIG: Dict[str, Any] = load_config()


if __name__ == "__main__":
    # For debugging and verifying configuration values, print the configuration.
    import pprint
    pprint.pprint(CONFIG)
