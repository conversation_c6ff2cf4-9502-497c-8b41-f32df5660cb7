#!/usr/bin/env python3
"""
utils.py

Utility functions and classes for the AUTO-J project. This module provides helper functions for:
    - Configuration parsing
    - Logging setup
    - Data preprocessing (text truncation and response augmentation)
    - Data formatting to unify inputs for pairwise and single-response evaluation protocols
    - File I/O operations (JSON and text file reading/writing)
    - Basic evaluation utilities such as correlation computation and timestamp generation

These functions are used by dataset_loader.py, trainer.py, and evaluation.py to support the overall experiment pipeline.
All default settings are derived from config.yaml via config.py.

Author: [Your Name]
Date: [Date]
"""

import os
import json
import logging
import datetime
from typing import Any, Dict, Optional, List, Union
import numpy as np
import yaml

# ------------------------- Configuration Parsing -------------------------

def parse_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """
    Parse the configuration file (in YAML format) from the specified path and return a configuration dictionary.
    
    Args:
        config_path (str): Path to the configuration file. Defaults to "config.yaml".
    
    Returns:
        Dict[str, Any]: A dictionary containing configuration parameters.
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found at: {config_path}")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
            if not isinstance(config_data, dict):
                raise ValueError("Parsed configuration is not a dictionary.")
            return config_data
    except yaml.YAMLError as e:
        raise ValueError(f"Error parsing configuration file: {e}") from e


# ------------------------- Logging Setup -------------------------

def setup_logger(logger_name: str, log_file: Optional[str] = None, level: int = logging.INFO) -> logging.Logger:
    """
    Set up a logger that logs messages both to the console and optionally to a file.
    
    Args:
        logger_name (str): Name of the logger.
        log_file (Optional[str]): If provided, logs will also be written to this file.
        level (int): Logging level (default is logging.INFO).
    
    Returns:
        logging.Logger: Configured logger instance.
    """
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)

    # Create formatter with timestamp, log level and message
    formatter = logging.Formatter(fmt="%(asctime)s - %(levelname)s - %(message)s",
                                  datefmt="%Y-%m-%d %H:%M:%S")

    # Clear any existing handlers
    if logger.hasHandlers():
        logger.handlers.clear()

    # Log to console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Optionally log to file
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    logger.info(f"Logger '{logger_name}' initialized. Logging to console" + (f" and file {log_file}" if log_file else ""))
    return logger


# ------------------------- Data Preprocessing Utilities -------------------------

def truncate_text(text: str, max_length: int, head: Optional[int] = None, tail: Optional[int] = None) -> str:
    """
    Truncate the input text to ensure its length does not exceed max_length. If truncation
    is needed, keep the first 'head' characters and the last 'tail' characters, inserting
    an ellipsis ("...") in between.
    
    By default, if head and tail are not provided, they are set to half of (max_length - 3).
    
    Args:
        text (str): Input text string.
        max_length (int): Maximum allowed length for the text.
        head (Optional[int]): Number of characters to keep at the beginning.
        tail (Optional[int]): Number of characters to keep at the end.
    
    Returns:
        str: Possibly truncated text with an ellipsis in the middle if truncation occurred.
    """
    if len(text) <= max_length:
        return text

    # If head or tail not explicitly provided, split the available space evenly.
    if head is None or tail is None:
        available = max_length - 3  # 3 characters reserved for '...'
        head = available // 2
        tail = available - head

    truncated = text[:head] + "..." + text[-tail:]
    return truncated


def swap_responses(sample: Dict[str, Any]) -> Dict[str, Any]:
    """
    Augment a pairwise sample by swapping the roles of 'response1' and 'response2' and adjusting the label accordingly.
    
    The input sample dictionary is expected to contain at least:
        - "response1": The first response string.
        - "response2": The second response string.
        - "label": The original label which can be "Response 1 Preferred", "Response 2 Preferred", or "Tie".
    
    This function returns a new augmented dictionary with swapped responses and an updated label.
    
    Args:
        sample (Dict[str, Any]): A dictionary representing a pairwise training sample.
    
    Returns:
        Dict[str, Any]: New sample dictionary with swapped responses.
    """
    # Ensure required keys exist
    if not all(k in sample for k in ["response1", "response2", "label"]):
        raise ValueError("Input sample must contain 'response1', 'response2', and 'label' keys.")
    
    swapped_sample = sample.copy()
    # Swap response strings
    swapped_sample["response1"], swapped_sample["response2"] = sample["response2"], sample["response1"]
    
    # Adjust label accordingly
    original_label = sample["label"].strip().lower()
    if original_label == "response 1 preferred":
        swapped_sample["label"] = "Response 2 Preferred"
    elif original_label == "response 2 preferred":
        swapped_sample["label"] = "Response 1 Preferred"
    else:
        swapped_sample["label"] = "Tie"
    
    return swapped_sample


def format_unified_input(input_data: Dict[str, Any], protocol: str) -> Dict[str, Any]:
    """
    Format a raw sample input into the unified input format based on the specified protocol.
    
    The 'protocol' parameter should be either "pairwise" or "single-response".
    
    For pairwise:
        The unified format (according to Tab. 16) might include fields such as:
            - "query": the user query.
            - "response1": first response.
            - "response2": second response.
            - "context": (optionally) extra text explaining conditions, etc.
    
    For single-response:
        The unified format (according to Tab. 17) might include:
            - "query": the user query.
            - "response": the single response.
            - "context": (optionally) any additional information.
    
    Note: Scenario criteria are implicitly learned and thus are not explicitly inserted into the input.
    
    Args:
        input_data (Dict[str, Any]): Raw sample input data.
        protocol (str): Either "pairwise" or "single-response".
    
    Returns:
        Dict[str, Any]: Formatted dictionary adhering to unified input style.
    
    Raises:
        ValueError: If protocol is not recognized.
    """
    formatted_input: Dict[str, Any] = {}
    if protocol == "pairwise":
        # Expected raw keys: "query", "response1", "response2"
        required_keys = ["query", "response1", "response2"]
        for key in required_keys:
            if key not in input_data:
                raise ValueError(f"Missing key '{key}' in input_data for pairwise protocol.")
        formatted_input["query"] = input_data["query"]
        formatted_input["response1"] = input_data["response1"]
        formatted_input["response2"] = input_data["response2"]
        # Optionally, add unified context if available (omitted scenario criteria for context distillation)
        formatted_input["context"] = input_data.get("context", "")
    elif protocol == "single-response":
        # Expected raw keys: "query", "response"
        required_keys = ["query", "response"]
        for key in required_keys:
            if key not in input_data:
                raise ValueError(f"Missing key '{key}' in input_data for single-response protocol.")
        formatted_input["query"] = input_data["query"]
        formatted_input["response"] = input_data["response"]
        # Optionally, add context, such as previous critiques if available.
        formatted_input["context"] = input_data.get("context", "")
    else:
        raise ValueError(f"Unknown protocol: {protocol}. Expected 'pairwise' or 'single-response'.")
    
    return formatted_input


# ------------------------- File I/O Operations -------------------------

def read_json(file_path: str) -> Any:
    """
    Read and parse a JSON file from the specified file path.
    
    Args:
        file_path (str): Path to the JSON file.
    
    Returns:
        Any: Parsed JSON data.
    
    Raises:
        FileNotFoundError: If the file does not exist.
        json.JSONDecodeError: If the file content is not valid JSON.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"JSON file not found at: {file_path}")
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


def write_json(data: Any, file_path: str) -> None:
    """
    Write the provided data to a JSON file at the specified file path.
    
    Args:
        data (Any): Data to be written to the file.
        file_path (str): Path to the output JSON file.
    
    Returns:
        None
    """
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=4, ensure_ascii=False)


def load_text_file(file_path: str) -> str:
    """
    Read a text file and return its content as a string.
    
    Args:
        file_path (str): Path to the text file.
    
    Returns:
        str: Contents of the file.
    
    Raises:
        FileNotFoundError: If the file does not exist.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Text file not found at: {file_path}")
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


# ------------------------- Miscellaneous Functions -------------------------

def _rankdata(a: np.ndarray) -> np.ndarray:
    """
    Compute the ranks of the array elements, with average ranks for ties.
    
    Args:
        a (np.ndarray): Input array.
    
    Returns:
        np.ndarray: Array of ranks with the same shape as input.
    """
    temp = a.argsort()
    ranks = np.empty_like(temp, dtype=float)
    ranks[temp] = np.arange(1, len(a) + 1).astype(np.float64)

    # Handle ties by assigning the average rank for tied values.
    # Iterate over unique elements and adjust ranks.
    unique_vals = np.unique(a)
    for value in unique_vals:
        indices = np.where(a == value)[0]
        if indices.size > 1:
            avg_rank = np.mean(ranks[indices])
            ranks[indices] = avg_rank
    return ranks


def compute_correlation(metric1: List[Union[int, float]], 
                        metric2: List[Union[int, float]], 
                        method: str = "pearson") -> float:
    """
    Compute correlation coefficient between two lists of numerical metrics.
    Supports 'pearson' and 'spearman' methods.
    
    Args:
        metric1 (List[Union[int, float]]): First list of metric values.
        metric2 (List[Union[int, float]]): Second list of metric values.
        method (str): Correlation method. 'pearson' or 'spearman'. Defaults to "pearson".
    
    Returns:
        float: Correlation coefficient.
    
    Raises:
        ValueError: If the chosen method is not supported.
    """
    if len(metric1) != len(metric2):
        raise ValueError("Both metric lists must have the same length.")
    arr1 = np.array(metric1, dtype=np.float64)
    arr2 = np.array(metric2, dtype=np.float64)
    
    if method.lower() == "pearson":
        if arr1.std() == 0 or arr2.std() == 0:
            return 0.0
        corr = np.corrcoef(arr1, arr2)[0, 1]
        return float(corr)
    elif method.lower() == "spearman":
        ranks1 = _rankdata(arr1)
        ranks2 = _rankdata(arr2)
        if ranks1.std() == 0 or ranks2.std() == 0:
            return 0.0
        corr = np.corrcoef(ranks1, ranks2)[0, 1]
        return float(corr)
    else:
        raise ValueError(f"Unsupported correlation method: {method}")


def timestamp() -> str:
    """
    Generate a string timestamp in the format YYYYMMDD_HHMMSS.
    
    Returns:
        str: Current timestamp as a string.
    """
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
