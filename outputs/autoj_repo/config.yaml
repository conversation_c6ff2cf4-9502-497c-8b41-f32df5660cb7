## config.yaml
training:
  epochs: 5
  total_update_steps: 675
  checkpoint_interval: 50
  batch_size: 64
  max_sequence_length: 4096
  learning_rate:
    peak: 1e-5
    warmup_percentage: 0.03  # 3% warmup steps
    schedule: cosine_decay
  optimizer:
    type: AdamW
    beta1: 0.9
    beta2: 0.95
    weight_decay: 0.1
hardware:
  gpus: 8
  gpu_type: NVIDIA A100
  precision:
    mix: [BF16, TF32]
  deepspeed:
    stage: 3
    gradient_checkpointing: true
    flash_attention: true
model:
  base_model: "LLaMA-2-13B-chat"
data:
  pairwise_dataset_size: 3436
  single_response_dataset_size: 960
evaluation:
  eval_p:
    total_samples: 1392  # 58 scenarios x 24 per scenario
  eval_c:
    total_samples: 232   # 58 scenarios x 4 per scenario
  eval_r:
    num_queries: 116     # 58 scenarios x 2 queries
    candidates_per_query: 32