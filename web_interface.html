<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paper2Code 流式API 测试界面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        #stream-output {
            background: #1e1e1e;
            color: #ffffff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .stage-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .stage {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .stage.pending {
            background: #e9ecef;
            color: #6c757d;
        }
        .stage.active {
            background: #007bff;
            color: white;
        }
        .stage.completed {
            background: #28a745;
            color: white;
        }
        .stage.error {
            background: #dc3545;
            color: white;
        }
        .status-line {
            margin: 2px 0;
        }
        .status-line.status {
            color: #17a2b8;
        }
        .status-line.output {
            color: #ffffff;
        }
        .status-line.error {
            color: #dc3545;
            font-weight: bold;
        }
        .status-line.complete {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Paper2Code 流式API 测试界面</h1>
        
        <!-- 健康检查 -->
        <div class="section">
            <h2>🔍 服务状态检查</h2>
            <button onclick="checkHealth()">检查服务状态</button>
            <div id="health-status"></div>
        </div>
        
        <!-- 文件列表 -->
        <div class="section">
            <h2>📋 文件管理</h2>
            <button onclick="listFiles()">刷新文件列表</button>
            <div id="file-lists"></div>
        </div>
        
        <!-- 复制示例文件 -->
        <div class="section">
            <h2>📂 复制示例文件</h2>
            <div class="form-group">
                <label for="example-file">选择示例文件:</label>
                <select id="example-file">
                    <option value="">请先刷新文件列表</option>
                </select>
            </div>
            <button onclick="copyExample()">复制到data目录</button>
            <div id="copy-result"></div>
        </div>
        
        <!-- 流式处理论文 -->
        <div class="section">
            <h2>🎯 流式处理论文</h2>
            <div class="form-group">
                <label for="paper-file">选择PDF文件:</label>
                <select id="paper-file">
                    <option value="">请先刷新文件列表</option>
                </select>
            </div>
            <div class="form-group">
                <label for="paper-name">论文名称 (可选):</label>
                <input type="text" id="paper-name" placeholder="留空则使用文件名">
            </div>
            <div class="form-group">
                <label for="gpt-version">GPT版本:</label>
                <select id="gpt-version">
                    <option value="openai/o3-mini">openai/o3-mini</option>
                    <option value="openai/gpt-4">openai/gpt-4</option>
                    <option value="openai/gpt-3.5-turbo">openai/gpt-3.5-turbo</option>
                </select>
            </div>
            <button onclick="processPaperStream()" id="process-btn">开始流式处理</button>
            <button onclick="stopProcessing()" id="stop-btn" style="display: none; background-color: #dc3545;">停止处理</button>
            
            <!-- 进度指示器 -->
            <div class="stage-indicator" id="stage-indicator" style="display: none;">
                <div class="stage pending" id="stage-init">初始化</div>
                <div class="stage pending" id="stage-pdf-conversion">PDF转JSON</div>
                <div class="stage pending" id="stage-preprocess">预处理</div>
                <div class="stage pending" id="stage-planning">规划</div>
                <div class="stage pending" id="stage-config">配置</div>
                <div class="stage pending" id="stage-analyzing">分析</div>
                <div class="stage pending" id="stage-coding">编码</div>
            </div>
            
            <div id="process-result"></div>
        </div>
        
        <!-- 流式输出日志 -->
        <div class="section">
            <h2>📝 实时处理日志</h2>
            <button onclick="clearStreamOutput()">清空日志</button>
            <div id="stream-output"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let eventSource = null;
        
        function log(message, type = 'info') {
            const output = document.getElementById('stream-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            const line = document.createElement('div');
            line.className = `status-line ${type}`;
            line.textContent = logEntry;
            output.appendChild(line);
            output.scrollTop = output.scrollHeight;
        }
        
        function showMessage(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function clearStreamOutput() {
            document.getElementById('stream-output').innerHTML = '';
        }
        
        function updateStage(stageName, status) {
            const stageMap = {
                'init': 'stage-init',
                'file_copy': 'stage-init',
                'validation': 'stage-init',
                'pdf_conversion': 'stage-pdf-conversion',
                'PDF转JSON': 'stage-pdf-conversion',
                '预处理': 'stage-preprocess',
                '规划阶段': 'stage-planning',
                '配置提取': 'stage-config',
                'config_copy': 'stage-config',
                '分析阶段': 'stage-analyzing',
                '编码阶段': 'stage-coding'
            };
            
            const elementId = stageMap[stageName];
            if (elementId) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.className = `stage ${status}`;
                }
            }
        }
        
        async function checkHealth() {
            try {
                log('检查服务健康状态...', 'status');
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    const data = await response.json();
                    showMessage('health-status', `✅ 服务正常: ${data.status}`, 'success');
                    log('✅ 服务健康检查通过', 'status');
                } else {
                    showMessage('health-status', `❌ 服务异常: ${response.status}`, 'error');
                    log(`❌ 服务健康检查失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showMessage('health-status', `❌ 无法连接到服务: ${error.message}`, 'error');
                log(`❌ 健康检查出错: ${error.message}`, 'error');
            }
        }
        
        async function listFiles() {
            try {
                log('获取文件列表...', 'status');
                const response = await fetch(`${API_BASE}/list_papers`);
                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新文件列表显示
                    let html = '<h3>📁 Data目录文件:</h3>';
                    html += `<div class="file-list">`;
                    html += `<strong>PDF文件:</strong> ${data.data_pdf_files.join(', ') || '无'}<br>`;
                    html += `<strong>JSON文件:</strong> ${data.data_json_files.join(', ') || '无'}`;
                    html += `</div>`;
                    
                    html += '<h3>📂 Examples目录文件:</h3>';
                    html += `<div class="file-list">`;
                    html += `<strong>PDF文件:</strong> ${data.examples_pdf_files.join(', ') || '无'}<br>`;
                    html += `<strong>JSON文件:</strong> ${data.examples_json_files.join(', ') || '无'}`;
                    html += `</div>`;
                    
                    document.getElementById('file-lists').innerHTML = html;
                    
                    // 更新下拉选择框
                    updateSelectOptions('example-file', data.examples_pdf_files);
                    updateSelectOptions('paper-file', data.data_pdf_files);
                    
                    log('✅ 文件列表获取成功', 'status');
                } else {
                    log(`❌ 获取文件列表失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 获取文件列表出错: ${error.message}`, 'error');
            }
        }
        
        function updateSelectOptions(selectId, options) {
            const select = document.getElementById(selectId);
            select.innerHTML = '<option value="">请选择文件</option>';
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }
        
        async function copyExample() {
            const filename = document.getElementById('example-file').value;
            if (!filename) {
                showMessage('copy-result', '请选择要复制的文件', 'error');
                return;
            }
            
            try {
                log(`复制示例文件: ${filename}`, 'status');
                const response = await fetch(`${API_BASE}/copy_example_to_data?filename=${encodeURIComponent(filename)}`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showMessage('copy-result', `✅ ${data.message}`, 'success');
                        log(`✅ 文件复制成功: ${data.copied_files.join(', ')}`, 'status');
                        // 刷新文件列表
                        listFiles();
                    } else {
                        showMessage('copy-result', `❌ ${data.message}`, 'error');
                        log(`❌ 文件复制失败: ${data.message}`, 'error');
                    }
                } else {
                    const errorData = await response.json();
                    showMessage('copy-result', `❌ 复制失败: ${errorData.detail}`, 'error');
                    log(`❌ 复制请求失败: ${errorData.detail}`, 'error');
                }
            } catch (error) {
                showMessage('copy-result', `❌ 复制出错: ${error.message}`, 'error');
                log(`❌ 复制文件出错: ${error.message}`, 'error');
            }
        }
        
        function stopProcessing() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                document.getElementById('process-btn').disabled = false;
                document.getElementById('process-btn').textContent = '开始流式处理';
                document.getElementById('stop-btn').style.display = 'none';
                log('❌ 用户停止了处理过程', 'error');
            }
        }
        
        async function processPaperStream() {
            const filename = document.getElementById('paper-file').value;
            const paperName = document.getElementById('paper-name').value;
            const gptVersion = document.getElementById('gpt-version').value;
            
            if (!filename) {
                showMessage('process-result', '请选择要处理的PDF文件', 'error');
                return;
            }
            
            const processBtn = document.getElementById('process-btn');
            const stopBtn = document.getElementById('stop-btn');
            const stageIndicator = document.getElementById('stage-indicator');
            
            try {
                processBtn.disabled = true;
                processBtn.textContent = '处理中...';
                stopBtn.style.display = 'inline-block';
                stageIndicator.style.display = 'flex';
                
                // 重置所有阶段状态
                document.querySelectorAll('.stage').forEach(stage => {
                    stage.className = 'stage pending';
                });
                
                clearStreamOutput();
                log(`🚀 开始流式处理论文: ${filename}`, 'status');
                showMessage('process-result', '🚀 正在流式处理论文，请查看实时日志...', 'info');
                
                const requestData = {
                    filename: filename,
                    gpt_version: gptVersion
                };
                
                if (paperName) {
                    requestData.paper_name = paperName;
                }
                
                // 发送POST请求并处理流式响应
                const response = await fetch(`${API_BASE}/process_paper_stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                switch(data.type) {
                                    case 'status':
                                        log(`📊 [${data.stage}] ${data.message}`, 'status');
                                        updateStage(data.stage, 'active');
                                        break;
                                    case 'output':
                                        log(`📝 [${data.stage}] ${data.message}`, 'output');
                                        break;
                                    case 'error':
                                        log(`❌ [${data.stage}] ${data.message}`, 'error');
                                        updateStage(data.stage, 'error');
                                        break;
                                    case 'complete':
                                        log(`✅ ${data.message}`, 'complete');
                                        updateStage(data.stage, 'completed');
                                        showMessage('process-result', 
                                            `✅ ${data.message}<br>输出目录: ${data.data.output_dir}<br>代码仓库: ${data.data.output_repo_dir}`, 
                                            'success');
                                        // 标记所有阶段为完成
                                        document.querySelectorAll('.stage').forEach(stage => {
                                            if (!stage.classList.contains('error')) {
                                                stage.className = 'stage completed';
                                            }
                                        });
                                        break;
                                }
                            } catch (e) {
                                log(`❌ 解析消息失败: ${line}`, 'error');
                            }
                        }
                    }
                }
                
            } catch (error) {
                showMessage('process-result', `❌ 处理出错: ${error.message}`, 'error');
                log(`❌ 论文处理出错: ${error.message}`, 'error');
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = '开始流式处理';
                stopBtn.style.display = 'none';
            }
        }
        
        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkHealth();
            listFiles();
        };
    </script>
</body>
</html>
