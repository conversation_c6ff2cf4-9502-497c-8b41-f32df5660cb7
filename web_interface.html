<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paper2Code API 测试界面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .file-list {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        .file-item {
            padding: 5px;
            margin: 2px 0;
            background: #f8f9fa;
            border-radius: 3px;
            cursor: pointer;
        }
        .file-item:hover {
            background: #e9ecef;
        }
        .file-item.selected {
            background: #007bff;
            color: white;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        #output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Paper2Code API 测试界面</h1>
        
        <!-- 健康检查 -->
        <div class="section">
            <h2>🔍 服务状态检查</h2>
            <button onclick="checkHealth()">检查服务状态</button>
            <div id="health-status"></div>
        </div>
        
        <!-- 文件列表 -->
        <div class="section">
            <h2>📋 文件管理</h2>
            <button onclick="listFiles()">刷新文件列表</button>
            <div id="file-lists"></div>
        </div>
        
        <!-- 复制示例文件 -->
        <div class="section">
            <h2>📂 复制示例文件</h2>
            <div class="form-group">
                <label for="example-file">选择示例文件:</label>
                <select id="example-file">
                    <option value="">请先刷新文件列表</option>
                </select>
            </div>
            <button onclick="copyExample()">复制到data目录</button>
            <div id="copy-result"></div>
        </div>
        
        <!-- 处理论文 -->
        <div class="section">
            <h2>🎯 处理论文</h2>
            <div class="form-group">
                <label for="paper-file">选择PDF文件:</label>
                <select id="paper-file">
                    <option value="">请先刷新文件列表</option>
                </select>
            </div>
            <div class="form-group">
                <label for="paper-name">论文名称 (可选):</label>
                <input type="text" id="paper-name" placeholder="留空则使用文件名">
            </div>
            <div class="form-group">
                <label for="gpt-version">GPT版本:</label>
                <select id="gpt-version">
                    <option value="openai/o3-mini">openai/o3-mini</option>
                    <option value="openai/gpt-4">openai/gpt-4</option>
                    <option value="openai/gpt-3.5-turbo">openai/gpt-3.5-turbo</option>
                </select>
            </div>
            <button onclick="processPaper()" id="process-btn">开始处理</button>
            <div class="progress" id="progress" style="display: none;">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            <div id="process-result"></div>
        </div>
        
        <!-- 输出日志 -->
        <div class="section">
            <h2>📝 操作日志</h2>
            <button onclick="clearOutput()">清空日志</button>
            <div id="output"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            output.textContent += logEntry;
            output.scrollTop = output.scrollHeight;
        }
        
        function showMessage(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        async function checkHealth() {
            try {
                log('检查服务健康状态...');
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    const data = await response.json();
                    showMessage('health-status', `✅ 服务正常: ${data.status}`, 'success');
                    log('✅ 服务健康检查通过');
                } else {
                    showMessage('health-status', `❌ 服务异常: ${response.status}`, 'error');
                    log(`❌ 服务健康检查失败: ${response.status}`);
                }
            } catch (error) {
                showMessage('health-status', `❌ 无法连接到服务: ${error.message}`, 'error');
                log(`❌ 健康检查出错: ${error.message}`);
            }
        }
        
        async function listFiles() {
            try {
                log('获取文件列表...');
                const response = await fetch(`${API_BASE}/list_papers`);
                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新文件列表显示
                    let html = '<h3>📁 Data目录文件:</h3>';
                    html += `<div class="file-list">`;
                    html += `<strong>PDF文件:</strong> ${data.data_pdf_files.join(', ') || '无'}<br>`;
                    html += `<strong>JSON文件:</strong> ${data.data_json_files.join(', ') || '无'}`;
                    html += `</div>`;
                    
                    html += '<h3>📂 Examples目录文件:</h3>';
                    html += `<div class="file-list">`;
                    html += `<strong>PDF文件:</strong> ${data.examples_pdf_files.join(', ') || '无'}<br>`;
                    html += `<strong>JSON文件:</strong> ${data.examples_json_files.join(', ') || '无'}`;
                    html += `</div>`;
                    
                    document.getElementById('file-lists').innerHTML = html;
                    
                    // 更新下拉选择框
                    updateSelectOptions('example-file', data.examples_pdf_files);
                    updateSelectOptions('paper-file', data.data_pdf_files);
                    
                    log('✅ 文件列表获取成功');
                } else {
                    log(`❌ 获取文件列表失败: ${response.status}`);
                }
            } catch (error) {
                log(`❌ 获取文件列表出错: ${error.message}`);
            }
        }
        
        function updateSelectOptions(selectId, options) {
            const select = document.getElementById(selectId);
            select.innerHTML = '<option value="">请选择文件</option>';
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }
        
        async function copyExample() {
            const filename = document.getElementById('example-file').value;
            if (!filename) {
                showMessage('copy-result', '请选择要复制的文件', 'error');
                return;
            }
            
            try {
                log(`复制示例文件: ${filename}`);
                const response = await fetch(`${API_BASE}/copy_example_to_data?filename=${encodeURIComponent(filename)}`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showMessage('copy-result', `✅ ${data.message}`, 'success');
                        log(`✅ 文件复制成功: ${data.copied_files.join(', ')}`);
                        // 刷新文件列表
                        listFiles();
                    } else {
                        showMessage('copy-result', `❌ ${data.message}`, 'error');
                        log(`❌ 文件复制失败: ${data.message}`);
                    }
                } else {
                    const errorData = await response.json();
                    showMessage('copy-result', `❌ 复制失败: ${errorData.detail}`, 'error');
                    log(`❌ 复制请求失败: ${errorData.detail}`);
                }
            } catch (error) {
                showMessage('copy-result', `❌ 复制出错: ${error.message}`, 'error');
                log(`❌ 复制文件出错: ${error.message}`);
            }
        }
        
        async function processPaper() {
            const filename = document.getElementById('paper-file').value;
            const paperName = document.getElementById('paper-name').value;
            const gptVersion = document.getElementById('gpt-version').value;
            
            if (!filename) {
                showMessage('process-result', '请选择要处理的PDF文件', 'error');
                return;
            }
            
            const processBtn = document.getElementById('process-btn');
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progress-bar');
            
            try {
                processBtn.disabled = true;
                processBtn.textContent = '处理中...';
                progress.style.display = 'block';
                progressBar.style.width = '10%';
                
                log(`开始处理论文: ${filename}`);
                showMessage('process-result', '🚀 正在处理论文，请耐心等待...', 'info');
                
                const requestData = {
                    filename: filename,
                    gpt_version: gptVersion
                };
                
                if (paperName) {
                    requestData.paper_name = paperName;
                }
                
                // 模拟进度更新
                const progressInterval = setInterval(() => {
                    const currentWidth = parseInt(progressBar.style.width) || 10;
                    if (currentWidth < 90) {
                        progressBar.style.width = (currentWidth + 5) + '%';
                    }
                }, 5000);
                
                const response = await fetch(`${API_BASE}/process_paper`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showMessage('process-result', 
                            `✅ ${data.message}<br>输出目录: ${data.output_dir}<br>代码仓库: ${data.output_repo_dir}`, 
                            'success');
                        log(`✅ 论文处理完成: ${data.message}`);
                        log(`   输出目录: ${data.output_dir}`);
                        log(`   代码仓库: ${data.output_repo_dir}`);
                    } else {
                        showMessage('process-result', `❌ ${data.message}${data.error ? '<br>错误: ' + data.error : ''}`, 'error');
                        log(`❌ 论文处理失败: ${data.message}`);
                        if (data.error) log(`   错误详情: ${data.error}`);
                    }
                } else {
                    const errorData = await response.json();
                    showMessage('process-result', `❌ 处理失败: ${errorData.detail}`, 'error');
                    log(`❌ 处理请求失败: ${errorData.detail}`);
                }
            } catch (error) {
                showMessage('process-result', `❌ 处理出错: ${error.message}`, 'error');
                log(`❌ 论文处理出错: ${error.message}`);
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = '开始处理';
                setTimeout(() => {
                    progress.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 2000);
            }
        }
        
        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkHealth();
            listFiles();
        };
    </script>
</body>
</html>
