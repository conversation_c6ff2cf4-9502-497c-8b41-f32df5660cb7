from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import os
import sys
import json
import shutil
from pathlib import Path
import logging
from typing import Optional, AsyncGenerator
import asyncio
from contextlib import redirect_stdout, redirect_stderr
import io
import traceback

# 添加codes目录到Python路径
sys.path.append('codes')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Paper2Code Streaming API", description="将PDF论文转换为代码的流式API服务")

class ProcessRequest(BaseModel):
    filename: str
    paper_name: Optional[str] = None
    gpt_version: str = "openai/o3-mini"

class StreamMessage(BaseModel):
    type: str  # "status", "progress", "output", "error", "complete"
    stage: str
    message: str
    data: Optional[dict] = None

def create_args_object(**kwargs):
    """创建一个类似argparse.Namespace的对象"""
    class Args:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    return Args(**kwargs)

async def stream_step_execution(step_name: str, step_func, *args, **kwargs) -> AsyncGenerator[str, None]:
    """执行单个步骤并流式返回结果"""
    try:
        # 发送开始状态
        yield f"data: {json.dumps({'type': 'status', 'stage': step_name, 'message': f'开始执行 {step_name}'})}\n\n"
        
        # 捕获输出
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        try:
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                # 在线程池中执行步骤
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, step_func, *args, **kwargs)
            
            # 发送输出
            stdout_content = stdout_capture.getvalue()
            if stdout_content:
                yield f"data: {json.dumps({'type': 'output', 'stage': step_name, 'message': stdout_content})}\n\n"
            
            stderr_content = stderr_capture.getvalue()
            if stderr_content:
                yield f"data: {json.dumps({'type': 'output', 'stage': step_name, 'message': stderr_content})}\n\n"
            
            # 发送完成状态
            yield f"data: {json.dumps({'type': 'status', 'stage': step_name, 'message': f'{step_name} 执行完成'})}\n\n"
            
        except Exception as e:
            error_msg = f"{step_name} 执行失败: {str(e)}"
            yield f"data: {json.dumps({'type': 'error', 'stage': step_name, 'message': error_msg})}\n\n"
            raise
            
    except Exception as e:
        error_msg = f"{step_name} 发生错误: {str(e)}\n{traceback.format_exc()}"
        yield f"data: {json.dumps({'type': 'error', 'stage': step_name, 'message': error_msg})}\n\n"
        raise

async def process_paper_stream(request: ProcessRequest) -> AsyncGenerator[str, None]:
    """流式处理论文的主函数"""
    try:
        # 验证文件名
        if not request.filename:
            yield f"data: {json.dumps({'type': 'error', 'stage': 'validation', 'message': '文件名不能为空'})}\n\n"
            return
        
        # 构建文件路径
        filename_without_ext = request.filename.replace('.pdf', '').replace('.json', '')
        paper_name = request.paper_name or filename_without_ext
        
        yield f"data: {json.dumps({'type': 'status', 'stage': 'init', 'message': f'开始处理论文: {paper_name}'})}\n\n"
        
        # 定义路径
        data_dir = Path("data")
        
        # 查找文件
        pdf_path = None
        pdf_json_path = None
        
        # 首先在data目录查找
        data_pdf = data_dir / request.filename
        data_json = data_dir / f"{filename_without_ext}.json"
        
        if data_pdf.exists():
            pdf_path = data_pdf
        if data_json.exists():
            pdf_json_path = data_json
            
        # 如果在data目录没找到，尝试在examples目录查找并复制
        if not pdf_path or not pdf_json_path:
            examples_dir = Path("examples")
            if examples_dir.exists():
                # 查找并复制PDF文件
                if not pdf_path:
                    for pdf_file in examples_dir.rglob(request.filename):
                        data_dir.mkdir(exist_ok=True)
                        pdf_path = data_dir / pdf_file.name
                        shutil.copy2(pdf_file, pdf_path)
                        yield f"data: {json.dumps({'type': 'status', 'stage': 'file_copy', 'message': f'已从examples复制PDF文件: {pdf_file.name}'})}\n\n"
                        break
                
                # 查找并复制JSON文件
                if not pdf_json_path:
                    json_filename = f"{filename_without_ext}.json"
                    for json_file in examples_dir.rglob(json_filename):
                        data_dir.mkdir(exist_ok=True)
                        pdf_json_path = data_dir / json_file.name
                        shutil.copy2(json_file, pdf_json_path)
                        yield f"data: {json.dumps({'type': 'status', 'stage': 'file_copy', 'message': f'已从examples复制JSON文件: {json_file.name}'})}\n\n"
                        break
        
        # 验证文件存在
        if not pdf_path or not pdf_path.exists():
            yield f"data: {json.dumps({'type': 'error', 'stage': 'validation', 'message': f'PDF文件不存在: {request.filename}'})}\n\n"
            return
        
        if not pdf_json_path or not pdf_json_path.exists():
            yield f"data: {json.dumps({'type': 'error', 'stage': 'validation', 'message': f'JSON文件不存在: {filename_without_ext}.json'})}\n\n"
            return
        
        # 定义其他路径
        pdf_json_cleaned_path = data_dir / f"{filename_without_ext}_cleaned.json"
        output_dir = Path("outputs") / paper_name
        output_repo_dir = Path("outputs") / f"{paper_name}_repo"
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        output_repo_dir.mkdir(parents=True, exist_ok=True)
        
        yield f"data: {json.dumps({'type': 'status', 'stage': 'init', 'message': '文件验证完成，开始处理流程'})}\n\n"
        
        # 步骤1: 预处理
        def pdf_process_step():
            # 导入并执行PDF处理
            import importlib.util
            spec = importlib.util.spec_from_file_location("pdf_process", "codes/0_pdf_process.py")
            pdf_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(pdf_module)

            # 调用处理函数
            pdf_module.process_pdf_json(str(pdf_json_path), str(pdf_json_cleaned_path))

        async for chunk in stream_step_execution("预处理", pdf_process_step):
            yield chunk
        
        # 步骤2: 规划
        def planning_step():
            # 动态导入并执行规划模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("planning", "codes/1_planning.py")
            planning_module = importlib.util.module_from_spec(spec)
            
            # 设置模块的全局变量
            planning_module.paper_name = paper_name
            planning_module.gpt_version = request.gpt_version
            planning_module.paper_format = "JSON"
            planning_module.pdf_json_path = str(pdf_json_cleaned_path)
            planning_module.output_dir = str(output_dir)
            
            # 执行模块
            spec.loader.exec_module(planning_module)
        
        async for chunk in stream_step_execution("规划阶段", planning_step):
            yield chunk
        
        # 步骤3: 提取配置
        def extract_config_step():
            import importlib.util
            spec = importlib.util.spec_from_file_location("extract_config", "codes/1.1_extract_config.py")
            config_module = importlib.util.module_from_spec(spec)
            
            config_module.paper_name = paper_name
            config_module.output_dir = str(output_dir)
            
            spec.loader.exec_module(config_module)
        
        async for chunk in stream_step_execution("配置提取", extract_config_step):
            yield chunk
        
        # 步骤4: 复制配置文件
        yield f"data: {json.dumps({'type': 'status', 'stage': 'config_copy', 'message': '复制配置文件'})}\n\n"
        config_source = output_dir / "planning_config.yaml"
        config_dest = output_repo_dir / "config.yaml"
        if config_source.exists():
            shutil.copy2(config_source, config_dest)
            yield f"data: {json.dumps({'type': 'status', 'stage': 'config_copy', 'message': '配置文件复制完成'})}\n\n"
        else:
            yield f"data: {json.dumps({'type': 'status', 'stage': 'config_copy', 'message': '配置文件不存在，跳过复制'})}\n\n"
        
        # 步骤5: 分析
        def analyzing_step():
            import importlib.util
            spec = importlib.util.spec_from_file_location("analyzing", "codes/2_analyzing.py")
            analyzing_module = importlib.util.module_from_spec(spec)
            
            analyzing_module.paper_name = paper_name
            analyzing_module.gpt_version = request.gpt_version
            analyzing_module.paper_format = "JSON"
            analyzing_module.pdf_json_path = str(pdf_json_cleaned_path)
            analyzing_module.output_dir = str(output_dir)
            
            spec.loader.exec_module(analyzing_module)
        
        async for chunk in stream_step_execution("分析阶段", analyzing_step):
            yield chunk
        
        # 步骤6: 编码
        def coding_step():
            import importlib.util
            spec = importlib.util.spec_from_file_location("coding", "codes/3_coding.py")
            coding_module = importlib.util.module_from_spec(spec)
            
            coding_module.paper_name = paper_name
            coding_module.gpt_version = request.gpt_version
            coding_module.paper_format = "JSON"
            coding_module.pdf_json_path = str(pdf_json_cleaned_path)
            coding_module.output_dir = str(output_dir)
            coding_module.output_repo_dir = str(output_repo_dir)
            
            spec.loader.exec_module(coding_module)
        
        async for chunk in stream_step_execution("编码阶段", coding_step):
            yield chunk
        
        # 完成
        yield f"data: {json.dumps({'type': 'complete', 'stage': 'finished', 'message': f'论文 {paper_name} 处理完成', 'data': {'output_dir': str(output_dir), 'output_repo_dir': str(output_repo_dir)}})}\n\n"
        
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}\n{traceback.format_exc()}"
        yield f"data: {json.dumps({'type': 'error', 'stage': 'general', 'message': error_msg})}\n\n"

@app.post("/process_paper_stream")
async def process_paper_stream_endpoint(request: ProcessRequest):
    """流式处理论文接口"""
    return StreamingResponse(
        process_paper_stream(request),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "Paper2Code Streaming API服务",
        "version": "2.0.0",
        "description": "将PDF论文转换为代码的流式API服务",
        "features": ["直接模块调用", "流式响应", "实时状态更新"]
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}

@app.get("/list_papers")
async def list_papers():
    """列出data目录和examples目录下的所有PDF和JSON文件"""
    try:
        result = {
            "data_pdf_files": [],
            "data_json_files": [],
            "examples_pdf_files": [],
            "examples_json_files": []
        }
        
        # 检查data目录
        data_dir = Path("data")
        if data_dir.exists():
            result["data_pdf_files"] = [f.name for f in data_dir.glob("*.pdf")]
            result["data_json_files"] = [f.name for f in data_dir.glob("*.json") if not f.name.endswith("_cleaned.json")]
        
        # 检查examples目录
        examples_dir = Path("examples")
        if examples_dir.exists():
            for pdf_file in examples_dir.rglob("*.pdf"):
                result["examples_pdf_files"].append(str(pdf_file.relative_to(examples_dir)))
            for json_file in examples_dir.rglob("*.json"):
                if not json_file.name.endswith("_cleaned.json"):
                    result["examples_json_files"].append(str(json_file.relative_to(examples_dir)))
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@app.post("/copy_example_to_data")
async def copy_example_to_data(filename: str):
    """将examples目录下的文件复制到data目录"""
    try:
        examples_dir = Path("examples")
        data_dir = Path("data")
        
        data_dir.mkdir(exist_ok=True)
        
        source_file = None
        for file_path in examples_dir.rglob(filename):
            source_file = file_path
            break
        
        if not source_file:
            raise HTTPException(status_code=404, detail=f"在examples目录中未找到文件: {filename}")
        
        dest_file = data_dir / source_file.name
        shutil.copy2(source_file, dest_file)
        
        copied_files = [source_file.name]
        
        # 如果是PDF文件，也尝试复制对应的JSON文件
        if source_file.suffix.lower() == '.pdf':
            json_filename = source_file.stem + '.json'
            json_source = source_file.parent / json_filename
            if json_source.exists():
                json_dest = data_dir / json_filename
                shutil.copy2(json_source, json_dest)
                copied_files.append(json_filename)
        
        return {
            "success": True,
            "message": f"已复制 {', '.join(copied_files)} 到data目录",
            "copied_files": copied_files
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"复制文件失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
