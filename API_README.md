# Paper2Code 流式API 文档

## 概述

Paper2Code 流式API 是一个基于 FastAPI 的 Web 服务，用于将 PDF 论文转换为可执行代码。该 API 提供实时流式输出，让用户可以实时查看处理进度。

## 功能特性

- 🚀 流式响应 - 实时查看处理进度
- 📄 支持 PDF 论文处理
- 🔄 完整的 Paper2Code 流程
- 📊 实时处理状态反馈
- 🗂️ 文件列表查看和管理
- ❤️ 健康检查

## API 接口

### 1. 根路径
- **URL:** `GET /`
- **描述:** 获取 API 基本信息
- **响应:**
```json
{
    "message": "Paper2Code Streaming API服务",
    "version": "2.0.0",
    "description": "将PDF论文转换为代码的流式API服务",
    "features": ["流式响应", "实时状态更新", "Server-Sent Events"]
}
```

### 2. 健康检查
- **URL:** `GET /health`
- **描述:** 检查服务状态
- **响应:**
```json
{
    "status": "healthy"
}
```

### 3. 列出论文文件
- **URL:** `GET /list_papers`
- **描述:** 列出 data 目录和 examples 目录下的所有 PDF 和 JSON 文件
- **响应:**
```json
{
    "data_pdf_files": ["paper1.pdf", "paper2.pdf"],
    "data_json_files": ["paper1.json", "paper2.json"],
    "examples_pdf_files": ["demo/2310.05470v2.pdf", "Transformer/Transformer.pdf"],
    "examples_json_files": ["demo/2310.05470v2.json", "Transformer/Transformer.json"]
}
```

### 4. 复制示例文件
- **URL:** `POST /copy_example_to_data?filename={filename}`
- **描述:** 将 examples 目录下的文件复制到 data 目录
- **参数:**
  - `filename`: 要复制的文件名（支持相对路径，如 "demo/2310.05470v2.pdf"）
- **响应:**
```json
{
    "success": true,
    "message": "已复制 2310.05470v2.pdf 和 2310.05470v2.json 到data目录",
    "copied_files": ["2310.05470v2.pdf", "2310.05470v2.json"]
}
```

### 5. 流式处理论文 (主要接口)
- **URL:** `POST /process_paper_stream`
- **描述:** 流式处理 PDF 论文，执行完整的 Paper2Code 流程
- **请求体:**
```json
{
    "filename": "paper.pdf",
    "paper_name": "my_paper",
    "gpt_version": "openai/o3-mini"
}
```

- **参数说明:**
  - `filename` (必需): PDF 文件名，文件应位于 `/data` 目录下
  - `paper_name` (可选): 论文名称，默认使用文件名
  - `gpt_version` (可选): GPT 版本，默认为 "openai/o3-mini"

- **流式响应格式:**
```
data: {"type": "status", "stage": "init", "message": "开始处理论文: my_paper"}

data: {"type": "output", "stage": "预处理", "message": "Processing file..."}

data: {"type": "error", "stage": "预处理", "message": "Error occurred"}

data: {"type": "complete", "stage": "finished", "message": "论文 my_paper 处理完成", "data": {"output_dir": "outputs/my_paper", "output_repo_dir": "outputs/my_paper_repo"}}
```

## 流式响应消息类型

- **status**: 状态更新消息
- **output**: 程序输出消息
- **error**: 错误信息
- **complete**: 完成消息

## 使用示例

### 使用 curl

```bash
# 检查服务状态
curl http://localhost:8000/health

# 列出可用文件
curl http://localhost:8000/list_papers

# 复制示例文件到data目录
curl -X POST "http://localhost:8000/copy_example_to_data?filename=demo/2310.05470v2.pdf"

# 流式处理论文
curl -X POST "http://localhost:8000/process_paper_stream" \
     -H "Content-Type: application/json" \
     -d '{
         "filename": "2310.05470v2.pdf",
         "paper_name": "autoj",
         "gpt_version": "openai/o3-mini"
     }'
```

### 使用 Python requests

```python
import requests
import json

# 流式处理论文
response = requests.post(
    "http://localhost:8000/process_paper_stream",
    json={
        "filename": "2310.05470v2.pdf",
        "paper_name": "autoj",
        "gpt_version": "openai/o3-mini"
    },
    stream=True
)

for line in response.iter_lines():
    if line and line.startswith(b'data: '):
        data = json.loads(line[6:])  # 去掉 'data: ' 前缀
        print(f"[{data['stage']}] {data['message']}")
```

### 使用 JavaScript EventSource

```javascript
const eventSource = new EventSource('http://localhost:8000/process_paper_stream');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log(`[${data.stage}] ${data.message}`);
    
    if (data.type === 'complete') {
        eventSource.close();
    }
};
```

## 前置条件

### 自动PDF转JSON（推荐）

API现在支持自动PDF转JSON转换！只需要：

1. **克隆s2orc-doc2json仓库:**
```bash
git clone https://github.com/allenai/s2orc-doc2json.git
```

2. **启动Grobid服务:**
```bash
cd ./s2orc-doc2json/grobid-0.7.3
./gradlew run
```

3. **准备PDF文件:**
   - 将PDF文件放入 `data/` 目录
   - API会自动检测并转换为JSON格式

### 手动PDF转JSON（可选）

如果你想手动转换，可以使用：

```bash
# 转换 PDF 为 JSON
python ./s2orc-doc2json/doc2json/grobid2json/process_pdf.py \
    -i data/your_paper.pdf \
    -t ./s2orc-doc2json/temp_dir/ \
    -o data/
```

### 文件结构

最少需要：
- `data/your_paper.pdf` - 原始 PDF 文件

API会自动生成：
- `data/your_paper.json` - 转换后的 JSON 文件（如果不存在）

## 处理流程

API 执行以下步骤：

1. **PDF转JSON:** 如果JSON文件不存在，自动从PDF转换 (`s2orc-doc2json`)
2. **预处理:** 清理 JSON 数据 (`0_pdf_process.py`)
3. **规划:** 生成实现计划 (`1_planning.py`)
4. **配置提取:** 提取配置信息 (`1.1_extract_config.py`)
5. **分析:** 分析论文内容 (`2_analyzing.py`)
6. **编码:** 生成最终代码 (`3_coding.py`)

## 输出结果

处理完成后，结果将保存在：
- `outputs/{paper_name}/` - 中间处理文件
- `outputs/{paper_name}_repo/` - 最终生成的代码仓库

## 错误处理

常见错误及解决方案：

1. **文件不存在:** 确保 PDF 和 JSON 文件都在 data 目录下
2. **处理超时:** 大型论文可能需要较长时间，请耐心等待
3. **API 密钥错误:** 检查 OpenAI API 密钥配置

## 注意事项

- 处理大型论文可能需要较长时间（几分钟到几十分钟）
- 确保有足够的磁盘空间存储输出文件
- 建议在处理前备份重要数据
- API 服务需要访问 OpenAI API，确保网络连接正常
- 使用流式响应可以实时监控处理进度

## 技术细节

- **框架:** FastAPI
- **流式协议:** Server-Sent Events (SSE)
- **异步处理:** asyncio subprocess
- **端口:** 8000
- **CORS:** 已启用，支持跨域访问
