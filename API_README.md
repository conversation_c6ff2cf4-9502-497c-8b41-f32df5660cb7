# Paper2Code API 使用说明

## 概述

Paper2Code API 是一个基于 FastAPI 的 Web 服务，用于将 PDF 论文转换为可执行代码。该 API 封装了原有的 run.sh 脚本功能，提供了更便捷的 HTTP 接口。

## 功能特性

- 🚀 RESTful API 接口
- 📄 支持 PDF 论文处理
- 🔄 完整的 Paper2Code 流程
- 📊 实时处理状态反馈
- 🗂️ 文件列表查看
- ❤️ 健康检查

## 安装和启动

### 方法1: 使用启动脚本

**Linux/Mac:**
```bash
chmod +x start_api.sh
./start_api.sh
```

**Windows:**
```cmd
start_api.bat
```

### 方法2: 手动启动

1. 安装依赖：
```bash
pip install -r requirements_api.txt
```

2. 启动服务：
```bash
python api_server.py
```

服务将在 `http://localhost:8000` 启动。

## API 接口

### 1. 根路径
- **URL:** `GET /`
- **描述:** 获取 API 基本信息
- **响应:**
```json
{
    "message": "Paper2Code API服务",
    "version": "1.0.0",
    "description": "将PDF论文转换为代码的API服务"
}
```

### 2. 健康检查
- **URL:** `GET /health`
- **描述:** 检查服务状态
- **响应:**
```json
{
    "status": "healthy"
}
```

### 3. 列出论文文件
- **URL:** `GET /list_papers`
- **描述:** 列出 data 目录和 examples 目录下的所有 PDF 和 JSON 文件
- **响应:**
```json
{
    "data_pdf_files": ["paper1.pdf", "paper2.pdf"],
    "data_json_files": ["paper1.json", "paper2.json"],
    "examples_pdf_files": ["demo/2310.05470v2.pdf", "Transformer/Transformer.pdf"],
    "examples_json_files": ["demo/2310.05470v2.json", "Transformer/Transformer.json"]
}
```

### 4. 复制示例文件
- **URL:** `POST /copy_example_to_data?filename={filename}`
- **描述:** 将 examples 目录下的文件复制到 data 目录
- **参数:**
  - `filename`: 要复制的文件名（支持相对路径，如 "demo/2310.05470v2.pdf"）
- **响应:**
```json
{
    "success": true,
    "message": "已复制 2310.05470v2.pdf 和 2310.05470v2.json 到data目录",
    "copied_files": ["2310.05470v2.pdf", "2310.05470v2.json"]
}
```

### 5. 处理论文 (主要接口)
- **URL:** `POST /process_paper`
- **描述:** 处理 PDF 论文，执行完整的 Paper2Code 流程
- **请求体:**
```json
{
    "filename": "paper.pdf",
    "paper_name": "my_paper",
    "gpt_version": "openai/o3-mini"
}
```

- **参数说明:**
  - `filename` (必需): PDF 文件名，文件应位于 `/data` 目录下
  - `paper_name` (可选): 论文名称，默认使用文件名
  - `gpt_version` (可选): GPT 版本，默认为 "openai/o3-mini"

- **成功响应:**
```json
{
    "success": true,
    "message": "论文 my_paper 处理完成",
    "output_dir": "outputs/my_paper",
    "output_repo_dir": "outputs/my_paper_repo",
    "error": null
}
```

- **失败响应:**
```json
{
    "success": false,
    "message": "处理失败",
    "output_dir": null,
    "output_repo_dir": null,
    "error": "错误详情"
}
```

## 使用示例

### 使用 curl

```bash
# 检查服务状态
curl http://localhost:8000/health

# 列出可用文件
curl http://localhost:8000/list_papers

# 复制示例文件到data目录
curl -X POST "http://localhost:8000/copy_example_to_data?filename=demo/2310.05470v2.pdf"

# 处理论文
curl -X POST "http://localhost:8000/process_paper" \
     -H "Content-Type: application/json" \
     -d '{
         "filename": "2310.05470v2.pdf",
         "paper_name": "autoj",
         "gpt_version": "openai/o3-mini"
     }'
```

### 使用 Python requests

```python
import requests

# 处理论文
response = requests.post(
    "http://localhost:8000/process_paper",
    json={
        "filename": "2310.05470v2.pdf",
        "paper_name": "autoj",
        "gpt_version": "openai/o3-mini"
    }
)

result = response.json()
print(result)
```

## 前置条件

1. **PDF 转 JSON:** 在使用 API 之前，需要先将 PDF 文件转换为 JSON 格式。使用 s2orc-doc2json 工具：

```bash
# 启动 Grobid 服务
cd ./s2orc-doc2json/grobid-0.7.3
./gradlew run

# 转换 PDF 为 JSON
python ./s2orc-doc2json/doc2json/grobid2json/process_pdf.py \
    -i data/your_paper.pdf \
    -t ./s2orc-doc2json/temp_dir/ \
    -o data/
```

2. **文件结构:** 确保以下文件存在：
   - `data/your_paper.pdf` - 原始 PDF 文件
   - `data/your_paper.json` - 转换后的 JSON 文件

## 处理流程

API 执行以下步骤（对应原 run.sh 脚本）：

1. **预处理:** 清理 JSON 数据 (`0_pdf_process.py`)
2. **规划:** 生成实现计划 (`1_planning.py`)
3. **配置提取:** 提取配置信息 (`1.1_extract_config.py`)
4. **分析:** 分析论文内容 (`2_analyzing.py`)
5. **编码:** 生成最终代码 (`3_coding.py`)

## 输出结果

处理完成后，结果将保存在：
- `outputs/{paper_name}/` - 中间处理文件
- `outputs/{paper_name}_repo/` - 最终生成的代码仓库

## 错误处理

常见错误及解决方案：

1. **文件不存在:** 确保 PDF 和 JSON 文件都在 data 目录下
2. **处理超时:** 大型论文可能需要较长时间，请耐心等待
3. **API 密钥错误:** 检查 OpenAI API 密钥配置

## 注意事项

- 处理大型论文可能需要较长时间（几分钟到几十分钟）
- 确保有足够的磁盘空间存储输出文件
- 建议在处理前备份重要数据
- API 服务需要访问 OpenAI API，确保网络连接正常

## 开发和扩展

如需修改或扩展 API 功能，可以编辑 `api_server.py` 文件。主要组件：

- `ProcessRequest`: 请求模型
- `ProcessResponse`: 响应模型  
- `process_paper`: 主要处理函数
- `run_command`: 命令执行工具函数
