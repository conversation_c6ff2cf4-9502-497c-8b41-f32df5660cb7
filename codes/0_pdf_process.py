import json
import argparse

def remove_spans(data):
    # If data is a dictionary, recursively check its keys
    if isinstance(data, dict):
        # Remove specific keys if present
        for key in [" ", "ref_spans", "eq_spans", "authors", "bib_entries", \
                    "year", "venue", "identifiers", "_pdf_hash", "header"]:
            data.pop(key, None)
        # Recursively apply to child dictionaries or lists
        for key, value in data.items():
            data[key] = remove_spans(value)
    # If data is a list, apply the function to each item
    elif isinstance(data, list):
        return [remove_spans(item) for item in data]
    return data

def main(args):
    input_json_path = args.input_json_path
    output_json_path = args.output_json_path 

    with open(f'{input_json_path}') as f:
        data = json.load(f)

    cleaned_data = remove_spans(data)

    print(f"[SAVED] {output_json_path}")
    with open(output_json_path, 'w') as f:
        json.dump(cleaned_data, f)


def process_pdf_json(input_json_path, output_json_path):
    """处理PDF JSON文件的包装函数，用于API调用"""
    class Args:
        def __init__(self, input_json_path, output_json_path):
            self.input_json_path = input_json_path
            self.output_json_path = output_json_path

    args = Args(input_json_path, output_json_path)
    main(args)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_json_path", type=str)
    parser.add_argument("--output_json_path", type=str)

    
    args = parser.parse_args()
    main(args)

# run
# cd ./s2orc-doc2json/grobid-0.7.3
# ./gradlew run

# python doc2json/grobid2json/process_pdf.py -i tests/pdf/transformer.pdf -t temp_dir/ -o output_dir/