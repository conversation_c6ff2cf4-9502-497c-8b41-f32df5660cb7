"""
模块化的配置提取功能
将1.1_extract_config.py改造为可以直接调用的函数
"""

import json
import re
import os
import shutil
from typing import Optional, Callable
from utils import extract_planning, content_to_json, format_json_data

def extract_config(
    paper_name: str,
    output_dir: str,
    progress_callback: Optional[Callable[[str], None]] = None
) -> dict:
    """
    从规划轨迹中提取配置信息
    
    Args:
        paper_name: 论文名称
        output_dir: 输出目录
        progress_callback: 进度回调函数
        
    Returns:
        包含处理结果的字典
    """
    
    if progress_callback:
        progress_callback("开始提取配置信息...")
    
    # 检查规划轨迹文件是否存在
    planning_file = f'{output_dir}/planning_trajectories.json'
    if not os.path.exists(planning_file):
        error_msg = f"规划轨迹文件不存在: {planning_file}"
        if progress_callback:
            progress_callback(error_msg)
        raise FileNotFoundError(error_msg)
    
    # 加载规划轨迹
    if progress_callback:
        progress_callback("加载规划轨迹...")
    
    try:
        with open(planning_file, encoding='utf8') as f:
            traj = json.load(f)
    except Exception as e:
        error_msg = f"加载规划轨迹失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 提取YAML配置内容
    if progress_callback:
        progress_callback("提取YAML配置...")
    
    yaml_raw_content = ""
    for turn_idx, turn in enumerate(traj):
        if turn_idx == 8:  # 通常配置在第8个回合
            yaml_raw_content = turn['content']
            break
    
    if not yaml_raw_content:
        # 如果第8个回合没有内容，尝试查找包含YAML的回合
        for turn in traj:
            if turn['role'] == 'assistant' and 'yaml' in turn['content'].lower():
                yaml_raw_content = turn['content']
                break
    
    if not yaml_raw_content:
        error_msg = "未找到包含配置的回合"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 处理thinking标签
    if "</think>" in yaml_raw_content:
        yaml_raw_content = yaml_raw_content.split("</think>")[-1]
    
    # 提取YAML内容
    yaml_content = None
    
    # 尝试匹配标准格式
    match = re.search(r"```yaml\n(.*?)\n```", yaml_raw_content, re.DOTALL)
    if match:
        yaml_content = match.group(1)
    else:
        # 尝试匹配转义格式
        match2 = re.search(r"```yaml\\n(.*?)\\n```", yaml_raw_content, re.DOTALL)
        if match2:
            yaml_content = match2.group(1)
    
    if not yaml_content:
        error_msg = "未找到YAML配置内容"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 保存配置文件
    if progress_callback:
        progress_callback("保存配置文件...")
    
    config_file = f'{output_dir}/planning_config.yaml'
    try:
        with open(config_file, 'w', encoding='utf8') as f:
            f.write(yaml_content)
    except Exception as e:
        error_msg = f"保存配置文件失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    # 创建artifacts目录并处理规划内容
    if progress_callback:
        progress_callback("处理规划artifacts...")
    
    artifact_output_dir = f"{output_dir}/planning_artifacts"
    os.makedirs(artifact_output_dir, exist_ok=True)
    
    try:
        context_lst = extract_planning(planning_file)
        
        # 保存规划上下文
        for idx, context in enumerate(context_lst):
            # 尝试解析为JSON
            try:
                json_data = content_to_json(context)
                formatted_data = format_json_data(json_data)
                
                # 保存为JSON文件
                with open(f'{artifact_output_dir}/planning_context_{idx}.json', 'w', encoding='utf8') as f:
                    json.dump(formatted_data, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                # 如果不是JSON格式，保存为文本文件
                with open(f'{artifact_output_dir}/planning_context_{idx}.txt', 'w', encoding='utf8') as f:
                    f.write(context)
                    
    except Exception as e:
        # 这不是致命错误，只是警告
        if progress_callback:
            progress_callback(f"处理artifacts时出现警告: {e}")
    
    if progress_callback:
        progress_callback("配置提取完成！")
    
    return {
        "success": True,
        "config_file": config_file,
        "yaml_content": yaml_content,
        "artifact_dir": artifact_output_dir,
        "context_count": len(context_lst) if 'context_lst' in locals() else 0
    }

# 为了向后兼容，保留命令行接口
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--paper_name', type=str, required=True)
    parser.add_argument('--output_dir', type=str, default="")
    
    args = parser.parse_args()
    
    def print_progress(msg):
        print(f"[CONFIG] {msg}")
    
    try:
        result = extract_config(
            paper_name=args.paper_name,
            output_dir=args.output_dir,
            progress_callback=print_progress
        )
        print("配置提取成功完成！")
        print(f"配置文件: {result['config_file']}")
    except Exception as e:
        print(f"配置提取失败: {e}")
        import sys
        sys.exit(1)
