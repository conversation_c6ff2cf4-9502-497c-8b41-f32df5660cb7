"""
模块化的论文分析功能
将2_analyzing.py改造为可以直接调用的函数
"""

from openai import OpenAI
import json
import os
import sys
import copy
from typing import Optional, Dict, Any, List, Callable
from utils import extract_planning, content_to_json, print_response, print_log_cost, load_accumulated_cost, save_accumulated_cost

def create_openai_client():
    """创建OpenAI客户端"""
    return OpenAI(
        api_key="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",
        base_url="https://openrouter.ai/api/v1",
        timeout=6000
    )

def load_paper_content(paper_format: str, pdf_json_path: Optional[str] = None, pdf_latex_path: Optional[str] = None):
    """加载论文内容"""
    if paper_format == "JSON":
        if not pdf_json_path:
            raise ValueError("JSON格式需要提供pdf_json_path参数")
        with open(pdf_json_path) as f:
            return json.load(f)
    elif paper_format == "LaTeX":
        if not pdf_latex_path:
            raise ValueError("LaTeX格式需要提供pdf_latex_path参数")
        with open(pdf_latex_path) as f:
            return f.read()
    else:
        raise ValueError("无效的论文格式。请选择 'JSON' 或 'LaTeX'")

def load_config_and_planning(output_dir: str):
    """加载配置文件和规划结果"""
    # 加载配置文件
    config_path = f'{output_dir}/planning_config.yaml'
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path) as f:
        config_yaml = f.read()
    
    # 加载规划轨迹
    planning_path = f'{output_dir}/planning_trajectories.json'
    if not os.path.exists(planning_path):
        raise FileNotFoundError(f"规划轨迹文件不存在: {planning_path}")
    
    context_lst = extract_planning(planning_path)
    
    return config_yaml, context_lst

def api_call(messages: List[Dict[str, str]], gpt_version: str, client: OpenAI):
    """调用OpenAI API"""
    try:
        completion = client.chat.completions.create(
            model=gpt_version,
            messages=messages,
            temperature=0.7,
            max_tokens=4000
        )
        return completion
    except Exception as e:
        print(f"API调用失败: {e}")
        raise

def run_analyzing(
    paper_name: str,
    gpt_version: str,
    pdf_json_path: Optional[str] = None,
    pdf_latex_path: Optional[str] = None,
    paper_format: str = "JSON",
    output_dir: str = "",
    progress_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    运行论文分析阶段
    
    Args:
        paper_name: 论文名称
        gpt_version: GPT版本
        pdf_json_path: JSON格式论文路径
        pdf_latex_path: LaTeX格式论文路径
        paper_format: 论文格式 ("JSON" 或 "LaTeX")
        output_dir: 输出目录
        progress_callback: 进度回调函数
        
    Returns:
        包含处理结果的字典
    """
    
    if progress_callback:
        progress_callback("初始化分析阶段...")
    
    # 创建OpenAI客户端
    client = create_openai_client()
    
    # 加载论文内容
    if progress_callback:
        progress_callback("加载论文内容...")
    
    try:
        paper_content = load_paper_content(paper_format, pdf_json_path, pdf_latex_path)
    except Exception as e:
        error_msg = f"加载论文内容失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 加载配置和规划结果
    if progress_callback:
        progress_callback("加载配置和规划结果...")
    
    try:
        config_yaml, context_lst = load_config_and_planning(output_dir)
    except Exception as e:
        error_msg = f"加载配置和规划结果失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 加载累积成本
    total_accumulated_cost = load_accumulated_cost(f"{output_dir}/accumulated_cost.json")
    
    # 初始化变量
    trajectories = []
    responses = []
    current_stage = "analyzing"
    
    # 创建分析消息
    if progress_callback:
        progress_callback("创建分析提示...")
    
    # 系统消息
    system_msg = {
        'role': "system", 
        'content': f"""You are an expert code analyst and software architect with deep expertise in understanding and analyzing research papers for implementation purposes.

You will receive:
1. A research paper in {paper_format} format
2. A configuration file (YAML) that outlines the implementation plan
3. Previous planning context from earlier analysis

Your task is to perform a detailed technical analysis of the paper to extract all necessary information for implementation. This analysis should be comprehensive and actionable.

Instructions:
1. **Deep Technical Analysis**: Analyze the paper's methodology, algorithms, and technical details
2. **Implementation Insights**: Identify key implementation challenges and solutions
3. **Code Structure Planning**: Suggest optimal code organization and architecture
4. **Data Flow Analysis**: Map out how data flows through the system
5. **Performance Considerations**: Identify potential bottlenecks and optimization opportunities

Focus on:
- Algorithm details and mathematical formulations
- Data structures and their relationships
- Input/output specifications
- Error handling requirements
- Testing strategies
- Performance metrics and benchmarks

Provide actionable insights that will guide the coding phase."""
    }
    
    # 用户消息
    user_content = f"""Please perform a detailed technical analysis of this research paper for implementation:

**Paper Content:**
{json.dumps(paper_content) if paper_format == 'JSON' else paper_content}

**Configuration:**
{config_yaml}

**Previous Planning Context:**
{json.dumps(context_lst)}

Please provide a comprehensive technical analysis that will guide the implementation phase."""
    
    user_msg = {'role': "user", 'content': user_content}
    
    # 添加消息到轨迹
    trajectories.extend([system_msg, user_msg])
    
    # 执行分析
    if progress_callback:
        progress_callback("开始技术分析...")
    
    try:
        # 调用API
        completion = api_call(trajectories, gpt_version, client)
        
        # 处理响应
        completion_json = json.loads(completion.model_dump_json())
        
        # 打印和记录
        if progress_callback:
            progress_callback("处理分析结果...")
        
        print_response(completion_json)
        temp_total_accumulated_cost = print_log_cost(completion_json, gpt_version, current_stage, output_dir, total_accumulated_cost)
        total_accumulated_cost = temp_total_accumulated_cost
        
        responses.append(completion_json)
        
        # 添加响应到轨迹
        message = completion.choices[0].message
        trajectories.append({'role': message.role, 'content': message.content})
        
    except Exception as e:
        error_msg = f"分析阶段API调用失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    # 保存结果
    if progress_callback:
        progress_callback("保存分析结果...")
    
    try:
        # 保存累积成本
        save_accumulated_cost(f"{output_dir}/accumulated_cost.json", total_accumulated_cost)
        
        # 保存响应
        with open(f'{output_dir}/analyzing_response.json', 'w', encoding='utf-8') as f:
            json.dump(responses, f, ensure_ascii=False, indent=2)
        
        # 保存轨迹
        with open(f'{output_dir}/analyzing_trajectories.json', 'w', encoding='utf-8') as f:
            json.dump(trajectories, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        error_msg = f"保存分析结果失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    if progress_callback:
        progress_callback("分析阶段完成！")
    
    return {
        "success": True,
        "responses": responses,
        "trajectories": trajectories,
        "total_cost": total_accumulated_cost,
        "output_files": [
            f"{output_dir}/analyzing_response.json",
            f"{output_dir}/analyzing_trajectories.json",
            f"{output_dir}/accumulated_cost.json"
        ]
    }

# 为了向后兼容，保留命令行接口
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--paper_name', type=str, required=True)
    parser.add_argument('--gpt_version', type=str, default="o3-mini")
    parser.add_argument('--paper_format', type=str, default="JSON", choices=["JSON", "LaTeX"])
    parser.add_argument('--pdf_json_path', type=str)
    parser.add_argument('--pdf_latex_path', type=str)
    parser.add_argument('--output_dir', type=str, default="")
    
    args = parser.parse_args()
    
    def print_progress(msg):
        print(f"[ANALYZING] {msg}")
    
    try:
        result = run_analyzing(
            paper_name=args.paper_name,
            gpt_version=args.gpt_version,
            pdf_json_path=args.pdf_json_path,
            pdf_latex_path=args.pdf_latex_path,
            paper_format=args.paper_format,
            output_dir=args.output_dir,
            progress_callback=print_progress
        )
        print("分析阶段成功完成！")
    except Exception as e:
        print(f"分析阶段失败: {e}")
        sys.exit(1)
