"""
模块化的代码生成功能
将3_coding.py改造为可以直接调用的函数
"""

from openai import OpenAI
import json
import os
import sys
import copy
import re
from typing import Optional, Dict, Any, List, Callable
from utils import extract_planning, content_to_json, extract_code_from_content, print_response, print_log_cost, load_accumulated_cost, save_accumulated_cost

def create_openai_client():
    """创建OpenAI客户端"""
    return OpenAI(
        api_key="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",
        base_url="https://openrouter.ai/api/v1",
        timeout=6000
    )

def load_paper_content(paper_format: str, pdf_json_path: Optional[str] = None, pdf_latex_path: Optional[str] = None):
    """加载论文内容"""
    if paper_format == "JSON":
        if not pdf_json_path:
            raise ValueError("JSON格式需要提供pdf_json_path参数")
        with open(pdf_json_path) as f:
            return json.load(f)
    elif paper_format == "LaTeX":
        if not pdf_latex_path:
            raise ValueError("LaTeX格式需要提供pdf_latex_path参数")
        with open(pdf_latex_path) as f:
            return f.read()
    else:
        raise ValueError("无效的论文格式。请选择 'JSON' 或 'LaTeX'")

def load_previous_results(output_dir: str):
    """加载之前阶段的结果"""
    # 加载配置文件
    config_path = f'{output_dir}/planning_config.yaml'
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path) as f:
        config_yaml = f.read()
    
    # 加载规划轨迹
    planning_path = f'{output_dir}/planning_trajectories.json'
    if not os.path.exists(planning_path):
        raise FileNotFoundError(f"规划轨迹文件不存在: {planning_path}")
    
    context_lst = extract_planning(planning_path)
    
    # 加载分析结果
    analyzing_path = f'{output_dir}/analyzing_trajectories.json'
    analyzing_context = []
    if os.path.exists(analyzing_path):
        with open(analyzing_path) as f:
            analyzing_traj = json.load(f)
        for turn in analyzing_traj:
            if turn['role'] == 'assistant':
                content = turn['content']
                if "</think>" in content:
                    content = content.split("</think>")[-1].strip()
                analyzing_context.append(content)
    
    return config_yaml, context_lst, analyzing_context

def api_call(messages: List[Dict[str, str]], gpt_version: str, client: OpenAI):
    """调用OpenAI API"""
    try:
        completion = client.chat.completions.create(
            model=gpt_version,
            messages=messages,
            temperature=0.7,
            max_tokens=4000
        )
        return completion
    except Exception as e:
        print(f"API调用失败: {e}")
        raise

def run_coding(
    paper_name: str,
    gpt_version: str,
    pdf_json_path: Optional[str] = None,
    pdf_latex_path: Optional[str] = None,
    paper_format: str = "JSON",
    output_dir: str = "",
    output_repo_dir: str = "",
    progress_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    运行代码生成阶段
    
    Args:
        paper_name: 论文名称
        gpt_version: GPT版本
        pdf_json_path: JSON格式论文路径
        pdf_latex_path: LaTeX格式论文路径
        paper_format: 论文格式 ("JSON" 或 "LaTeX")
        output_dir: 输出目录
        output_repo_dir: 代码仓库输出目录
        progress_callback: 进度回调函数
        
    Returns:
        包含处理结果的字典
    """
    
    if progress_callback:
        progress_callback("初始化编码阶段...")
    
    # 创建OpenAI客户端
    client = create_openai_client()
    
    # 加载论文内容
    if progress_callback:
        progress_callback("加载论文内容...")
    
    try:
        paper_content = load_paper_content(paper_format, pdf_json_path, pdf_latex_path)
    except Exception as e:
        error_msg = f"加载论文内容失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 加载之前阶段的结果
    if progress_callback:
        progress_callback("加载规划和分析结果...")
    
    try:
        config_yaml, context_lst, analyzing_context = load_previous_results(output_dir)
    except Exception as e:
        error_msg = f"加载之前阶段结果失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 创建输出目录
    os.makedirs(output_repo_dir, exist_ok=True)
    
    # 加载累积成本
    total_accumulated_cost = load_accumulated_cost(f"{output_dir}/accumulated_cost.json")
    
    # 初始化变量
    trajectories = []
    responses = []
    current_stage = "coding"
    
    # 创建编码消息
    if progress_callback:
        progress_callback("创建编码提示...")
    
    # 系统消息
    system_msg = {
        'role': "system", 
        'content': f"""You are an expert software engineer and researcher specializing in implementing research papers into production-ready code.

You will receive:
1. A research paper in {paper_format} format
2. A configuration file (YAML) outlining the implementation plan
3. Planning context from the strategic planning phase
4. Technical analysis from the analysis phase

Your task is to generate complete, working, production-ready code that implements the research paper's methodology.

Requirements:
1. **Complete Implementation**: Generate all necessary code files, classes, and functions
2. **Production Quality**: Code should be clean, well-documented, and follow best practices
3. **Modular Design**: Create a well-structured, modular codebase
4. **Documentation**: Include comprehensive docstrings and comments
5. **Testing**: Include unit tests and example usage
6. **Dependencies**: Clearly specify all required dependencies

Code Structure:
- Main implementation files
- Configuration files
- Test files
- README with usage instructions
- Requirements/dependencies file

Focus on:
- Correctness and accuracy to the paper
- Code readability and maintainability
- Performance optimization where applicable
- Error handling and edge cases
- Comprehensive documentation"""
    }
    
    # 用户消息
    user_content = f"""Please implement the complete codebase for this research paper:

**Paper Content:**
{json.dumps(paper_content) if paper_format == 'JSON' else paper_content}

**Configuration:**
{config_yaml}

**Planning Context:**
{json.dumps(context_lst)}

**Technical Analysis:**
{json.dumps(analyzing_context)}

Please generate a complete, production-ready implementation with all necessary files, documentation, and tests."""
    
    user_msg = {'role': "user", 'content': user_content}
    
    # 添加消息到轨迹
    trajectories.extend([system_msg, user_msg])
    
    # 执行编码
    if progress_callback:
        progress_callback("开始代码生成...")
    
    try:
        # 调用API
        completion = api_call(trajectories, gpt_version, client)
        
        # 处理响应
        completion_json = json.loads(completion.model_dump_json())
        
        # 打印和记录
        if progress_callback:
            progress_callback("处理代码生成结果...")
        
        print_response(completion_json)
        temp_total_accumulated_cost = print_log_cost(completion_json, gpt_version, current_stage, output_dir, total_accumulated_cost)
        total_accumulated_cost = temp_total_accumulated_cost
        
        responses.append(completion_json)
        
        # 添加响应到轨迹
        message = completion.choices[0].message
        trajectories.append({'role': message.role, 'content': message.content})
        
        # 提取并保存代码
        if progress_callback:
            progress_callback("提取和保存生成的代码...")
        
        code_content = message.content
        extracted_files = extract_code_from_content(code_content, output_repo_dir)
        
    except Exception as e:
        error_msg = f"编码阶段API调用失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    # 保存结果
    if progress_callback:
        progress_callback("保存编码结果...")
    
    try:
        # 保存累积成本
        save_accumulated_cost(f"{output_dir}/accumulated_cost.json", total_accumulated_cost)
        
        # 保存响应
        with open(f'{output_dir}/coding_response.json', 'w', encoding='utf-8') as f:
            json.dump(responses, f, ensure_ascii=False, indent=2)
        
        # 保存轨迹
        with open(f'{output_dir}/coding_trajectories.json', 'w', encoding='utf-8') as f:
            json.dump(trajectories, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        error_msg = f"保存编码结果失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    if progress_callback:
        progress_callback("编码阶段完成！")
    
    return {
        "success": True,
        "responses": responses,
        "trajectories": trajectories,
        "total_cost": total_accumulated_cost,
        "extracted_files": extracted_files if 'extracted_files' in locals() else [],
        "output_files": [
            f"{output_dir}/coding_response.json",
            f"{output_dir}/coding_trajectories.json",
            f"{output_dir}/accumulated_cost.json"
        ],
        "output_repo_dir": output_repo_dir
    }

# 为了向后兼容，保留命令行接口
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--paper_name', type=str, required=True)
    parser.add_argument('--gpt_version', type=str, default="o3-mini")
    parser.add_argument('--paper_format', type=str, default="JSON", choices=["JSON", "LaTeX"])
    parser.add_argument('--pdf_json_path', type=str)
    parser.add_argument('--pdf_latex_path', type=str)
    parser.add_argument('--output_dir', type=str, default="")
    parser.add_argument('--output_repo_dir', type=str, default="")
    
    args = parser.parse_args()
    
    def print_progress(msg):
        print(f"[CODING] {msg}")
    
    try:
        result = run_coding(
            paper_name=args.paper_name,
            gpt_version=args.gpt_version,
            pdf_json_path=args.pdf_json_path,
            pdf_latex_path=args.pdf_latex_path,
            paper_format=args.paper_format,
            output_dir=args.output_dir,
            output_repo_dir=args.output_repo_dir,
            progress_callback=print_progress
        )
        print("编码阶段成功完成！")
        print(f"代码已保存到: {result['output_repo_dir']}")
    except Exception as e:
        print(f"编码阶段失败: {e}")
        sys.exit(1)
