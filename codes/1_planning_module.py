"""
模块化的论文规划功能
将1_planning.py改造为可以直接调用的函数
"""

from openai import OpenAI
import json
import os
import sys
from typing import Optional, Dict, Any, List, Callable
from utils import print_response, print_log_cost, load_accumulated_cost, save_accumulated_cost

def create_openai_client():
    """创建OpenAI客户端"""
    return OpenAI(
        api_key="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",
        base_url="https://openrouter.ai/api/v1",
        timeout=6000
    )

def load_paper_content(paper_format: str, pdf_json_path: Optional[str] = None, pdf_latex_path: Optional[str] = None):
    """加载论文内容"""
    if paper_format == "JSON":
        if not pdf_json_path:
            raise ValueError("JSON格式需要提供pdf_json_path参数")
        with open(pdf_json_path) as f:
            return json.load(f)
    elif paper_format == "LaTeX":
        if not pdf_latex_path:
            raise ValueError("LaTeX格式需要提供pdf_latex_path参数")
        with open(pdf_latex_path) as f:
            return f.read()
    else:
        raise ValueError("无效的论文格式。请选择 'JSON' 或 'LaTeX'")

def create_planning_messages(paper_format: str, paper_content: Any) -> List[Dict[str, str]]:
    """创建规划阶段的消息"""
    plan_msg = [
        {'role': "system", "content": f"""You are an expert researcher and strategic planner with a deep understanding of experimental design and reproducibility in scientific research. 
You will receive a research paper in {paper_format} format. 
Your task is to create a detailed and efficient plan to reproduce the experiments and methodologies described in the paper.
This plan should align precisely with the paper's methodology, experimental setup, and evaluation metrics. 

Instructions:
1. Analyze the paper thoroughly to understand its core contributions, methodology, and experimental setup.
2. Create a comprehensive plan that includes:
   - Overview of the paper's main contributions
   - Detailed breakdown of the methodology
   - Step-by-step implementation plan
   - Required datasets and resources
   - Evaluation metrics and benchmarks
   - Expected outcomes and validation methods

3. Structure your response in three parts:
   - **Overview**: High-level summary of what needs to be implemented
   - **Detailed Plan**: Step-by-step breakdown of the implementation
   - **Implementation Requirements**: Technical requirements, dependencies, and resources needed

4. Be specific about:
   - Algorithms and models to implement
   - Data preprocessing steps
   - Training procedures
   - Evaluation protocols
   - Performance metrics

5. Consider practical aspects:
   - Computational requirements
   - Time estimates for each phase
   - Potential challenges and solutions
   - Alternative approaches if needed

Your plan should be actionable and detailed enough for a skilled developer to follow and reproduce the paper's results."""},
        {'role': "user", "content": f"Please analyze this research paper and create a comprehensive implementation plan:\n\n{json.dumps(paper_content) if paper_format == 'JSON' else paper_content}"}
    ]
    
    return plan_msg

def api_call(messages: List[Dict[str, str]], gpt_version: str, client: OpenAI):
    """调用OpenAI API"""
    try:
        completion = client.chat.completions.create(
            model=gpt_version,
            messages=messages,
            temperature=0.7,
            max_tokens=4000
        )
        return completion
    except Exception as e:
        print(f"API调用失败: {e}")
        raise

def run_planning(
    paper_name: str,
    gpt_version: str,
    pdf_json_path: Optional[str] = None,
    pdf_latex_path: Optional[str] = None,
    paper_format: str = "JSON",
    output_dir: str = "",
    progress_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    运行论文规划阶段
    
    Args:
        paper_name: 论文名称
        gpt_version: GPT版本
        pdf_json_path: JSON格式论文路径
        pdf_latex_path: LaTeX格式论文路径
        paper_format: 论文格式 ("JSON" 或 "LaTeX")
        output_dir: 输出目录
        progress_callback: 进度回调函数
        
    Returns:
        包含处理结果的字典
    """
    
    if progress_callback:
        progress_callback("初始化规划阶段...")
    
    # 创建OpenAI客户端
    client = create_openai_client()
    
    # 加载论文内容
    if progress_callback:
        progress_callback("加载论文内容...")
    
    try:
        paper_content = load_paper_content(paper_format, pdf_json_path, pdf_latex_path)
    except Exception as e:
        error_msg = f"加载论文内容失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise ValueError(error_msg)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载累积成本
    total_accumulated_cost = load_accumulated_cost(f"{output_dir}/accumulated_cost.json")
    
    # 创建规划消息
    if progress_callback:
        progress_callback("创建规划提示...")
    
    plan_msg = create_planning_messages(paper_format, paper_content)
    
    # 初始化对话轨迹和响应
    trajectories = []
    responses = []
    current_stage = "planning"
    
    # 执行规划对话
    if progress_callback:
        progress_callback("开始与GPT对话进行规划...")
    
    # 添加初始消息到轨迹
    trajectories.extend(plan_msg)
    
    try:
        # 调用API
        completion = api_call(trajectories, gpt_version, client)
        
        # 处理响应
        completion_json = json.loads(completion.model_dump_json())
        
        # 打印和记录
        if progress_callback:
            progress_callback("处理API响应...")
        
        print_response(completion_json)
        temp_total_accumulated_cost = print_log_cost(completion_json, gpt_version, current_stage, output_dir, total_accumulated_cost)
        total_accumulated_cost = temp_total_accumulated_cost
        
        responses.append(completion_json)
        
        # 添加响应到轨迹
        message = completion.choices[0].message
        trajectories.append({'role': message.role, 'content': message.content})
        
    except Exception as e:
        error_msg = f"API调用失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    # 保存结果
    if progress_callback:
        progress_callback("保存规划结果...")
    
    try:
        # 保存累积成本
        save_accumulated_cost(f"{output_dir}/accumulated_cost.json", total_accumulated_cost)
        
        # 保存响应
        with open(f'{output_dir}/planning_response.json', 'w', encoding='utf-8') as f:
            json.dump(responses, f, ensure_ascii=False, indent=2)
        
        # 保存轨迹
        with open(f'{output_dir}/planning_trajectories.json', 'w', encoding='utf-8') as f:
            json.dump(trajectories, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        error_msg = f"保存结果失败: {e}"
        if progress_callback:
            progress_callback(error_msg)
        raise RuntimeError(error_msg)
    
    if progress_callback:
        progress_callback("规划阶段完成！")
    
    return {
        "success": True,
        "responses": responses,
        "trajectories": trajectories,
        "total_cost": total_accumulated_cost,
        "output_files": [
            f"{output_dir}/planning_response.json",
            f"{output_dir}/planning_trajectories.json",
            f"{output_dir}/accumulated_cost.json"
        ]
    }

# 为了向后兼容，保留命令行接口
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--paper_name', type=str, required=True)
    parser.add_argument('--gpt_version', type=str, required=True)
    parser.add_argument('--paper_format', type=str, default="JSON", choices=["JSON", "LaTeX"])
    parser.add_argument('--pdf_json_path', type=str)
    parser.add_argument('--pdf_latex_path', type=str)
    parser.add_argument('--output_dir', type=str, default="")
    
    args = parser.parse_args()
    
    def print_progress(msg):
        print(f"[PLANNING] {msg}")
    
    try:
        result = run_planning(
            paper_name=args.paper_name,
            gpt_version=args.gpt_version,
            pdf_json_path=args.pdf_json_path,
            pdf_latex_path=args.pdf_latex_path,
            paper_format=args.paper_format,
            output_dir=args.output_dir,
            progress_callback=print_progress
        )
        print("规划阶段成功完成！")
    except Exception as e:
        print(f"规划阶段失败: {e}")
        sys.exit(1)
