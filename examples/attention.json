{"paper_id": "Attention Is All You Need", "title": "Attention Is All You Need", "abstract": "The dominant sequence transduction models are based on complex recurrent or\nconvolutional neural networks that include an encoder and a decoder. The best\nperforming models also connect the encoder and decoder through an attention\nmechanism. We propose a new simple network architecture, the Transformer,\nbased solely on attention mechanisms, dispensing with recurrence and convolutions\nentirely. Experiments on two machine translation tasks show these models to\nbe superior in quality while being more parallelizable and requiring significantly\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 Englishto-German translation task, improving over the existing best results, including\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\nbest models from the literature. We show that the Transformer generalizes well to\nother tasks by applying it successfully to English constituency parsing both with\nlarge and limited training data.\n", "pdf_parse": {"paper_id": "Attention Is All You Need", "body_text": [{"text": "Recurrent neural networks, long short-term memory [13] and gated recurrent [7] neural networks\nin particular, have been firmly established as state of the art approaches in sequence modeling and\ntransduction problems such as language modeling and machine translation [35, 2, 5]. Numerous\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\narchitectures [38, 24, 15].\nRecurrent models typically factor computation along the symbol positions of the input and output\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\nstates ht, as a function of the previous hidden state ht−1 and the input for position t. This inherently\nsequential nature precludes parallelization within training examples, which becomes critical at longer\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved\nsignificant improvements in computational efficiency through factorization tricks [21] and conditional\ncomputation [32], while also improving model performance in case of the latter. The fundamental\nconstraint of sequential computation, however, remains.\nAttention mechanisms have become an integral part of compelling sequence modeling and transduction models in various tasks, allowing modeling of dependencies without regard to their distance in\nthe input or output sequences [2, 19]. In all but a few cases [27], however, such attention mechanisms\nare used in conjunction with a recurrent network.\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.", "section": "Introduction", "sec_num": "1"}, {"text": "The goal of reducing sequential computation also forms the foundation of the Extended Neural GPU\n[16], ByteNet [18] and ConvS2S [9], all of which use convolutional neural networks as basic building\nblock, computing hidden representations in parallel for all input and output positions. In these models,\nthe number of operations required to relate signals from two arbitrary input or output positions grows\nin the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes\nit more difficult to learn dependencies between distant positions [12]. In the Transformer this is\nreduced to a constant number of operations, albeit at the cost of reduced effective resolution due\nto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as\ndescribed in section 3.2.\nSelf-attention, sometimes called intra-attention is an attention mechanism relating different positions\nof a single sequence in order to compute a representation of the sequence. Self-attention has been\nused successfully in a variety of tasks including reading comprehension, abstractive summarization,\ntextual entailment and learning task-independent sentence representations [4, 27, 28, 22].\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequencealigned recurrence and have been shown to perform well on simple-language question answering and\nlanguage modeling tasks [34].\nTo the best of our knowledge, however, the Transformer is the first transduction model relying\nentirely on self-attention to compute representations of its input and output without using sequencealigned RNNs or convolution. In the following sections, we will describe the Transformer, motivate\nself-attention and discuss its advantages over models such as [17, 18] and [9].\n", "section": "Background", "sec_num": "2"}, {"text": "Most competitive neural sequence transduction models have an encoder-decoder structure [5, 2, 35].\nHere, the encoder maps an input sequence of symbol representations (x1, ..., xn) to a sequence\nof continuous representations z = (z1, ..., zn). Given z, the decoder then generates an output\nsequence (y1, ..., ym) of symbols one element at a time. At each step the model is auto-regressive\n[10], consuming the previously generated symbols as additional input when generating the next.\nThe Transformer follows this overall architecture using stacked self-attention and point-wise, fully\nconnected layers for", "section": "Model Architecture", "sec_num": "3"}, {"text": "Encoder: The encoder is composed of a stack of N = 6 identical layers. Each layer has two\nsub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, positionwise fully connected feed-forward network. We employ a residual connection [11] around each of\nthe two sub-layers, followed by layer normalization [1]. That is, the output of each sub-layer is\nLayerNorm(x + Sublayer(x)), where Sublayer(x) is the function implemented by the sub-layer\nitself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\nlayers, produce outputs of dimension dmodel = 512.", "section": "Encoder and Decoder Stacks", "sec_num": "3.1"}, {"text": "Decoder: The decoder is also composed of a stack of N = 6 identical layers. In addition to the two\nsub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head\nattention over the output of the encoder stack. Similar to the encoder, we employ residual connections\naround each of the sub-layers, followed by layer normalization. We also modify the self-attention\nsub-layer in the decoder stack to prevent positions from attending to subsequent positions. This\nmasking, combined with fact that the output embeddings are offset by one position, ensures that the\npredictions for position i can depend only on the known outputs at positions less than i.\n", "section": "Encoder and Decoder Stacks", "sec_num": "3.1"}, {"text": "An attention function can be described as mapping a query and a set of key-value pairs to an output,\nwhere the query, keys, values, and output are all vectors. The output is computed as a weighted sum of the values, where the weight assigned to each value is computed by a compatibility function of the\nquery with the corresponding key.", "section": "Attention", "sec_num": "3.2"}, {"text": "We call our particular attention \"Scaled Dot-Product Attention\" (Figure 2). The input consists of\nqueries and keys of dimension dk, and values of dimension dv. We compute the dot products of the\nquery with all keys, divide each by √\ndk, and apply a softmax function to obtain the weights on the\nvalues.\nIn practice, we compute the attention function on a set of queries simultaneously, packed together\ninto a matrix Q. The keys and values are also packed together into matrices K and V . We compute\nthe matrix of outputs as:\nAttention(Q, K, V ) = softmax(QKT\n√\ndk\n)V (1)\nThe two most commonly used attention functions are additive attention [2], and dot-product (multiplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor\nof √\n1\ndk\n. Additive attention computes the compatibility function using a feed-forward network with\na single hidden layer. While the two are similar in theoretical complexity, dot-product attention is\nmuch faster and more space-efficient in practice, since it can be implemented using highly optimized\nmatrix multiplication code.\nWhile for small values of dk the two mechanisms perform similarly, additive attention outperforms\ndot product attention without scaling for larger values of dk [3]. We suspect that for large values of\ndk, the dot products grow large in magnitude, pushing the softmax function into regions where it has\nextremely small gradients 4\n. To counteract this effect, we scale the dot products by √\n1\ndk\n.", "section": "Scaled Dot-Product Attention\n", "sec_num": "3.2.1"}, {"text": "Instead of performing a single attention function with dmodel-dimensional keys, values and queries,\nwe found it beneficial to linearly project the queries, keys and values h times with different, learned\nlinear projections to dk, dk and dv dimensions, respectively. On each of these projected versions of\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\n4To illustrate why the dot products get large, assume that the components of q and k are independent random\nvariables with mean 0 and variance 1. Then their dot product, q · k =\nPdk\ni=1 qiki, has mean 0 and variance dk.\n4\noutput values. These are concatenated and once again projected, resulting in the final values, as\ndepicted in Figure 2.\nMulti-head attention allows the model to jointly attend to information from different representation\nsubspaces at different positions. With a single attention head, averaging inhibits this.\nMultiHead(Q, K, V ) = Concat(head1, ..., headh)WO\nwhere headi = Attention(QWQ\ni\n, KW K\ni\n, V WV\ni\n)\nWhere the projections are parameter matrices W\nQ\ni ∈ R\ndmodel×dk , W K\ni ∈ R\ndmodel×dk , WV\ni ∈ R\ndmodel×dv\nand WO ∈ R\nhdv×dmodel\n.\nIn this work we employ h = 8 parallel attention layers, or heads. For each of these we use\ndk = dv = dmodel/h = 64. Due to the reduced dimension of each head, the total computational cost\nis similar to that of single-head attention with full dimensionality", "section": "Multi-Head Attention", "sec_num": "3.2.2"}, {"text": "The Transformer uses multi-head attention in three different ways:\n• In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,\nand the memory keys and values come from the output of the encoder. This allows every\nposition in the decoder to attend over all positions in the input sequence. This mimics the\ntypical encoder-decoder attention mechanisms in sequence-to-sequence models such as\n[38, 2, 9].\n• The encoder contains self-attention layers. In a self-attention layer all of the keys, values\nand queries come from the same place, in this case, the output of the previous layer in the\nencoder. Each position in the encoder can attend to all positions in the previous layer of the\nencoder.\n• Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\nall positions in the decoder up to and including that position. We need to prevent leftward\ninformation flow in the decoder to preserve the auto-regressive property. We implement this\ninside of scaled dot-product attention by masking out (setting to −∞) all values in the input\nof the softmax which correspond to illegal connections. See Figure 2.\n", "section": "Applications of Attention in our Model", "sec_num": "3.2.3"}, {"text": "In addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully\nconnected feed-forward network, which is applied to each position separately and identically. This\nconsists of two linear transformations with a ReLU activation in between.\nFFN(x) = max(0, xW1 + b1)W2 + b2 (2)\nWhile the linear transformations are the same across different positions, they use different parameters\nfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1.\nThe dimensionality of input and output is dmodel = 512, and the inner-layer has dimensionality\ndf f = 2048.", "section": "Position-wise Feed-Forward Networks\n", "sec_num": "3.3"}, {"text": "Similarly to other sequence transduction models, we use learned embeddings to convert the input\ntokens and output tokens to vectors of dimension dmodel. We also use the usual learned linear transformation and softmax function to convert the decoder output to predicted next-token probabilities. In\nour model, we share the same weight matrix between the two embedding layers and the pre-softmax\nlinear transformation, similar to [30]. In the embedding layers, we multiply those weights by √\ndmodel.\n5\nTable 1: Maximum path lengths, per-layer complexity and minimum number of sequential operations\nfor different layer types. n is the sequence length, d is the representation dimension, k is the kernel\nsize of convolutions and r the size of the neighborhood in restricted self-attention", "section": "Embeddings and Softmax", "sec_num": "3.4"}, {"text": "Since our model contains no recurrence and no convolution, in order for the model to make use of the\norder of the sequence, we must inject some information about the relative or absolute position of the\ntokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel\nas the embeddings, so that the two can be summed. There are many choices of positional encodings,\nlearned and fixed [9].\nIn this work, we use sine and cosine functions of different frequencies:\nP E(pos,2i) = sin(pos/100002i/dmodel)\nP E(pos,2i+1) = cos(pos/100002i/dmodel)\nwhere pos is the position and i is the dimension. That is, each dimension of the positional encoding\ncorresponds to a sinusoid. The wavelengths form a geometric progression from 2π to 10000 · 2π. We\nchose this function because we hypothesized it would allow the model to easily learn to attend by\nrelative positions, since for any fixed offset k, P Epos+k can be represented as a linear function of\nP Epos.\nWe also experimented with using learned positional embeddings [9] instead, and found that the two\nversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version\nbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered\nduring training.\n", "section": "Positional Encoding", "sec_num": "3.5"}, {"text": "In this section we compare various aspects of self-attention layers to the recurrent and convolutional layers commonly used for mapping one variable-length sequence of symbol representations\n(x1, ..., xn) to another sequence of equal length (z1, ..., zn), with xi\n, zi ∈ R\nd\n, such as a hidden\nlayer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we\nconsider three desiderata.\nOne is the total computational complexity per layer. Another is the amount of computation that can\nbe parallelized, as measured by the minimum number of sequential operations required.\nThe third is the path length between long-range dependencies in the network. Learning long-range\ndependencies is a key challenge in many sequence transduction tasks. One key factor affecting the\nability to learn such dependencies is the length of the paths forward and backward signals have to\ntraverse in the network. The shorter these paths between any combination of positions in the input\nand output sequences, the easier it is to learn long-range dependencies [12]. Hence we also compare\nthe maximum path length between any two input and output positions in networks composed of the\ndifferent layer types.\nAs noted in Table 1, a self-attention layer connects all positions with a constant number of sequentially\nexecuted operations, whereas a recurrent layer requires O(n) sequential operations. In terms of\ncomputational complexity, self-attention layers are faster than recurrent layers when the sequence\n6\nlength n is smaller than the representation dimensionality d, which is most often the case with\nsentence representations used by state-of-the-art models in machine translations, such as word-piece\n[38] and byte-pair [31] representations. To improve computational performance for tasks involving\nvery long sequences, self-attention could be restricted to considering only a neighborhood of size r in\nthe input sequence centered around the respective output position. This would increase the maximum\npath length to O(n/r). We plan to investigate this approach further in future work.\nA single convolutional layer with kernel width k < n does not connect all pairs of input and output\npositions. Doing so requires a stack of O(n/k) convolutional layers in the case of contiguous kernels,\nor O(logk(n)) in the case of dilated convolutions [18], increasing the length of the longest paths\nbetween any two positions in the network. Convolutional layers are generally more expensive than\nrecurrent layers, by a factor of k. Separable convolutions [6], however, decrease the complexity\nconsiderably, to O(k · n · d + n · d\n2\n). Even with k = n, however, the complexity of a separable\nconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,\nthe approach we take in our model.\nAs side benefit, self-attention could yield more interpretable models. We inspect attention distributions\nfrom our models and present and discuss examples in the appendix. Not only do individual attention\nheads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic\nand semantic structure of the sentences.\n", "section": "Why Self-Attention", "sec_num": "4"}, {"text": "This section describes the training regime for our models.\n", "section": "Training", "sec_num": "5"}, {"text": "We trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million\nsentence pairs. Sentences were encoded using byte-pair encoding [3], which has a shared sourcetarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT\n2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece\nvocabulary [38]. Sentence pairs were batched together by approximate sequence length. Each training\nbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000\ntarget tokens.\n", "section": "Training Data and Batching", "sec_num": "5.1"}, {"text": "We trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We\ntrained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the\nbottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps\n(3.5 days).", "section": "Hardware and Schedule", "sec_num": "5.2"}, {"text": "We used the Adam optimizer [20] with β1 = 0.9, β2 = 0.98 and ϵ = 10−9\n. We varied the learning\nrate over the course of training, according to the formula:\nlrate = d\n−0.5\nmodel · min(step_num−0.5\n, step_num · warmup_steps−1.5\n) (3)\nThis corresponds to increasing the learning rate linearly for the first warmup_steps training steps,\nand decreasing it thereafter proportionally to the inverse square root of the step number. We used\nwarmup_steps = 4000.", "section": "Optimizer", "sec_num": "5.3"}, {"text": "We employ three types of regularization during training:\n7\nTable 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on the\nEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.", "section": "Regularization", "sec_num": "5.4"}, {"text": "| Model                          | BLEU EN-DE | BLEU EN-FR | Training Cost (FLOPs) EN-DE | Training Cost (FLOPs) EN-FR |\n|--------------------------------|------------|------------|-----------------------------|-----------------------------|\n| ByteNet [18]                   | 23.75      |            |                             |                             |\n| Deep-Att + PosUnk [39]         |            | 39.2       |                             | 1.0e20                      |\n| GNMT + RL [38]                 | 24.6       | 39.92      | 2.3e19                      | 1.4e20                      |\n| ConvS2S [9]                    | 25.16      | 40.46      | 9.6e18                      | 1.5e20                      |\n| MoE [32]                       | 26.03      | 40.56      | 2.0e19                      | 1.2e20                      |\n| Deep-Att + PosUnk Ensemble [39]|            | 40.4       |                             | 8.0e20                      |\n| GNMT + RL Ensemble [38]        | 26.30      | 41.16      | 1.8e20                      | 1.1e21                      |\n| ConvS2S Ensemble [9]           | 26.36      | 41.29      | 7.7e19                      | 1.2e21                      |\n| Transformer (base model)       | 27.3       | 38.1       | 3.3e18                      |                             |\n| Transformer (big)              | 28.4       | 41.8       | 2.3e19  ", "section": "Regularization", "sec_num": "5.4"}, {"text": "Residual Dropout We apply dropout [33] to the output of each sub-layer, before it is added to the\nsub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the\npositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\nPdrop = 0.1.\nLabel Smoothing During training, we employed label smoothing of value ϵls = 0.1 [36]. This\nhurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.\n", "section": "Regularization", "sec_num": "5.4"}, {"text": "On the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big)\nin Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0\nBLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is\nlisted in the bottom line of Table 3. Training took 3.5 days on 8 P100 GPUs. Even our base model\nsurpasses all previously published models and ensembles, at a fraction of the training cost of any of\nthe competitive models.\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,\noutperforming all of the previously published single models, at less than 1/4 the training cost of the\nprevious state-of-the-art model. The Transformer (big) model trained for English-to-French used\ndropout rate Pdrop = 0.1, instead of 0.3.\nFor the base models, we used a single model obtained by averaging the last 5 checkpoints, which\nwere written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We\nused beam search with a beam size of 4 and length penalty α = 0.6 [38]. These hyperparameters\nwere chosen after experimentation on the development set. We set the maximum output length during\ninference to input length + 50, but terminate early when possible [38].\nTable 2 summarizes our results and compares our translation quality and training costs to other model\narchitectures from the literature. We estimate the number of floating point operations used to train a\nmodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained\nsingle-precision floating-point capacity of each GPU 5\n.", "section": "Results-Machine Translation", "sec_num": "6.1"}, {"text": "To evaluate the importance of different components of the Transformer, we varied our base model\nin different ways, measuring the change in performance on English-to-German translation on the development set, newstest2013. We used beam search as described in the previous section, but no\ncheckpoint averaging. We present these results in Table 3.\nIn Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,\nkeeping the amount of computation constant, as described in Section 3.2.2. While single-head\nattention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.\nIn Table 3 rows (B), we observe that reducing the attention key size dk hurts model quality. This\nsuggests that determining compatibility is not easy and that a more sophisticated compatibility\nfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,\nbigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our\nsinusoidal positional encoding with learned positional embeddings [9], and observe nearly identical\nresults to the base model.", "section": "Results-Model Variations", "sec_num": "6.2"}, {"text": "Table 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base\nmodel. All metrics are on the English-to-German translation development set, newstest2013. Listed\nperplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to\nper-word perplexities.\n", "section": "Results-Model Variations", "sec_num": "6.2"}, {"text": "| Group   | N   | d_model | d_ff   | h   | d_k  | d_v  | P_drop | eps_ls | train steps | PPL (dev) | BLEU (dev) | params x10^6 | Notes                                           |\n|:--------|:----|:--------|:-------|:----|:-----|:-----|:-------|:-------|:------------|:----------|:-----------|:-------------|:------------------------------------------------|\n| base    | 6   | 512     | 2048   | 8   | 64   | 64   | 0.1    | 0.1    | 100K        | 4.92      | 25.8       | 65           |                                                 |\n| (A)     | 6   | 512     | 2048   | 1   | 512  | 512  | 0.1    | 0.1    | 100K        | 5.29      | 24.9       |              | Vary h, d_k, d_v                                |\n| (A)     | 6   | 512     | 2048   | 4   | 128  | 128  | 0.1    | 0.1    | 100K        | 5.00      | 25.5       |              | Vary h, d_k, d_v                                |\n| (A)     | 6   | 512     | 2048   | 16  | 32   | 32   | 0.1    | 0.1    | 100K        | 4.91      | 25.8       |              | Vary h, d_k, d_v                                |\n| (A)     | 6   | 512     | 2048   | 32  | 16   | 16   | 0.1    | 0.1    | 100K        | 5.01      | 25.4       |              | Vary h, d_k, d_v                                |\n| (B)     | 6   | 512     | 2048   | 16  | 32   | 32   | 0.1    | 0.1    | 100K        | 5.16      | 25.1       | 58           | Vary h (d_k, d_v derived)                       |\n| (B)     | 6   | 512     | 2048   | 32  | 16   | 16   | 0.1    | 0.1    | 100K        | 5.01      | 25.4       | 60           | Vary h (d_k, d_v derived)                       |\n| (C)     | 2   | 512     | 2048   | 8   | 64   | 64   | 0.1    | 0.1    | 100K        | 6.11      | 23.7       | 36           | Vary N (base params for d_model etc.)           |\n| (C)     | 4   | 512     | 2048   | 8   | 64   | 64   | 0.1    | 0.1    | 100K        | 5.19      | 25.3       | 50           | Vary N (base params for d_model etc.)           |\n| (C)     | 8   | 512     | 2048   | 8   | 64   | 64   | 0.1    | 0.1    | 100K        | 4.88      | 25.5       | 80           | Vary N (base params for d_model etc.)           |\n| (C)     | 6   | 256     | 1024   | 8   | 32   | 32   | 0.1    | 0.1    | 100K        | 5.75      | 24.5       | 28           | Vary d_model, d_ff, d_k, d_v (N=base, h derived)|\n| (C)     | 6   | 1024    | 4096   | 8   | 128  | 128  | 0.1    | 0.1    | 100K        | 4.66      | 26.0       | 168          | Vary d_model, d_ff, d_k, d_v (N=base, h derived)|\n| (C)     | 6   | 512     | 1024   | 8   | 64   | 64   | 0.1    | 0.1    | 100K        | 5.12      | 25.4       | 53           | Vary d_ff (base params for d_model etc.)        |\n| (C)     | 6   | 512     | 4096   | 8   | 64   | 64   | 0.1    | 0.1    | 100K        | 4.75      | 26.2       | 90           | Vary d_ff (base params for d_model etc.)        |\n| (D)     | 6   | 512     | 2048   | 8   | 64   | 64   | 0.0    | 0.1    | 100K        | 5.77      | 24.6       |              | P_drop=0.0 (base other params)                  |\n| (D)     | 6   | 512     | 2048   | 8   | 64   | 64   | 0.2    | 0.1    | 100K        | 4.95      | 25.5       |              | P_drop=0.2 (base other params)                  |\n| (D)     | 6   | 512     | 2048   | 8   | 64   | 64   | 0.1    | 0.0    | 100K        | 4.67      | 25.3       |              | eps_ls=0.0 (base other params)                  |\n| (D)     | 6   | 512     | 2048   | 8   | 64   | 64   | 0.1    | 0.2    | 100K        | 5.47      | 25.7       |              | eps_ls=0.2 (base other params)                  |\n| big (E) | 6   | 1024    | 4096   | 16  | 64   | 64   | 0.3    |        | 300K        | **4.33** | **26.4** | 213          | positional embedding instead of sinusoids       |", "section": "Table 3", "sec_num": "6.2"}, {"text": "To evaluate if the Transformer can generalize to other tasks we performed experiments on English\nconstituency parsing. This task presents specific challenges: the output is subject to strong structural\nconstraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\nmodels have not been able to attain state-of-the-art results in small-data regimes [37].\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the\nPenn Treebank [25], about 40K training sentences. We also trained it in a semi-supervised setting,\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences\n[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens\nfor the semi-supervised setting.\nWe performed only a small number of experiments to select the dropout, both attention and residual\n(section 5.4), learning rates and beam size on the Section 22 development set, all other parameters\nremained unchanged from the English-to-German base translation model. During inference, we increased the maximum output length to input length + 300. We used a beam size of 21 and α = 0.3\nfor both WSJ only and the semi-supervised setting.\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs surprisingly well, yielding better results than all previously reported models with the exception of the\nRecurrent Neural Network Grammar [8].\nIn contrast to RNN sequence-to-sequence models [37], the Transformer outperforms the BerkeleyParser [29] even when training only on the WSJ training set of 40K sentences.\n", "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "Table 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23\nof WSJ)", "section": "English Constituency Parsing", "sec_num": "6.3"}, {"text": "| Parser                       | Training                 | WSJ 23 F1 |\n|------------------------------|--------------------------|-----------|\n| <PERSON><PERSON><PERSON> & Kaiser et al. (2014) [37] | WSJ only, discriminative | 88.3      |\n| <PERSON><PERSON> et al. (2006) [29]    | WSJ only, discriminative | 90.4      |\n| <PERSON> et al. (2013) [40]       | WSJ only, discriminative | 90.4      |\n| <PERSON> et al. (2016) [8]       | WSJ only, discriminative | 91.7      |\n| Transformer (4 layers)       | WSJ only, discriminative | 91.3      |\n| <PERSON> et al. (2013) [40]       | semi-supervised          | 91.3      |\n| <PERSON> (2009) [14]   | semi-supervised          | 91.3      |\n| <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2006) [26]  | semi-supervised          | 92.1      |\n| <PERSON><PERSON><PERSON> & Kaiser et al. (2014) [37] | semi-supervised          | 92.1      |\n| Transformer (4 layers)       | semi-supervised          | 92.7      |\n| <PERSON><PERSON> et al. (2015) [23]     | multi-task               | 93.0      |\n| <PERSON> et al. (2016) [8]       | generative               | 93.3      |", "section": "Table 4", "sec_num": "6.3"}, {"text": "In this work, we presented the Transformer, the first sequence transduction model based entirely on\nattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with\nmulti-headed self-attention.\nFor translation tasks, the Transformer can be trained significantly faster than architectures based\non recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best\nmodel outperforms even all previously reported ensembles.\nWe are excited about the future of attention-based models and plan to apply them to other tasks. We\nplan to extend the Transformer to problems involving input and output modalities other than text and\nto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs\nsuch as images, audio and video. Making generation less sequential is another research goals of ours.\nThe code we used to train and evaluate our models is available at https://github.com/\ntensorflow/tensor2tensor.", "section": "Conclusion", "sec_num": "7"}]}}