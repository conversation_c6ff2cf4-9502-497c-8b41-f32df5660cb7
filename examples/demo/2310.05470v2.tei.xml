<?xml version="1.0" encoding="UTF-8"?>
<TEI xml:space="preserve" xmlns="http://www.tei-c.org/ns/1.0" 
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
xsi:schemaLocation="http://www.tei-c.org/ns/1.0 https://raw.githubusercontent.com/kermitt2/grobid/master/grobid-home/schemas/xsd/Grobid.xsd"
 xmlns:xlink="http://www.w3.org/1999/xlink">
	<teiHeader xml:lang="en">
		<fileDesc>
			<titleStmt>
				<title level="a" type="main">GENERATIVE JUDGE FOR EVALUATING ALIGNMENT</title>
			</titleStmt>
			<publicationStmt>
				<publisher/>
				<availability status="unknown"><licence/></availability>
				<date type="published" when="2023-12-07">7 Dec 2023</date>
			</publicationStmt>
			<sourceDesc>
				<biblStruct>
					<analytic>
						<author>
							<persName><forename type="first">Junlong</forename><surname>Li</surname></persName>
							<affiliation key="aff0">
								<orgName type="institution">Shanghai Jiao Tong University</orgName>
							</affiliation>
							<affiliation key="aff5">
								<orgName type="laboratory">Generative AI Research Lab (GAIR)</orgName>
							</affiliation>
						</author>
						<author>
							<persName><forename type="first">Shichao</forename><surname>Sun</surname></persName>
							<affiliation key="aff2">
								<orgName type="institution">Hong Kong Polytechnic University</orgName>
							</affiliation>
							<affiliation key="aff5">
								<orgName type="laboratory">Generative AI Research Lab (GAIR)</orgName>
							</affiliation>
						</author>
						<author>
							<persName><forename type="first">Weizhe</forename><surname>Yuan</surname></persName>
							<affiliation key="aff0">
								<orgName type="institution">Shanghai Jiao Tong University</orgName>
							</affiliation>
							<affiliation key="aff3">
								<orgName type="institution">New York University</orgName>
							</affiliation>
						</author>
						<author>
							<persName><forename type="first">Run-Ze</forename><surname>Fan</surname></persName>
							<affiliation key="aff0">
								<orgName type="institution">Shanghai Jiao Tong University</orgName>
							</affiliation>
							<affiliation key="aff1">
								<orgName type="laboratory">Shanghai Artificial Intelligence Laboratory</orgName>
							</affiliation>
							<affiliation key="aff4">
								<orgName type="department">Chinese Academy of Sciences</orgName>
							</affiliation>
							<affiliation key="aff5">
								<orgName type="laboratory">Generative AI Research Lab (GAIR)</orgName>
							</affiliation>
						</author>
						<author>
							<persName><forename type="first">Hai</forename><surname>Zhao</surname></persName>
							<affiliation key="aff0">
								<orgName type="institution">Shanghai Jiao Tong University</orgName>
							</affiliation>
							<affiliation key="aff1">
								<orgName type="laboratory">Shanghai Artificial Intelligence Laboratory</orgName>
							</affiliation>
						</author>
						<author>
							<persName><forename type="first">Pengfei</forename><surname>Liu</surname></persName>
							<affiliation key="aff0">
								<orgName type="institution">Shanghai Jiao Tong University</orgName>
							</affiliation>
							<affiliation key="aff1">
								<orgName type="laboratory">Shanghai Artificial Intelligence Laboratory</orgName>
							</affiliation>
							<affiliation key="aff2">
								<orgName type="institution">Hong Kong Polytechnic University</orgName>
							</affiliation>
							<affiliation key="aff3">
								<orgName type="institution">New York University</orgName>
							</affiliation>
							<affiliation key="aff4">
								<orgName type="department">Chinese Academy of Sciences</orgName>
							</affiliation>
							<affiliation key="aff5">
								<orgName type="laboratory">Generative AI Research Lab (GAIR)</orgName>
							</affiliation>
						</author>
						<title level="a" type="main">GENERATIVE JUDGE FOR EVALUATING ALIGNMENT</title>
					</analytic>
					<monogr>
						<imprint>
							<date type="published" when="2023-12-07">7 Dec 2023</date>
						</imprint>
					</monogr>
					<idno type="MD5">9FE8A3650C1CE66BF3E65B5E607FCD83</idno>
					<idno type="arXiv">arXiv:2310.05470v2[cs.CL]</idno>
				</biblStruct>
			</sourceDesc>
		</fileDesc>
		<encodingDesc>
			<appInfo>
				<application version="0.7.3" ident="GROBID" when="2025-05-21T13:25+0000">
					<desc>GROBID - A machine learning software for extracting information from scholarly documents</desc>
					<ref target="https://github.com/kermitt2/grobid"/>
				</application>
			</appInfo>
		</encodingDesc>
		<profileDesc>
			<abstract>
<div xmlns="http://www.tei-c.org/ns/1.0"><p>The rapid development of Large Language Models (LLMs) has substantially expanded the range of tasks they can address. In the field of Natural Language Processing (NLP), researchers have shifted their focus from conventional NLP tasks (e.g., sequence tagging and parsing) towards tasks that revolve around aligning with human needs (e.g., brainstorming and email writing). This shift in task distribution imposes new requirements on evaluating these aligned models regarding generality (i.e., assessing performance across diverse scenarios), flexibility (i.e., examining under different protocols), and interpretability (i.e., scrutinizing models with explanations). In this paper, we propose a generative judge with 13B parameters, AUTO-J, designed to address these challenges. Our model is trained on user queries and LLM-generated responses under massive real-world scenarios and accommodates diverse evaluation protocols (e.g., pairwise response comparison and single-response evaluation) with well-structured natural language critiques. To demonstrate the efficacy of our approach, we construct a new testbed covering 58 different scenarios. Experimentally, AUTO-J outperforms a series of strong competitors, including both open-source and closed-source models, by a large margin. We also provide detailed analysis and case studies to further reveal the potential of our method and make a variety of resources public at https://github.com/GAIR-NLP/auto-j.</p></div>
			</abstract>
		</profileDesc>
	</teiHeader>
	<text xml:lang="en">
		<body>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="1">INTRODUCTION</head><p>In natural language processing, the evaluation methodology for generation tasks is continually updating with the advancement of modeling techniques, ranging from ROUGE <ref type="bibr" target="#b20">(Lin, 2004)</ref> to <ref type="bibr">ROUGE-WE (Ng &amp; Abrecht, 2015)</ref> (a metric enhanced with word embedding <ref type="bibr" target="#b23">(Mikolov et al., 2013)</ref>) and then to BERTScore <ref type="bibr" target="#b46">(Zhang et al., 2019)</ref>, BARTScore <ref type="bibr" target="#b45">(Yuan et al., 2021)</ref>, and GPTScore <ref type="bibr" target="#b13">(Fu et al., 2023)</ref> (metrics enhanced by pre-trained language models <ref type="bibr" target="#b29">(Peters et al., 2018;</ref><ref type="bibr" target="#b10">Devlin et al., 2019;</ref><ref type="bibr" target="#b18">Lewis et al., 2020)</ref>), aiming for a more reliable evaluation for ever-growing modeling techniques. Recently, the advent of large language models <ref type="bibr" target="#b3">(Brown et al., 2020;</ref><ref type="bibr">Touvron et al., 2023a;</ref><ref type="bibr">b;</ref><ref type="bibr" target="#b7">Chowdhery et al., 2022)</ref> has not only reshaped the implementation approach for modeling techniques (i.e., paradigm shift from "pre-train, fine-tuning" to "pre-train, supervised fine-tune, and reward model-based tune" <ref type="bibr" target="#b50">(Ziegler et al., 2019;</ref><ref type="bibr">Stiennon et al., 2020;</ref><ref type="bibr" target="#b27">Ouyang et al., 2022)</ref>) but also broadened the spectrum of tasks that modeling techniques seek to address (i.e., task distribution shift from traditional NLP tasks towards those more aligned with human needs <ref type="bibr">(Bai et al., 2022a;</ref><ref type="bibr">OpenAI, 2023;</ref><ref type="bibr" target="#b49">Zhou et al., 2023;</ref><ref type="bibr" target="#b43">Xu et al., 2023;</ref><ref type="bibr" target="#b36">Taori et al., 2023;</ref><ref type="bibr" target="#b4">Bubeck et al., 2023)</ref>).</p><p>Given the evolving modeling techniques, the evaluation methods are in urgent need of upgrading and improvement to adapt to new challenges and requirements, particularly in the following aspects: (i) generality: the evaluation method should support massive real-world scenarios where gold references are usually unavailable. Traditional approaches frequently require human references and apply a single evaluation metric to constrained tasks (e.g., ROUGE <ref type="bibr" target="#b20">(Lin, 2004</ref>) for text summarization, BLEU <ref type="bibr" target="#b28">(Papineni et al., 2002)</ref> for machine translation) are struggling to keep pace with the current demands for evaluation. (ii) flexibility: the evaluation method should accommodate different protocols with desirable performance. The current LLM-based modeling paradigm requires methodological support of the evaluation in various aspects, and the evaluation protocols they demand also exhibit variations. For instance, when learning a reward model, it is necessary to compare two responses, while evaluating the final system output often involves assessing a single response <ref type="bibr">(Stiennon et al., 2020)</ref>.<ref type="foot" target="#foot_0">1</ref> (iii) interpretability: evaluation results are encouraged to provide more than solely numerical scores. Additional explanations are crucial to enhance the reliability of evaluation outcomes and facilitate humans' involvement in the evaluation loop <ref type="bibr" target="#b33">(Saunders et al., 2022)</ref>.</p><p>In this context, researchers have engaged in some preliminary explorations, with the central idea being to conceptualize evaluation as an instruction-following problem <ref type="bibr" target="#b13">(Fu et al., 2023;</ref><ref type="bibr" target="#b21">Liu et al., 2023)</ref> based on a high-capacity LLM. For example, <ref type="bibr" target="#b48">Zheng et al. (2023)</ref>; <ref type="bibr" target="#b49">Zhou et al. (2023)</ref>; <ref type="bibr" target="#b11">Dubois et al. (2023)</ref>; <ref type="bibr">Wang et al. (2023a)</ref> employ proprietary LLMs (e.g., ChatGPT, Claude or GPT-4) through API calls to perform various evaluation protocols. Such methods have shown decent agreement with human judgment, but they also face challenges in terms of consistency and reproducibility due to the opacity of API models as well as the high API cost. An alternative is to train a specialized evaluator based on open-source LLMs. PandaLM <ref type="bibr">(Wang et al., 2023c)</ref> is able to compare a pair of responses for a given query with a brief explanation of the evaluation process, and Shepherd <ref type="bibr">(Wang et al., 2023b)</ref> can provide critiques to a LLM's response to pinpoint its shortcomings. These models have achieved remarkable performance in certain settings; however, they are relatively limited in the following aspects: (a) Some are not optimized to evaluate various deployed LLMs under massive real-world scenarios but are only trained on synthetic data (e.g., the Alpaca dataset <ref type="bibr" target="#b36">(Taori et al., 2023)</ref> by , online forums, or traditional NLP datasets, without the consideration of scenariospecific evaluation criteria. (b) Each of these models only supports one evaluation protocol, like pairwise comparison or single-response evaluation, making them less flexible for various evaluation requirements. (c) They only provide brief or no natural language explanation for their evaluation, reducing the reliability of the result.</p><p>To address the above challenges, we develop AUTO-J, a generative judge with 13B parameters trained on user queries and model-generated responses from massive real-world scenarios. Methodologically, to train a more generalized judge, we created a new dataset from a large collection of data, encompassing 58 different scenarios, with most samples coming from real-world user queries and LLMs' responses. Based on the dataset, we guide <ref type="bibr">GPT-4 (OpenAI, 2023)</ref> with carefully hand-written criteria for each scenario to collect desired evaluation judgments as our supervised training signals and apply heuristic filtering strategies and post-processing methods to unify output formats and mitigate noise. We also design new testbeds from the above dataset for pairwise comparison and single-response evaluation, with a diverse and balanced scenario distribution ( §5.1). Through comprehensive meta-evaluation on its evaluation functionalities, we show that AUTO-J outperforms various strong baselines, including both open-source and closed-source models ( §6.1, §6.2, §6.3). We also conduct detailed analysis and case studies ( §6.4) to show a series of advantages offered by AUTO-J, from lessened positional bias in pairwise comparison, more specific critiques in single-response evaluation to the potential as a generative reward model to help improve base LLMs. To summarize, our contributions are:</p><p>(i) We develop AUTO-J, a new open-source model that can effectively and flexibly evaluate LLMs for both pairwise comparison and single-response assessment, with well-structured natural language critiques to support its evaluation. It establishes a new state-of-the-art performance among opensource models across all 58 scenarios (e.g., 8.9% improvement in pairwise evaluation in §6.1) and surpasses strong proprietary models such as ChatGPT and Claude-2 (e.g., with 12.1% and 12.4% gains in pairwise evaluation in §6.1) (ii) We construct a judgment dataset ( §3) that covers 58 real-world scenarios. Each judgment consists of both a numerical rating (or a pairwise comparison result) and a critique generated in accordance with our curated 332 criteria to support its evaluation. These data resources serve as a valuable foundation for both training and benchmarking evaluation methodologies under emerging technologies.</p><p>(iii) We have released a wealth of resources to meet the diverse needs for future research: out-ofthe-box models with superior performance; scenario typology and classifier; curated scenario-aware evaluation criteria and prompts; judgments with well-formatted critiques.</p><p>Figure <ref type="figure">1</ref>: An overview for our data construction pipeline in three steps.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2">RELATED WORK</head></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2.1">EVALUATION OF LLMS</head><p>It is universally known that the best way to evaluate LLMs is human judgment, but collecting human annotations can be costly, time-consuming, and laborious <ref type="bibr" target="#b27">(Ouyang et al., 2022;</ref><ref type="bibr" target="#b48">Zheng et al., 2023)</ref>. Using strong LLMs (usually closed-source ones, e.g., GPT-4, Claude, ChatGPT) as an automated proxy for assessing LLMs has become a natural choice <ref type="bibr" target="#b49">(Zhou et al., 2023)</ref>. With appropriate prompt design, the quality of evaluation and agreement to human judgment can be promising <ref type="bibr" target="#b11">(Dubois et al., 2023;</ref><ref type="bibr" target="#b48">Zheng et al., 2023;</ref><ref type="bibr" target="#b47">Zhang et al., 2023;</ref><ref type="bibr">Wang et al., 2023a)</ref>. However, the cost concern still exists when calling the APIs of these proprietary models, especially when there is a frequent need for model validation on large-scale data. Moreover, closed-source evaluation leads to low reproducibility due to potential changes in models behind the API. Some recent works have started to make attempts for open-source alternatives. SelFee <ref type="bibr" target="#b44">(Ye et al., 2023)</ref> collects generations, feedback, and revised generations from ChatGPT and fine-tunes LLaMA models to build a critique model. Shepherd <ref type="bibr">(Wang et al., 2023b</ref>) trains a model that can output critiques for single-response with the data of feedback from online communities and human annotation. PandaLM <ref type="bibr">(Wang et al., 2023c)</ref>  </p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2.2">META-EVALUATION TESTBED FOR LLM EVALUATORS</head><p>Besides the evaluators themselves, there is also a practical need to construct a comprehensive testbed to meta-evaluate them (i.e., assessing the quality of their evaluation). In <ref type="bibr" target="#b48">Zheng et al. (2023)</ref>, MTBench and Chatbot Arena Conversations are proposed. The former has only 80 human-crafted queries, each with several LLMs' responses and expert-level human annotation on pairwise comparison; the latter is a large collection of crowdsourced data, with more than 30K queries from real-world users and their vote on pairs of responses from different LLMs. FairEval <ref type="bibr">(Wang et al., 2023a)</ref> is based on the 80 queries from VicunaBench <ref type="bibr" target="#b6">(Chiang et al., 2023)</ref> with human annotated labels between ChatGPT and Vicuna responses. PandaLM <ref type="bibr">(Wang et al., 2023c)</ref> constructs a test set comprising 999 pairwise samples, with queries from 252 user-oriented instructions in <ref type="bibr" target="#b42">Wang et al. (2022)</ref>. LLMEval 2 <ref type="bibr" target="#b47">(Zhang et al., 2023)</ref> is much larger than the previous two, with 2,553 samples compiled from multiple data sources with human-annotated preferences. Shepherd <ref type="bibr">(Wang et al., 2023b)</ref> collects 352 samples from multiple sources for its critique model as a test set to evaluate the quality of the critiques.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="3">DATA CONSTRUCTION</head><p>We construct data from massive real-world scenarios with high-quality evaluation judgments for both training and testing. The data construction pipeline involves three main steps: (1) defining evaluation scenario and criteria, (2) collecting real-world queries and responses from different models for these scenarios and (3) generating desired evaluation judgments for different evaluation protocols. An overview of our data construction pipeline is shown in Fig. <ref type="figure">1</ref>.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Content Aspect</head><p>1. clarity: the written plan should clearly outline the objectives, tasks, and timeline ... 2. feasibility: the written plan should propose realistic and achievable steps and actions ... 3. creativity: the written plan should demonstrate creative thinking and innovative ideas ... 4. thoroughness: the written plan should cover all essential aspects and details of the event ...</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Basic Aspect</head><p>1. completeness of instruction following: for all key instructions (e.g., answer multiple ... 2. accuracy: all contents provided or mentioned in the response should be accurate ... 3. information richness: the response is encouraged to provide rich, detailed ... 1. structure: the written plan should be well structured , with a logical flow of ideas ... 2. layout: the written plan is encouraged to use headings, bullet points, lists, tables, or ... Criteria Besides the definition and description, we also design a set of criteria for each scenario that serves as a reference to guide models on how to do the evaluation. Each criterion has a name and a description. We show a condensed version of the set of criteria for the "planning" scenario in Fig. <ref type="figure" target="#fig_1">2</ref> (a) (the complete version is in Fig. <ref type="figure">9</ref>). Generally, criteria for each scenario consists of specific ones and basic ones (more general, shared by multiple scenarios). In total, we craft 332 different criteria. When we use a set of criteria, we put them in the system message for LLMs, as shown in Tab. 8.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="3.2">QUERIES AND RESPONSES COLLECTION</head><p>To start with, we first collect a large collection of data from the following sources: Chatbot Arena Conversations and MTBench <ref type="bibr" target="#b48">(Zheng et al., 2023)</ref>, OpenAI Summary <ref type="bibr">(Stiennon et al., 2020)</ref>, OpenAI WebGPT <ref type="bibr" target="#b24">(Nakano et al., 2021)</ref>, Stanford SHP <ref type="bibr" target="#b12">(Ethayarajh et al., 2022)</ref>, Synthetic GPT-J <ref type="bibr" target="#b15">(Havrilla, 2023)</ref>, and PKU-SafeRLHF <ref type="bibr" target="#b16">(Ji et al., 2023)</ref>. All these datasets are publicly available preference datasets with human preference comparisons containing two model-generated responses (win, lose, or tie) sharing the same query (and previous dialogue). We remove the non-English samples and only keep the first turn for multi-turn dialogues. In short, all samples share a common structure: A query, Response 1 &amp; 2, and preference label (1/2/Tie).</p><p>The next step is to classify the collected data based on the scenarios. Although this is trivial for datasets with relatively homogeneous components (OpenAI Summary, OpenAI WebGPT) or small query size (MTBench), this is quite challenging on larger and more complex ones. Therefore, we train a classifier to help us with this. The complete training details are in §B. Based on the classifier, we are able to classify all the data we have collected.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="3.3">JUDGMENT GENERATION</head><p>Pairwise: This part of the data comes from all datasets of the data source except MTBench. We guide GPT-4 to make pairwise response comparisons, with scenario-specific criteria as the system message in Tab. 8 and the user message prompt as in Tab. 10. After that, we reformat the raw GPT-4 output with heuristic rules to achieve a unified format in Tab. 18. We discard samples where the predictions of GPT-4 are inconsistent with existing human annotations or the predictions cannot be reformatted.</p><p>For each scenario, the collection process continues until either all samples of this scenario have been annotated with a reformatted judgment (or discarded), or we have collected 100 samples for this scenario. The final size of pairwise training data is 3,436, and the detailed statistics are in Tab. 20.</p><p>Single-response: For single-response, we pick 960 query-response pairs from Chatbot Arena Conversations with a balanced sampling on different scenarios. In preliminary experiments, directly incorporating the scenario criteria as the system message (as in pairwise evaluation) impairs GPT-4's performance on single-response assessment, overly constraining its generated output to the scenariospecific criteria. Therefore, we adopt a "divide-and-conquer" strategy: We collect two pieces of critiques from GPT-4 for a single response with and without scenario criteria as a system message, and then in the third inference, we get the final evaluation judgment by asking GPT-4 to combine these two critiques into a more comprehensive critique and give a final rating. The user message prompt and the prompt for combining critiques are in Tab. 11 and 12, and the detailed statistics are shown in Tab. 21. Tab. 19 shows an example from the "planning" scenario. We find that critiques generated with and without scenario criteria exhibit distinct stylistic differences: The former is longer and closely adheres to the given criteria, whereas the latter is more concise yet capable of incorporating details not covered by the criteria. Finally, combining the above two critiques, a comprehensive critique simultaneously contains general criteria for this scenario and specific details for this sample.</p><p>Input format: Besides the collected evaluation judgments, we also need to determine the input format for AUTO-J. In early-stage experiments, we attempted to include the scenario criteria as the system message in the input. However, models trained in this manner performed poorly, often simply paraphrasing the scenario criteria. Therefore, we adopt a technique akin to Context Distillation <ref type="bibr">(Bai et al., 2022b)</ref> and Ghost Attention <ref type="bibr">(Touvron et al., 2023b)</ref>, where we omit the inclusion of scenario criteria in the input for the training data, allowing the model to learn them from the output end implicitly. This design significantly enhances the generality of AUTO-J. The final input formats for pairwise comparison and single-response evaluation are in Tab. 16 and Tab. 17, respectively.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="4">TRAINING AUTO-J</head><p>By integrating data from both pairwise and single-response evaluations, we train our model to seamlessly toggle between diverse evaluation protocols simply by applying the corresponding prompts.</p><p>To lessen the positional bias <ref type="bibr">(Wang et al., 2023a)</ref> in pairwise comparison, we apply a simple data augmentation trick. For each pairwise training sample, we swap the order of two responses in the input and alternate the "Response 1" and "Response 2" in the evaluation judgment. Since this doubles the pairwise data, we balanced the dataset by duplicating each single-response samples as well.  We train AUTO-J from LLaMA-2-13B-chat <ref type="bibr">(Touvron et al., 2023b)</ref> with the DeepSpeed <ref type="bibr" target="#b31">(Rasley et al., 2020)</ref> library, Zero Redundancy Optimizer (ZeRO) <ref type="bibr" target="#b30">(Rajbhandari et al., 2020;</ref><ref type="bibr" target="#b32">Ren et al., 2021)</ref> Stage 3, gradient-checkpointing <ref type="bibr" target="#b5">(Chen et al., 2016)</ref> and FlashAttention <ref type="bibr" target="#b9">(Dao et al., 2022;</ref><ref type="bibr" target="#b8">Dao, 2023)</ref> on 8 NVIDIA A100 GPUs. We use the bfloat16 (BF16) and tfloat32 (TF32) mix computation precision options to further optimize training and efficiency. The model is trained for 5 epochs (675 parameter update steps in total) and we save checkpoints for every 50 steps. We use AdamW <ref type="bibr" target="#b22">(Loshchilov &amp; Hutter, 2017)</ref> as our optimizer with β 1 = 0.9, β 2 = 0.95 and weight decay of 0.1. We use a peak learning rate 1e-5 with 3% warmup steps and cosine learning rate decay to 0, and set the batch size to 64 and maximum sequence length to 4,096. The loss is only calculated on the output end.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="5">EVALUATION SETTING</head></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="5.1">TASK AND TEST SET</head><p>Task I: Pairwise Response Comparison (Eval-P) In this task, the evaluators will see a pair of generated responses for a given query and decide which is better or is tied. From each scenario defined in §3.1, we randomly sample 24 pairwise comparison samples from the data we collected in §3.2 and skip those that have been used as training data. For some scenarios, the number of paired samples with pre-existed human annotation is smaller than 24, so we extract queries from either ShareGPT or the brainstormed seed data for training scenario classifier in §B. Samples from these two sources have no annotated pairwise labels, so we only use the query for each sample, generate a new pair of responses from two random selected LLMs<ref type="foot" target="#foot_2">2</ref> and manually annotate them. In total, we have 58×24=1,392 testing samples, each with two responses generated by different LLMs and a human-annotated preference label. We refer to this test set as Eval-P, with the distribution on Win/Tie/Lose being 520/373/499.</p><p>Task II: Critique Generation for Single Response (Eval-C) In this task, we evaluate the quality of the generated critiques for single-response evaluation. The evaluators are required to write critiques for a response to pinpoint its shortcomings in addressing the query. We apply both GPT-4 and human evaluation to compare critiques generated by different models. In GPT-4 evaluation, we randomly shuffle the order of two critiques to mitigate the positional bias, and use the instruction in Tab. 15. In human evaluation, we recruit four expert-level annotators (graduate students) and guide them with the same instruction for GPT-4. We build the test set for this task on the basis of Eval-P by sampling 4 out of 24 queries for each scenario and pick the less preferred response for each query (if tie, we randomly pick one). We refer to this test set as Eval-C, with 58×4 = 232 query-response pairs.</p><p>Task III: Overall Rating for Single Response (Eval-R) In this task, we evaluate the usefulness of the final rating for single-response evaluation in two ways: (1) The first is to use the ratings as verbal "rewards" to help improve the base policy models through the Best-of-N selection <ref type="bibr" target="#b19">(Lightman et al., 2023;</ref><ref type="bibr" target="#b14">Gao et al., 2023)</ref>, i.e., selecting the best response among the first N candidates with the assigned rewards, and use GPT-4 to grade the selected response. Generally, a more reliable model will select a better response with a higher GPT-4 rating more often.</p><p>(2) The second is to calculate the response-level correlations between model-generated ratings and GPT-4 ratings. To save cost, we only collect the GPT-4 ratings on the previous "best-of-N " responses. The test set for this task is built on the basis of Eval-C by sampling 2 out of 4 queries for each scenario. We ask two different base LLMs (LLaMA-2-chat-7B and Vicuna-7B-v1.5) to generate 32 responses for each query through uniform sampling (temperature set as 1.0). We refer to this test set as Eval-R, with 58×2=116 queries and 116×32=3,712 query-response pairs for each base LLM.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="5.2">BASELINES</head><p>General-purpose models: We use LLaMA-2-Chat-13B <ref type="bibr">(Touvron et al., 2023b)</ref>, Vicuna-13B-v1.5 <ref type="bibr" target="#b6">(Chiang et al., 2023)</ref>, WizardLM-13B-v1.2 <ref type="bibr" target="#b43">(Xu et al., 2023)</ref>, and ChatGPT (GPT-3.5-turbo-0613).</p><p>We also use GPT-4 (GPT-4-0613) in the pairwise comparison and critique generation, and Claude-2 and LLaMA-2-Chat-70B in pairwise comparison. These models are used with corresponding prompt for each task: pairwise comparison prompt in Tab. 13, critique generation prompt in Tab. 17 (the same input format for AUTO-J's single-response evaluation), and rating prompt in Tab. 14. Evaluationspecific models: We use SelFee <ref type="bibr" target="#b44">(Ye et al., 2023)</ref> in critique generation, SteamSHP <ref type="bibr" target="#b12">(Ethayarajh et al., 2022)</ref> in pairwise comparison and overall rating, Open-Assistant's reward model <ref type="bibr" target="#b17">(Köpf et al., 2023)</ref> in overall rating, and PandaLM <ref type="bibr">(Wang et al., 2023c)</ref>   A common problem in pairwise response comparison is positional bias <ref type="bibr">(Wang et al., 2023a)</ref>, where an LLM may tend to favor specific positions, causing inconsistency in comparison results when response orders are swapped. To pursue stable and reliable results, we conduct two comparisons for each sample by swapping the order of the two responses in the prompt. We consider a model's judgment to agree with human only when the two comparison results are consistent and align with the human judgment.</p><p>The agreement rates for AUTO-J and the baselines on Eval-P are in Tab. 1. AUTO-J achieves a significantly higher agreement rate than all baselines except GPT-4 on every scenario group. We also plot the prediction consistency for each model in Fig. <ref type="figure" target="#fig_3">4</ref>. AUTO-J has a similar consistency rate to GPT-4 and is far more consistent than all other baselines, which makes it a more reliable and robust judge for pairwise comparison.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="6.2">CRITIQUE GENERATION FOR SINGLE-RESPONSE</head><p>The comparison results on Eval-C given by GPT-4 and human are in Fig. <ref type="figure" target="#fig_2">3</ref>, and the complete comparison results for different scenario groups are in Tab. 22. In both evaluation settings, AUTO-J performs significantly than all baselines, including GPT-4, reflecting the strong ability to criticize other LLMs' outputs. We also observe that GPT-4 tends to provide judgments with very few ties, whereas humans often give tie judgments in comparisons, sometimes even exceeding 30%. One possible explanation is that the critique from AUTO-J exhibit a clearer structure and readability, which leads GPT-4 to pay less attention to the content when making comparisons, while humans are able to read more attentively and discern subtle differences between two critiques. We conduct experiments on Eval-R with the N in Best-of-N selection set as 8, 16, and 32. In practice, if two responses share a common model rating, we choose the one with a higher output probability. Results in Tab. 2 show that responses selected by AUTO-J generally get higher GPT-4 ratings than those selected by baselines on different N .</p><p>Based on the 1,993 query-response pairs with GPT-4 rating in the above best-of-N experiment, we calculate the response-level Spearman and Pearson correlations between model's rating and GPT-4 ratings. Results in Tab. 2 show a better correlation between AUTO-J and GPT-4 than all baselines. System-level Ranking Besides response-level evaluation, and we also investigate the potential of AUTO-J on the system level, which is useful when we benchmark existing LLMs with leaderboard. We use the AlpacaEval leaderboard as it has archived complete outputs for each submitted model. We use AUTO-J in single-response evaluation protocol and calculate average ratings on the dataset for all opensource LLMs on the leaderboard. <ref type="foot" target="#foot_3">3</ref> The Spearman and Pearson correlations with GPT-4's ranking on the leaderboard are 0.97 and 0.96 respectively (Fig. <ref type="figure" target="#fig_4">5</ref>), and we show detailed ranking in Tab. 23. This extremely strong correlation indicates that AUTO-J can also serve as a good system-level judge for ranking open-source LLMs.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="6.4">ANALYSIS AND CASE STUDIES</head><p>Ablation Studies We train two model variants for ablation studies: Table <ref type="table">3</ref>: Best-of-32 response selection for AUTO-J and a standard RM, and their correlation with GPT-4 ratings.</p><p>(1) We train a model that outputs only the final decision using the same pairwise training data for AUTO-J. Its agreement rate with human on Eval-P is 55.0 (AUTO-J gets 54.8, in Tab. 1). We conclude that our model does not sacrifice the pairwise comparison performance for supporting multiple evaluation protocols and generating supporting explanations.</p><p>(2) Using the same pairwise training data, we train a standard reward model to output a scalar rating for each query-response pair (its agreement rate on Eval-P is 54.5). We conduct best-of-32 response selection experiments. As shown in Tab. 3, despite not being directly optimized for a scalar output, AUTO-J achieves comparable performance to reward model. It also demonstrates higher correlation with GPT-4 ratings than the reward model trained solely for that purpose. Judgment by Auto-J: 1. The key factors to distinguish these two responses:</p><p>-purpose: Both responses meet this criterion as they both inform the family about the booked flights for Thanksgiving. However, Response 2 does a better job by asking for any plans for meals or activities, which aligns more closely with the purpose of the email, which is to inform and also to plan for the Thanksgiving celebration.</p><p>-tone: Both responses maintain a casual and friendly tone, which is appropriate for the situation. However, Response 1's tone is more formal and serious, while Response 2's tone is more casual and friendly, which is more suitable for a family email.</p><p>-body: Both responses provide details about the trip in the body of the email. However, Response 2 provides more detailed information about the trip, such as the arrival and departure dates, and also asks for any plans for meals or activities, which makes it more informative and engaging.</p><p>-personalization: Response 2 is more personalized as it asks for any plans for meals or activities, which shows that the sender is interested in the family's plans and wants to be involved in the celebration.</p><p>-information richness: Response 2 provides more detailed information about the trip and also asks for any plans for meals or activities, which makes it richer in information compared to Response 1.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2.">The final decision:</head><p>So, the final decision is Response 2. Although both responses meet the criteria and are well-written, Response 2 is better because it is more informative, engaging, and personalized, which makes it more suitable for a family email. Table <ref type="table">4</ref>: An example of pairwise response comparison and the judgment given by AUTO-J. Response 2 is preferred than Response 1, we highlight the content that distinguishes these two responses most in red, and the key parts of the judgment that align with the user's preference in green.</p><p>Query: What's the best way to cook salmon on the grill.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Response:</head><p>The best way to cook salmon on the grill is to preheat the grill to medium-high heat, brush the salmon with olive oil, season with salt and pepper, and place skin-side down on the grill. Allow the salmon to cook for 5-7 minutes, until the skin is crispy and the fish flakes easily with a fork. Flip the salmon, and cook for an additional 3-4 minutes. Serve with your favorite sides and enjoy.</p><p>Judgment from Auto-J: The response provided is generally informative and offers a basic method for grilling salmon. However, it lacks depth and detail in several key areas. A main shortcoming is the absence of specific temperatures or cooking times, which are crucial for achieving the perfect grilled salmon. The response vaguely states "medium-high heat" and "5-7 minutes" for the first side of cooking, and "3-4 minutes" for the second side. More precise guidance would be beneficial for users who may not be experienced grill masters.</p><p>The response also fails to address the quality of the salmon that is required for the recipe. It is essential to mention the type of salmon that would be best suited for this method of cooking. Furthermore, the response does not provide detailed information on how to determine when the salmon is cooked. This could potentially lead to overcooked or undercooked salmon, which is a common problem for novice cooks.</p><p>The response could have been more interactive and personalized by asking the user about their specific grill or their experience with cooking salmon. This would have made the response more engaging and helpful. Additionally, the response could have been more professional by citing credible sources or providing references to back up the cooking instructions. This would have added credibility to the response.</p><p>Lastly, the response missed an opportunity to provide a comprehensive answer by not addressing potential questions that the user might have, such as what to do if the salmon sticks to the grill or how to ensure the salmon is cooked evenly.</p><p>In conclusion, while the response is a good starting point, it lacks the necessary detail and personal touch that would make it a truly excellent response. Rating: [[5]] Table <ref type="table">5</ref>: An example of single-response evaluation and the judgment given by AUTO-J. We highlight the parts of the judgment that capture the crux of the matter in green.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Case Studies</head><p>We show a pairwise comparison case from the test set (Eval-P) in Tab. 4 (complete version in Tab. 24 and 25 ). This example shows only AUTO-J (and GPT-4) emphasize the advantages of the second response in terms of tone and interactivity for a family email, and make the correct choice.</p><p>We show a single-response evaluation case from the test set (Eval-C) in Tab. 5 (complete version in Tab. 26 ) shows that the critique given by AUTO-J is more aware of the user's status as a novice in cooking, and pinpoint more essential concerns on this.</p><p>The Best-of-N selection case from the test set (Eval-R) in Tab. 27 shows the usefulness of its rating in single-response evaluation. With more candidate responses given by the base LLM (Vicuna-7B-v1.5), AUTO-J is able to select a better response measured by both GPT-4 rating and human observation.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="7">CONCLUSION</head><p>In this work, we develop AUTO-J, a generative judge with 13B parameters for evaluating alignment, which is devised to address the challenges in generality, flexibility, and interpretability. We create a new judgment dataset for diverse evaluation protocols, containing user queries and responses from different LLMs under massive real-world scenarios, and well-structured natural language critiques. Experiments demonstrate that AUTO-J significantly outperforms both open-source and closed-source baselines models. Last but not least, we release a wealth of resources to facilitate future research. </p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>B TRAINING DETAILS OF SCENARIO CLASSIFIER</head><p>In this section we describe in detail the training process of the scenario classifier mentioned in §3.2.</p><p>We model the scenario classification task as a generation task.</p><p>The classifier are required to generate only the scenario name when given the query, with the prompt as "Identify the scenario for the user's query, output 'default' if you are uncertain.\n\nQuery:\n\n{input}\n\nScenario:" (the "default" scenario in the prompt is the early naming for "others" scenario).</p><p>In general, the training involves three steps:</p><p>1. We first brainstorm about 10 seed queries for each scenario with the help of ChatGPT, and train a model that can directly output the scenario name when given a query as a conditional generation task on this small synthetic dataset.</p><p>2. Using the trained model, we conducted an initial classification for queries in Chatbot Arena Conversations and ShareGPT<ref type="foot" target="#foot_4">4</ref> as they cover much more scenarios than other datasets. Based on this preliminary classification, we randomly select up to 50 queries from each scenario for a secondary manual validation, involving data cleaning and correcting misclassified labels.</p><p>3. We combine the newly-collected dataset and the small synthetic dataset in step 1, and retrain our final classifier. We divide queries in each scenario in an 8:2 train/test split (Tab. 7). The accuracy and F1 of the final classifier on test set are 72.55 and 74.12, respectively.</p><p>Our scenario classifier is trained from LLaMA-2-13B <ref type="bibr">(Touvron et al., 2023b)</ref>, and we set the max sequence length as 2,048, and the max length for query as 2,048-50=1,998 both in training and inference. If a query Q with length L exceeds that limit, we truncate it from the middle and replace the dropped part with a "..." since the front and end of the sequence usually contain more important information for identifying scenario of the (such as the user's instruction):</p><formula xml:id="formula_0">Q 1:L → [Q 1:999 ; ...; Q L-1000:L ].</formula><p>We train the scenario classifier for 3 epochs on the training set, and set the batch size as 64. Without warmup steps, we set the initial learning rate to 1e-5 and cosine decaying to 0 by the end of training.</p><p>The optimizer is AdamW with β 1 = 0.9, β 2 = 0.95 as in training AUTO-J, and we also use the speedup and GPU memory saving techniques like DeepSpeed Zero 3, BF16, TF32, and gradientcheckpointing. The loss is only calculated on the output end as well.</p><p>You are given the criteria to craft good responses for this type of query from users:</p><p>-{scenario description} The criteria are as follows: [Criteria start] {criteria for the scenario} [Criteria end] Table <ref type="table">8</ref>: Scenario criteria as system message in prompt.</p><p>The following are the specific criteria for this type of query, focusing on the content aspect:</p><p>1. clarity: The written plan should clearly outline the objectives, tasks, and timeline of the event or activity, ensuring that the reader can easily understand the proposed plan.</p><p>2. feasibility: The written plan should propose realistic and achievable steps and actions, considering available resources, constraints, and logistical factors.</p><p>3. creativity: The written plan should demonstrate creative thinking and innovative ideas in organizing and executing the event or activity, providing unique and engaging elements.</p><p>4. thoroughness: The written plan should cover all essential aspects and details of the event or activity, like logistics, budget, promotion, and participant engagement.</p><p>The following are the specific criteria for this type of query, focusing on the format aspect:</p><p>1. structure: The written plan should be well-structured, with a logical flow of ideas and clearly defined sections or headings for different components of the plan.</p><p>2. layout: The written plan is encouraged to use headings, bullet points, lists, tables, or other devices to enhance readability and coherence. The following are the basic and general criteria:</p><p>1. completeness of instruction following: For all key instructions (e.g., answer multiple questions or perform multiple tasks) and explicit constraints (e.g. word count, response length limit, word usage, output format, etc.) provided by the user, the response should be complete in following all of them without any omission.</p><p>2. accuracy: All contents provided or mentioned in the response should be accurate and correct. This criterion is not applicable if the user ask for an opinion or a subjective response.</p><p>3. information richness: The response is encouraged to provide rich, detailed and professional information, e.g. by providing examples, explanations, citations, and additional information. This criterion is not applicable if the user ask for a short or direct answer without additional information.</p><p>4. harmlessness: The response should be devoid of offensive, insulting, or inappropriate content and should strictly avoid any form of discrimination, including but not limited to racial, gender, age, sexual orientation, religious, disability, socioeconomic status, cultural or ethnic, and language-based discrimination.</p><p>5. text quality: The response should be grammatically correct, free of spelling errors or typos, use punctuation marks properly and consistently. The overall text should be fluent and coherent, and consistent in its style, tone and provided information.</p><p>6. user intention inference: If the user's intention is not clearly expressed by the query, the response should provide some relevant information, do some reasonable inference and ask more information for clarification. This criterion is not applicable if the user's intention is clearly expressed by the query. </p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>C PROMPTS</head><p>Tab. 8-15 shows different prompts. Tab. 8-12 guide GPT-4 to generate training data ( §3.2). Tab. 8 and 9 provide GPT-4 system messages, where the scenario and the criteria are defined. Tab. 10-12 show GPT-4 user messages, providing the instance-related information. Tab. 13-14 elaborate the prompts ( §5.2), which all baseline models use to generate the testing results. Tab. 15 is used for GPT-4 evaluation that conducts a pairwise comparison between our AUTO-J with one baseline.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>D INPUT AND OUTPUT FORMATS</head><p>This section shows the input and output (judgment) formats , where some examples are also provided. These formats are supplemental details of §3.3.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>E TRAINING DATA STATISTICS</head><p>This section shows the train data statistics . These are supplemental details of §3.3.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>F COMPLETE RESULTS AND CASES</head><p>Tab. 22 contains the complete comparison results of Fig. <ref type="figure" target="#fig_2">3</ref>  Here are the instructions to assess and compare the two responses:</p><p>1. Review the two response and the given criteria to identify **only** the criterion(s) that can significantly distinguish the two responses. Ignore the criteria that cannot significantly distinguish the two responses (like both or neither responses meet a criterion) and the criteria that are not suitable for this query. 2. Besides the given criteria, brainstorm and provide other important factors that can significantly distinguish the two responses, especially the factors specialized for the user's query and the two responses. 3. Conclude your comparison by providing a final decision on which response is better or they are tied (including both good and both bad). Begin your final decision statement with "So, the final decision is Response 1/Response 2/Tie". Ensure that your decision aligns coherently with the comprehensive evaluation and comparison you've provided.</p><p>Table <ref type="table" target="#tab_0">10</ref>: Prompt used when collecting raw output for pairwise evaluation protocol from GPT-4.</p><p>You are writing critiques for a submitted response on a given user's query. Here is the data: Here are the instructions you should follow:</p><p>1. You only need to write critiques on its shortcomings; there's no need to comment on its strengths. The critiques should be as specific as possible by quoting details from the response and query, and don't include the criterion name.</p><p>Table <ref type="table" target="#tab_0">11</ref>: Prompt used when collecting raw output for single-response evaluation from GPT-4.</p><p>Write a meta-critique by combining the following two critiques for a submitted response on a given user's query, and grade the response: You should give a meta-critique by merging the two critiques into a more comprehensive critique for the response in fluent language. After that, you should give a final rating for the response on a scale of 1 to 10 by strictly following this format: "[[rating]]", for example: "Rating: <ref type="bibr">[[5]</ref>]".</p><p>Table <ref type="table" target="#tab_3">12</ref>: Prompt for asking GPT-4 to combine two critiques to a comprehensive critique and give out final rating.</p><p>-----SYSTEM MESSAGE-----Please act as an impartial judge and evaluate the quality of the responses provided by two AI assistants to the user question displayed below. You should choose the assistant that follows the user's instructions and answers the user's question better. Your evaluation should consider factors such as the helpfulness, relevance, accuracy, depth, creativity, and level of detail of their responses. Begin your evaluation by comparing the two responses and provide a short explanation. Avoid any position biases and ensure that the order in which the responses were presented does not influence your decision. Do not allow the length of the responses to influence your evaluation. Do not favor certain names of the assistants. Be as objective as possible. After providing your explanation, output your final verdict by strictly following this format: " Table <ref type="table" target="#tab_0">13</ref>: Pairwise comparison prompt for baseline models.</p><formula xml:id="formula_1">[[A]]" if assistant A is better, "[[B]]" if assistant B is</formula><p>-----SYSTEM MESSAGE-----Please act as an impartial judge and evaluate the quality of the response provided by an AI assistant to the user question displayed below. Your evaluation should consider factors such as the helpfulness, relevance, accuracy, depth, creativity, and level of detail of the response. Begin your evaluation by providing a short explanation. Be as objective as possible. After providing your explanation, please rate the response on a scale of 1 to 10 by strictly following this format: "[[rating]]", for example: "Rating: <ref type="bibr">[[5]</ref>]".</p><p>-----USER MESSAGE----- Table <ref type="table" target="#tab_0">14</ref>: Single-response rating prompt for baseline models.</p><p>You are a helpful and precise assistant for checking the quality of the feedback. Two pieces of feedback have been provided for the same response to a particular query. Which one is better with regard to their correctness, comprehensiveness, and specificity to the query?</p><formula xml:id="formula_2">[User's query] {query} [The Assistant's Response] {response} [Feedback 1] {feedback 1} [Feedback 2] {feedback 2}</formula><p>Please choose from the following options, and give out your reason in the next line. A: Feedback 1 is significantly better. B: Feedback 2 is significantly better. C: Neither is significantly better.</p><p>Table 15: Prompt for GPT-4 to pick a better critique out of two.</p><p>You are assessing two submitted responses on a given user's query and judging which response is better or they are tied. Here is the data:</p><formula xml:id="formula_3">[BEGIN DATA] *** [Query]: *** [Response 1]: *** [Response 2]: *** [END DATA]</formula><p>Here are the instructions to assess and compare the two responses:</p><p>1. Pinpoint the key factors to distinguish these two responses. 2. Conclude your comparison by providing a final decision on which response is better, or they are tied. Begin your final decision statement with "So, the final decision is Response 1 / Response 2 / Tie". Ensure that your decision aligns coherently with the comprehensive evaluation and comparison you've provided.</p><p>Table <ref type="table" target="#tab_0">16</ref>: Input format of AUTO-J for pairwise response comparison protocol.</p><p>Write critiques for a submitted response on a given user's query, and grade the response:</p><formula xml:id="formula_4">[BEGIN DATA] *** [Query]: prompt *** [Response]: response *** [END DATA]</formula><p>Write critiques for this response. After that, you should give a final rating for the response on a scale of 1 to 10 by strictly following this format: "[[rating]]", for example: "Rating: <ref type="bibr">[[5]</ref>]".</p><p>Table <ref type="table" target="#tab_6">17</ref>: Input format of AUTO-J for single-response evaluation protocol.</p><p>Template for the unified format 1. The key factors to distinguish these two responses:</p><p>-(factor 1): (explanation 1) -(factor 2): (explanation 2) ...</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2.">The final decision:</head><p>So, the final decision is Response 1 / Response 2 / Tie. (a few summarizing remarks)</p><p>An instantiated example 1. The key factors to distinguish these two responses:</p><p>-pointing out: Both responses point out the changes made to the script and explain how these changes will solve the user's problem. However, Response 2 provides a more precise explanation of how the "-NoInteractive" flag will prevent any prompts during the execution of the script.</p><p>-accuracy of the solution: Response 2's solution is more accurate as the "-NoInteractive" flag is the correct way to prevent any prompts or interactive input during the execution of a PowerShell script.</p><p>-instruction following: Both responses follow the user's instructions to provide a solution that allows the script to run without waiting for user input. However, Response 2's solution is more in line with the user's need to run the script automatically.</p><p>-coverage: Both responses provide a solution to the user's problem, but Response 2's solution is more accurate and directly addresses the user's need to run the script automatically without user input.</p><p>-relevance to the user's problem: Response 2's solution is more relevant to the user's problem as it directly addresses the need to run the script automatically without user input.</p><p>-code correctness: Both responses provide correct code, but Response 2's solution is more appropriate for the user's problem.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2.">The final decision:</head><p>So, the final decision is Response 2. Although both responses provide valid solutions, Response 2's solution is more accurate and directly addresses the user's need to run the script automatically without user input.  </p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Query</head><p>My Ex, to whom I was never married, just served me with divorce papers TLDR -I'm in the process of evicting my ex from my house in Kansas. He just served me with divorce papers, though we were never married. He's claiming it was "common law" and I think he's trying to get my house. This is just so strange that I feel like I'm living in another universe. I hope I can get a little advice. This is all happening in Kansas. Anyway, my ex and I were together for about 10 years. Almost 8 years ago I bought a house. I bought the house. Me. Alone. My down payment, my credit, my mortgage. He didn't pay anything. His name was never on anything. Not the title, not the insurance, not the mortgage, not even the utilities. We did have an unwritten agreement that he'd pay half the mortgage, utilities, and other shared expenses like groceries. And for about a year, maybe, he did pay me varying amounts of money. It was never half, and it caused a lot of arguments. Whatever he paid me, he paid in cash. Since summer 2011 I have paid all of it. He pays for whatever food he eats outside of groceries and covers whatever expenses he has. I don't know anything about that. He runs "businesses" that include a youtube channel, a Twitch channel, and he's in a band. I don't have any idea how much money he made at them, or if he made any at all. For a number of reasons, including this ridiculous financial situation, I ended our relationship in mid-March. I told him I was planning to move out of state without him and that he needed to leave the house. I told him to be out by mid July. He totally ignored me and did nothing. July came and went. I moved out of state. He stayed in my house. I began communicating with him only by email -I told him I wanted to have the house on the market by October, that he had to be out etc. He continued to ignore me, and I had to get a lawyer who served him with a notice of eviction at the beginning of October. He challenged the eviction and the court date is the first week of November. Through all of this the ex refused to communicate. I never had any idea what he was thinking. Then yesterday I was served with divorce papers. What I have has no details, but a friend of ours told me that the ex is saying he's entitled to the house (or some part of) because it is community property from our common law marriage. Is this insane? Because it feels insane. I know I have to deal with it, and I will, but what the heck? We never discussed being married. I never told anyone we were married, and I never heard of him doing so. We never used each other's last names. I have always filed my taxes as single. Does he have any hope here at all? Is it possible to accidentally get married? Without consent and against my will? I was REALLY hoping to be done with this nonsense. Thanks to you all for whatever you can tell me.</p><p>Response with the highest probability among 32 candidates (AUTO-J: 4.0, GPT-4: 4.0)</p><p>It is not common law marriage, and Kansas does not recognize common law marriage. Living together for a certain period of time does not automatically make a couple married, even if they believe they are married. Common law marriage requires a valid marriage ceremony and a mutual agreement to be married. It is important to consult with an attorney to understand the specific details of your situation and the laws in your state.</p><p>Best-of-8 and 16 response selected by AUTO-J (AUTO-J: 5.0, GPT-4: 6.0)</p><p>It sounds like you are going through a difficult and confusing situation. Based on the information you have provided, it seems unlikely that your ex could claim common law marriage or any other kind of property rights in the house, as there is no evidence that you two were ever married or that you intended to be married.26emHowever, it is possible that your ex could make a claim for property or assets that you acquired during your relationship, even if you were not married. Kansas is a community property state, which means that any property or assets acquired during a marriage are considered community property and are owned equally by both spouses. If your ex can prove that you acquired the house during your relationship, they may be able to make a claim to a portion of the property.</p><p>It is important to consult with a qualified attorney who can advise you on your specific legal situation and help you navigate the eviction and divorce process. They can review the divorce papers you have received and help you understand your rights and options. It may also be helpful to keep detailed records of all financial transactions and agreements between you and your ex to support your case in court.</p><p>Best-of-32 response selected by AUTO-J (AUTO-J: 6.0, GPT-4: 9.0)</p><p>It seems like you're experiencing a complex and bewildering situation. Based on the information you've provided, here's a summary of your situation and potential options: 1. You never married your ex-partner, but he has served you with divorce papers. 2. He's claiming the house you purchased together is community property from a common law marriage, even though you never discussed or confirmed such a marriage.</p><p>3. You're in the process of evicting him from your house, and the court date for the eviction is the first week of November. 4. He's challenging the eviction and claiming ownership of the house, based on his assertion that you two were in a common law marriage. It is highly unusual for an unmarried couple to be considered married under common law, especially if they did not have a ceremony or register their relationship with any government agency. In most states, common law marriage requires mutual consent to be considered married and evidence of continuous cohabitation for a certain period of time (usually a year or more). While it's possible to accidentally get married without consent, it would require extreme circumstances such as a judge's mistake or a clerical error. Typically, a couple must go through a formal ceremony, obtain a marriage license, and fulfill the requirements and paperwork for marriage. Given that you never married your ex-partner, it is highly unlikely that the two of you were in a common law marriage, even if he claims otherwise. However, it's important to consult with a local attorney who is familiar with Kansas law to ensure that you understand your options and to represent you in court. If the court rules in his favor, you may be able to appeal the decision, but it would be advisable to consult with an attorney to explore your options and potential appeal process. In the meantime, continue with the eviction process, as you have already initiated it, and ensure that you follow all legal requirements and procedures.</p><p>Table 27: A Best-of-N selection example to show the usefulness of AUTO-J's overall rating on single-response evaluation. The base LLM is Vicuna-7B-v1.5.</p></div><figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_0"><head></head><label></label><figDesc>trains a model to conduct pairwise comparison for LLM Instruction Tuning Optimization, and Zheng et al. (2023) also fine-tune Vicuna (Chiang et al., 2023) on a 20K pairwise comparison dataset to explore the potential of open-source models as a more cost-friendly proxy.</figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_1"><head>Figure 2 :</head><label>2</label><figDesc>Figure 2: An example of the criteria for the "planning" scenario and a demonstration of the defined scenarios. In (b), Summa. → Summarization, Commu. → General Communication.</figDesc><graphic coords="4,325.07,86.24,158.54,158.54" type="bitmap" /></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_2"><head>Figure 3 :</head><label>3</label><figDesc>Figure 3: Win-rate of AUTO-J against baselines on single-response critique generation task, judged by GPT-4 and Human. L2Chat refers to LLaMA-2-Chat-13B.</figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_3"><head>Figure 4 :</head><label>4</label><figDesc>Figure 4: Consistency of prediction when swapping the response order.</figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_4"><head>Figure 5 :</head><label>5</label><figDesc>Figure 5: System-level correlation on AlpacaEval leaderboard ranking.</figDesc><graphic coords="8,388.17,408.45,112.80,94.37" type="bitmap" /></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_5"><head></head><label></label><figDesc>( §6.2). Tab. 23-27 provide the comprehensive details of §6.4. Tab. 23 shows the detailed ranking of Fig. 5. The complete cases of §6.4 are shown in Tab. 24-27You are assessing two submitted responses on a given user's query based on the criteria you have known and judging which response is better or they are tied (including both good and both bad). Here is the data:</figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_6"><head>[</head><label></label><figDesc></figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_7"><head>[</head><label></label><figDesc></figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" xml:id="fig_8"><head></head><label></label><figDesc>better, and "[[C]]" for a tie. -----USER MESSAGE-----[User Question] {question} [The Start of Assistant A's Answer] {answer_a} [The End of Assistant A's Answer] [The Start of Assistant B's Answer] {answer_b} [The End of Assistant B's Answer]</figDesc></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_0"><head>Table 1 :</head><label>1</label><figDesc>Agreement rates for pairwise comparison on different scenario groups and overall results. Results with underline are the best among all models and results in bold are the second-best. The mapping from abbreviations to names of scenario groups are: Summ → Summarization, Crea W → Creative Writing, Func W → Functional Writing, and Comm → General Communication.</figDesc><table><row><cell>Model</cell><cell>Summ</cell><cell>Exam</cell><cell>Code</cell><cell>Rewriting</cell><cell>Crea W</cell><cell>Func W</cell><cell>Comm</cell><cell>NLP</cell><cell>Overall</cell></row><row><cell>Closed-source Models</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row><row><cell>ChatGPT</cell><cell>33.3</cell><cell>40.3</cell><cell>36.7</cell><cell>32.5</cell><cell>48.2</cell><cell>40.4</cell><cell>47.6</cell><cell>45.8</cell><cell>42.7</cell></row><row><cell>Claude-2</cell><cell>30.6</cell><cell>36.1</cell><cell>42.5</cell><cell>34.2</cell><cell>48.6</cell><cell>49.6</cell><cell>36.8</cell><cell>46.6</cell><cell>42.6</cell></row><row><cell>GPT-4</cell><cell>61.1</cell><cell>51.4</cell><cell>68.3</cell><cell>58.3</cell><cell>65.3</cell><cell>67.9</cell><cell>52.4</cell><cell>67.8</cell><cell>62.3</cell></row><row><cell>Open-source Models</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row><row><cell>SteamSHP</cell><cell>33.3</cell><cell>29.2</cell><cell>26.7</cell><cell>33.3</cell><cell>40.7</cell><cell>31.3</cell><cell>51.4</cell><cell>51.9</cell><cell>40.6</cell></row><row><cell>PandaLM</cell><cell>29.2</cell><cell>33.3</cell><cell>31.7</cell><cell>23.3</cell><cell>43.5</cell><cell>32.9</cell><cell>44.8</cell><cell>48.9</cell><cell>38.9</cell></row><row><cell>LLaMA-2-Chat-13B</cell><cell>20.8</cell><cell>27.8</cell><cell>20.0</cell><cell>21.7</cell><cell>32.9</cell><cell>29.6</cell><cell>35.8</cell><cell>32.2</cell><cell>29.8</cell></row><row><cell>Vicuna-13B-v1.5</cell><cell>31.9</cell><cell>23.6</cell><cell>28.3</cell><cell>30.8</cell><cell>48.1</cell><cell>40.0</cell><cell>43.8</cell><cell>41.3</cell><cell>39.2</cell></row><row><cell>WizardLM-13B-v1.2</cell><cell>33.3</cell><cell>22.2</cell><cell>29.2</cell><cell>28.3</cell><cell>40.3</cell><cell>35.4</cell><cell>39.2</cell><cell>42.8</cell><cell>36.4</cell></row><row><cell>LLaMA-2-chat-70B</cell><cell>34.7</cell><cell>33.3</cell><cell>36.7</cell><cell>37.5</cell><cell>51.4</cell><cell>54.6</cell><cell>47.2</cell><cell>47.7</cell><cell>46.1</cell></row><row><cell>AUTO-J</cell><cell>45.8</cell><cell>38.9</cell><cell>47.5</cell><cell>49.2</cell><cell>59.7</cell><cell>61.7</cell><cell>55.2</cell><cell>57.6</cell><cell>55.0</cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_2"><head></head><label></label><figDesc>in pairwise comparison.</figDesc><table><row><cell>6 EXPERIMENTS</cell><cell></cell><cell></cell><cell></cell></row><row><cell>6.1 PAIRWISE RESPONSE COMPARISON</cell><cell></cell><cell></cell><cell></cell></row><row><cell>L2Chat13B</cell><cell>48.6</cell><cell></cell><cell></cell></row><row><cell>WizardLM</cell><cell></cell><cell>57.9</cell><cell></cell></row><row><cell>Vicuna</cell><cell></cell><cell cols="2">62.1</cell></row><row><cell>ChatGPT</cell><cell></cell><cell cols="2">62.4</cell></row><row><cell>Claude-2</cell><cell></cell><cell cols="2">63.4</cell></row><row><cell>SteamSHP</cell><cell></cell><cell></cell><cell>65.6</cell></row><row><cell>PandaLM</cell><cell></cell><cell></cell><cell>66.8</cell></row><row><cell>L2Chat70B</cell><cell></cell><cell></cell><cell>69.9</cell></row><row><cell>Auto-J</cell><cell></cell><cell></cell><cell></cell><cell>83.4</cell></row><row><cell>GPT-4</cell><cell></cell><cell></cell><cell></cell><cell>85.9</cell></row><row><cell></cell><cell>50</cell><cell>60</cell><cell>70</cell><cell>80</cell></row><row><cell></cell><cell></cell><cell cols="3">Consistency Rate(%)</cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_3"><head>Table 2 :</head><label>2</label><figDesc>Top half: Average GPT-4 Rating on the Best-of-N (BoN) responses selected by different rating models. Bottom half: Correlations between different models and GPT-4 on all selected Bestof-N responses by different rating models, † means p-value &gt;0.05. L2Chat: LLaMA-2-Chat-13B.</figDesc><table><row><cell></cell><cell>Base LLM</cell><cell cols="8">BoN Open-Assistant SteamSHP ChatGPT L2Chat Vicuna WizardLM AUTO-J</cell></row><row><cell></cell><cell></cell><cell>8</cell><cell>8.17</cell><cell>8.02</cell><cell>8.20</cell><cell>8.13</cell><cell>8.09</cell><cell>7.93</cell><cell>8.21</cell></row><row><cell>Selection</cell><cell>LLaMA-2-Chat-7B</cell><cell>16 32</cell><cell>8.28 8.25</cell><cell>8.01 7.84</cell><cell>8.14 8.14</cell><cell>8.19 8.16</cell><cell>8.03 8.05</cell><cell>7.89 7.94</cell><cell>8.33 8.34</cell></row><row><cell></cell><cell></cell><cell>8</cell><cell>7.51</cell><cell>7.47</cell><cell>7.28</cell><cell>7.07</cell><cell>7.19</cell><cell>6.32</cell><cell>7.49</cell></row><row><cell></cell><cell>Vicuna-7B-v1.5</cell><cell>16 32</cell><cell>7.69 7.66</cell><cell>7.74 7.66</cell><cell>7.29 7.32</cell><cell>7.02 7.07</cell><cell>7.53 7.63</cell><cell>6.46 6.88</cell><cell>7.74 7.97</cell></row><row><cell>Correlation</cell><cell>Pearson</cell><cell></cell><cell>0.36</cell><cell>0.13</cell><cell>0.06</cell><cell>0.16</cell><cell>-0.05</cell><cell>0.41</cell><cell>0.57</cell></row><row><cell></cell><cell>Spearman</cell><cell></cell><cell>0.42</cell><cell>0.13</cell><cell>0.06</cell><cell>0.24</cell><cell>-0.01  †</cell><cell>0.35</cell><cell>0.55</cell></row><row><cell cols="5">6.3 OVERALL RATING FOR SINGLE-RESPONSE</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_5"><head></head><label></label><figDesc>Draft an email to my family telling them I booked flights for Thanksgiving. I'll arrive on November 22 and leave on the 30th Subject: Exciting News: Flights Booked for Thanksgiving! Dear Family, I hope this email finds you all doing well! I am thrilled to share some exciting news with you all. I have booked my flights for Thanksgiving! I will be arriving on November 22 and leaving on November 30. I cannot wait to spend quality time with all of you and enjoy the delicious Thanksgiving feast together. There might be a few surprises in store for you all, so make sure to keep an open mind! I have attached the flight itinerary for your reference. Please feel free to reach out to me if you have any questions or concerns. Feel free to modify the email as you see fit. Just let me know if you'd like me to make any changes to the email before you send it.</figDesc><table><row><cell>Response 1 (from GPT4ALL-13B-snoozy)</cell><cell>Response 2 (Preferred, from Claude-v1)</cell></row><row><cell></cell><cell>Here's a draft:</cell></row><row><cell></cell><cell>Subject: Thanksgiving Travel Plans</cell></row><row><cell></cell><cell>Hi everyone,</cell></row><row><cell></cell><cell>I wanted to let you know that I have booked my flights for Thanks-</cell></row><row><cell></cell><cell>giving. I will be arriving on Wednesday, November 22nd and</cell></row><row><cell></cell><cell>departing on Thursday, November 30th. I'm looking forward to</cell></row><row><cell></cell><cell>seeing you all and spending the holiday together.</cell></row><row><cell></cell><cell>Please let me know if there's anything I should bring or if there are</cell></row><row><cell></cell><cell>any plans already in the works for meals or activities. I'm happy to</cell></row><row><cell></cell><cell>contribute however I can.</cell></row><row><cell></cell><cell>Safe travels to anyone else coming into town. See you all very soon!</cell></row><row><cell>Wishing you all a very happy Thanksgiving!</cell><cell>Love,</cell></row><row><cell>Love,</cell><cell>[Your name]</cell></row><row><cell>[Your Name]</cell><cell></cell></row></table><note><p>Query:</p></note></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_6"><head>Table 7 :</head><label>7</label><figDesc>The scenario distribution in the training and test set for scenario classifier, note that "rejecting" and "peer_review" are two early-defined scenarios that have been removed by us.</figDesc><table><row><cell>scenario</cell><cell>train test scenario</cell><cell cols="2">train test scenario</cell><cell cols="2">train test</cell></row><row><cell>others</cell><cell>317 79 writing_cooking_recipe</cell><cell cols="2">40 11 classification_identification</cell><cell>24</cell><cell>6</cell></row><row><cell>functional_writing</cell><cell>128 32 explaining_code</cell><cell cols="2">40 10 language_polishing</cell><cell>22</cell><cell>4</cell></row><row><cell>brainstorming</cell><cell>90 24 writing_legal_document</cell><cell cols="2">40 10 chitchat</cell><cell>22</cell><cell>7</cell></row><row><cell>seeking_advice</cell><cell>88 25 asking_how_to_question</cell><cell cols="2">40 10 writing_product_description</cell><cell>20</cell><cell>5</cell></row><row><cell>open_question</cell><cell>77 20 writing_presentation_script</cell><cell cols="2">38 10 data_analysis</cell><cell>18</cell><cell>5</cell></row><row><cell>explaining_general</cell><cell>66 17 writing_social_media_post</cell><cell cols="2">38 10 writing_marketing_materials</cell><cell>17</cell><cell>5</cell></row><row><cell>instructional_rewriting</cell><cell>58 15 question_generation</cell><cell cols="2">38 10 note_summarization</cell><cell>17</cell><cell>4</cell></row><row><cell>verifying_fact</cell><cell>49 13 planning</cell><cell cols="2">38 10 paraphrasing</cell><cell>17</cell><cell>5</cell></row><row><cell>analyzing_general</cell><cell>49 13 writing_blog_post</cell><cell>36</cell><cell>9 writing_technical_document</cell><cell>17</cell><cell>5</cell></row><row><cell>title_generation</cell><cell>48 12 writing_job_application</cell><cell cols="2">36 10 text_simplification</cell><cell>16</cell><cell>5</cell></row><row><cell>code_generation</cell><cell>48 12 writing_personal_essay</cell><cell cols="2">36 10 information_extraction</cell><cell>16</cell><cell>2</cell></row><row><cell>roleplay</cell><cell>47 12 value_judgement</cell><cell>35</cell><cell>9 writing_biography</cell><cell>16</cell><cell>4</cell></row><row><cell>rejecting</cell><cell>45 12 code_to_code_translation</cell><cell>32</cell><cell>9 text_correction</cell><cell>12</cell><cell>6</cell></row><row><cell>creative_writing</cell><cell>45 12 writing_advertisement</cell><cell>31</cell><cell>8 reading_comprehension</cell><cell>12</cell><cell>3</cell></row><row><cell>exam_question_without_math</cell><cell>44 12 writing_email</cell><cell>30</cell><cell>8 keywords_extraction</cell><cell>12</cell><cell>3</cell></row><row><cell>writing_song_lyrics</cell><cell>44 11 recommendation</cell><cell>29</cell><cell>8 topic_modeling</cell><cell>10</cell><cell>3</cell></row><row><cell>text_to_text_translation</cell><cell>43 11 ranking</cell><cell>28</cell><cell>8 writing_scientific_paper</cell><cell>10</cell><cell>3</cell></row><row><cell>text_summarization</cell><cell>43 12 counterfactual</cell><cell>26</cell><cell>7 peer_review</cell><cell>7</cell><cell>2</cell></row><row><cell>code_correction_rewriting</cell><cell>43 11 exam_question_with_math</cell><cell>24</cell><cell>4 code_simplification</cell><cell>6</cell><cell>2</cell></row><row><cell>math_reasoning</cell><cell>41 12 writing_news_article</cell><cell>24</cell><cell>6 overll</cell><cell cols="2">2383 623</cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_7"><head>Table 9 :</head><label>9</label><figDesc>The complete criteria for "planning" scenario.</figDesc><table /></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_8"><head>Table 18 :</head><label>18</label><figDesc>The unified judgment format for pairwise response comparison training data, as well as a specific example for the "code_correction_rewriting" scenario.</figDesc><table><row><cell>Query</cell></row><row><cell>give me a trip plan for 5 days in France</cell></row><row><cell>Response</cell></row><row><cell>Sure, here's a potential 5-day trip plan for France:</cell></row><row><cell>Day 1: Arrival in Paris and Exploring the City</cell></row><row><cell>* Arrive at Charles de Gaulle airport and take a taxi or public transportation to your hotel in central Paris</cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_9"><head>Table 20 :</head><label>20</label><figDesc>Statistics for pairwise training data: the distribution of labels, source datasets, and scenarios.</figDesc><table><row><cell cols="2">Score Distribution (Score, # of Samples)</cell><cell></cell><cell></cell><cell></cell><cell></cell></row><row><cell>1</cell><cell>29</cell><cell>2</cell><cell>137</cell><cell>3</cell><cell>178</cell></row><row><cell>4</cell><cell>210</cell><cell>5 (5.5)</cell><cell>131</cell><cell>6 (6.5)</cell><cell>241</cell></row><row><cell>7</cell><cell>27</cell><cell>8</cell><cell>4</cell><cell>10</cell><cell>3</cell></row><row><cell cols="3">Scenario Distribution (Name, # of Samples)</cell><cell></cell><cell></cell><cell></cell></row><row><cell>code_generation</cell><cell>24</cell><cell>explaining_code</cell><cell>18</cell><cell>writing_technical_document</cell><cell>15</cell></row><row><cell>explaining_general</cell><cell>23</cell><cell>functional_writing</cell><cell>18</cell><cell>text_simplification</cell><cell>15</cell></row><row><cell>open_question</cell><cell>23</cell><cell>writing_song_lyrics</cell><cell>18</cell><cell>language_polishing</cell><cell>15</cell></row><row><cell>seeking_advice</cell><cell>23</cell><cell>ranking</cell><cell>18</cell><cell>code_to_code_translation</cell><cell>15</cell></row><row><cell>math_reasoning</cell><cell>22</cell><cell>planning</cell><cell>17</cell><cell>writing_blog_post</cell><cell>15</cell></row><row><cell>chitchat</cell><cell>21</cell><cell>classification_identification</cell><cell>17</cell><cell>reading_comprehension</cell><cell>14</cell></row><row><cell>value_judgment</cell><cell>21</cell><cell>exam_question_with_math</cell><cell>17</cell><cell>topic_modeling</cell><cell>14</cell></row><row><cell>brainstorming</cell><cell>21</cell><cell>writing_cooking_recipe</cell><cell>17</cell><cell>writing_advertisement</cell><cell>14</cell></row><row><cell>creative_writing</cell><cell>20</cell><cell>writing_email</cell><cell>17</cell><cell>title_generation</cell><cell>14</cell></row><row><cell>roleplay</cell><cell>20</cell><cell>information_extraction</cell><cell>17</cell><cell>keywords_extraction</cell><cell>14</cell></row><row><cell>verifying_fact</cell><cell>20</cell><cell>paraphrasing</cell><cell>17</cell><cell>writing_legal_document</cell><cell>14</cell></row><row><cell>counterfactual</cell><cell>19</cell><cell>code_correction_rewriting</cell><cell>17</cell><cell>writing_news_article</cell><cell>14</cell></row><row><cell>asking_how_to_question</cell><cell>19</cell><cell>data_analysis</cell><cell>16</cell><cell>writing_social_media_post</cell><cell>14</cell></row><row><cell>exam_question_without_math</cell><cell>19</cell><cell>writing_product_description</cell><cell>16</cell><cell>code_simplification</cell><cell>12</cell></row><row><cell>text_summarization</cell><cell>19</cell><cell>instructional_rewriting</cell><cell>16</cell><cell>writing_scientific_paper</cell><cell>12</cell></row><row><cell>recommendation</cell><cell>18</cell><cell>writing_presentation_script</cell><cell>16</cell><cell>writing_marketing_materials</cell><cell>8</cell></row><row><cell>question_generation</cell><cell>18</cell><cell>analyzing_general</cell><cell>16</cell><cell>note_summarization</cell><cell>4</cell></row><row><cell>text_to_text_translation</cell><cell>18</cell><cell>writing_job_application</cell><cell>16</cell><cell>writing_biography</cell><cell>4</cell></row><row><cell>writing_personal_essay</cell><cell>18</cell><cell>text_correction</cell><cell>16</cell><cell>others</cell><cell>27</cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_10"><head>Table 21 :</head><label>21</label><figDesc>Statistics for single training data: the distribution of GPT-4 ratings, and scenarios.</figDesc><table><row><cell></cell><cell></cell><cell cols="3">GPT-4 judgments</cell><cell></cell><cell></cell><cell></cell><cell cols="4">Human judgments</cell><cell></cell></row><row><cell>Baseline</cell><cell></cell><cell>SelFee</cell><cell></cell><cell cols="2">Vicuna</cell><cell></cell><cell></cell><cell>SelFee</cell><cell></cell><cell></cell><cell>Vicuna</cell><cell></cell></row><row><cell>judgment</cell><cell cols="12">Win Tie Lose Win Tie Lose Win Tie Lose Win Tie Lose</cell></row><row><cell>Summarization</cell><cell>12</cell><cell>0</cell><cell>0</cell><cell>9</cell><cell>0</cell><cell>3</cell><cell>10</cell><cell>1</cell><cell>1</cell><cell>11</cell><cell>1</cell><cell>0</cell></row><row><cell>Exam Questions</cell><cell>11</cell><cell>1</cell><cell>0</cell><cell>7</cell><cell>1</cell><cell>4</cell><cell>8</cell><cell>2</cell><cell>2</cell><cell>7</cell><cell>2</cell><cell>3</cell></row><row><cell>Code</cell><cell>20</cell><cell>0</cell><cell>0</cell><cell>15</cell><cell>0</cell><cell>5</cell><cell>15</cell><cell>2</cell><cell>3</cell><cell>11</cell><cell>5</cell><cell>4</cell></row><row><cell>Rewriting</cell><cell>18</cell><cell>0</cell><cell>2</cell><cell>18</cell><cell>0</cell><cell>2</cell><cell>14</cell><cell>5</cell><cell>1</cell><cell>14</cell><cell>4</cell><cell>2</cell></row><row><cell>Creative Writing</cell><cell>33</cell><cell>0</cell><cell>3</cell><cell></cell><cell>0</cell><cell>7</cell><cell>26</cell><cell>7</cell><cell>3</cell><cell>24</cell><cell>9</cell><cell>3</cell></row><row><cell>Functional Writing</cell><cell>37</cell><cell>0</cell><cell>3</cell><cell>28</cell><cell>0</cell><cell>12</cell><cell>37</cell><cell>2</cell><cell>1</cell><cell>32</cell><cell>6</cell><cell>2</cell></row><row><cell cols="2">General Communication 47</cell><cell>0</cell><cell>1</cell><cell>39</cell><cell>0</cell><cell>9</cell><cell>43</cell><cell>2</cell><cell>3</cell><cell>39</cell><cell>6</cell><cell>3</cell></row><row><cell>NLP Tasks</cell><cell>40</cell><cell>0</cell><cell>4</cell><cell>35</cell><cell>0</cell><cell>9</cell><cell>29</cell><cell>7</cell><cell>8</cell><cell>22</cell><cell>15</cell><cell>7</cell></row><row><cell>Overall</cell><cell>218</cell><cell>1</cell><cell>13</cell><cell>180</cell><cell>1</cell><cell>51</cell><cell cols="2">182 28</cell><cell>22</cell><cell cols="2">160 48</cell><cell>24</cell></row><row><cell>Baseline</cell><cell></cell><cell>L2Chat</cell><cell></cell><cell cols="3">ChatGPT</cell><cell></cell><cell>L2Chat</cell><cell></cell><cell></cell><cell>ChatGPT</cell><cell></cell></row><row><cell>judgment</cell><cell cols="12">Win Tie Lose Win Tie Lose Win Tie Lose Win Tie Lose</cell></row><row><cell>Summarization</cell><cell>10</cell><cell>0</cell><cell>2</cell><cell>12</cell><cell>0</cell><cell>0</cell><cell>10</cell><cell>1</cell><cell>1</cell><cell>10</cell><cell>1</cell><cell>1</cell></row><row><cell>Exam Questions</cell><cell>11</cell><cell>0</cell><cell>1</cell><cell>10</cell><cell>0</cell><cell>2</cell><cell>6</cell><cell>3</cell><cell>3</cell><cell>6</cell><cell>1</cell><cell>5</cell></row><row><cell>Code</cell><cell>18</cell><cell>0</cell><cell>2</cell><cell>16</cell><cell>0</cell><cell>4</cell><cell>11</cell><cell>5</cell><cell>4</cell><cell>8</cell><cell>6</cell><cell>6</cell></row><row><cell>Rewriting</cell><cell>17</cell><cell>1</cell><cell>2</cell><cell>14</cell><cell>0</cell><cell>6</cell><cell>10</cell><cell>7</cell><cell>3</cell><cell>9</cell><cell>8</cell><cell>3</cell></row><row><cell>Creative Writing</cell><cell>30</cell><cell>0</cell><cell>6</cell><cell>26</cell><cell>2</cell><cell>8</cell><cell>13</cell><cell>14</cell><cell>9</cell><cell>16</cell><cell>15</cell><cell>5</cell></row><row><cell>Functional Writing</cell><cell>32</cell><cell>0</cell><cell>8</cell><cell>22</cell><cell>2</cell><cell>16</cell><cell>23</cell><cell>13</cell><cell>4</cell><cell>23</cell><cell>14</cell><cell>3</cell></row><row><cell cols="2">General Communication 41</cell><cell>0</cell><cell>7</cell><cell>36</cell><cell>0</cell><cell>12</cell><cell>28</cell><cell>15</cell><cell>5</cell><cell>29</cell><cell>10</cell><cell>9</cell></row><row><cell>NLP Tasks</cell><cell>37</cell><cell>1</cell><cell>6</cell><cell>35</cell><cell>1</cell><cell>8</cell><cell>13</cell><cell>22</cell><cell>9</cell><cell>16</cell><cell>17</cell><cell>11</cell></row><row><cell>Overall</cell><cell>196</cell><cell>2</cell><cell>34</cell><cell>171</cell><cell>5</cell><cell>56</cell><cell cols="2">114 80</cell><cell cols="3">38 117 72</cell><cell>43</cell></row><row><cell>Baseline</cell><cell cols="3">WizardLM</cell><cell cols="2">GPT-4</cell><cell></cell><cell></cell><cell cols="2">WizardLM</cell><cell></cell><cell>GPT-4</cell><cell></cell></row><row><cell>judgment</cell><cell cols="12">Win Tie Lose Win Tie Lose Win Tie Lose Win Tie Lose</cell></row><row><cell>Summarization</cell><cell>10</cell><cell>0</cell><cell>2</cell><cell>8</cell><cell>0</cell><cell>4</cell><cell>11</cell><cell>1</cell><cell>0</cell><cell>6</cell><cell>2</cell><cell>4</cell></row><row><cell>Exam Questions</cell><cell>11</cell><cell>1</cell><cell>0</cell><cell>3</cell><cell>0</cell><cell>9</cell><cell>6</cell><cell>3</cell><cell>3</cell><cell>2</cell><cell>2</cell><cell>8</cell></row><row><cell>Code</cell><cell>17</cell><cell>0</cell><cell>3</cell><cell>12</cell><cell>0</cell><cell>8</cell><cell>9</cell><cell>7</cell><cell>4</cell><cell>6</cell><cell>5</cell><cell>9</cell></row><row><cell>Rewriting</cell><cell>15</cell><cell>1</cell><cell>4</cell><cell>12</cell><cell>0</cell><cell>8</cell><cell>12</cell><cell>6</cell><cell>2</cell><cell>12</cell><cell>3</cell><cell>5</cell></row><row><cell>Creative Writing</cell><cell>31</cell><cell>1</cell><cell>4</cell><cell>23</cell><cell>0</cell><cell>13</cell><cell>17</cell><cell>17</cell><cell>2</cell><cell>11</cell><cell>9</cell><cell>16</cell></row><row><cell>Functional Writing</cell><cell>30</cell><cell>1</cell><cell>9</cell><cell>17</cell><cell>1</cell><cell>22</cell><cell>24</cell><cell>12</cell><cell>4</cell><cell>27</cell><cell>6</cell><cell>7</cell></row><row><cell cols="2">General Communication 38</cell><cell>1</cell><cell>9</cell><cell>28</cell><cell>0</cell><cell>20</cell><cell>32</cell><cell>14</cell><cell>2</cell><cell>35</cell><cell>3</cell><cell>10</cell></row><row><cell>NLP Tasks</cell><cell>35</cell><cell>2</cell><cell>7</cell><cell>23</cell><cell>1</cell><cell>20</cell><cell>19</cell><cell>14</cell><cell>11</cell><cell>8</cell><cell>11</cell><cell>25</cell></row><row><cell>Overall</cell><cell>187</cell><cell>7</cell><cell>38</cell><cell>126</cell><cell>2</cell><cell cols="3">104 130 74</cell><cell>28</cell><cell cols="2">107 41</cell><cell>84</cell></row><row><cell cols="13">Table 22: Detailed comparison results between critiques generated by AUTO-J and baselines for</cell></row><row><cell cols="13">single-response evaluation. Results on left side are GPT-4 judgments, and results on right side</cell></row><row><cell cols="13">are human judgments. Vicuna, L2Chat, and WizardLM respectively stand for Vicuna-13B-v1.5,</cell></row><row><cell cols="5">LLaMA-2-Chat-13B, and WizardLM-13B-v1.2.</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row></table></figure>
<figure xmlns="http://www.tei-c.org/ns/1.0" type="table" xml:id="tab_11"><head>Table 23 :</head><label>23</label><figDesc></figDesc><table /><note><p><p>Values and ranking by Auto-J and GPT-4 for open-source LLMs on AlpacaEval. Value of AUTO-J is the model's average rating on AlpacaEval dataset assigned by AUTO-J in single-response evaluation protocol, and value of GPT-4 is the model's win-rate against Davinci003 determined by GPT-4 on AlpacaEval dataset. ∆ = Rank Auto-J -Rank</p> </p></note></figure>
			<note xmlns="http://www.tei-c.org/ns/1.0" place="foot" n="1" xml:id="foot_0"><p>Traditional metrics such as BLEU and ROUGE are capable of but not adept at conducting pairwise evaluation due to the worse performance in sample-level evaluation.(Bhandari et al.,  </p></note>
			<note xmlns="http://www.tei-c.org/ns/1.0" place="foot" xml:id="foot_1"><p>2020)   </p></note>
			<note xmlns="http://www.tei-c.org/ns/1.0" place="foot" n="2" xml:id="foot_2"><p>From LLaMA-2-chat family, Vicuna family, WizardLM family, Claude-2, ChatGPT and GPT-4</p></note>
			<note xmlns="http://www.tei-c.org/ns/1.0" place="foot" n="3" xml:id="foot_3"><p>53 models or their variants on https://tatsu-lab.github.io/alpaca_eval/ when the paper is written.</p></note>
			<note xmlns="http://www.tei-c.org/ns/1.0" place="foot" n="4" xml:id="foot_4"><p>This dataset is collected from https://sharegpt.com/, containing shared conversations with ChatGPT or GPT-4. We use a public available subset of it.</p></note>
		</body>
		<back>

			<div type="acknowledgement">
<div xmlns="http://www.tei-c.org/ns/1.0"><head>ACKNOWLEDGEMENT</head><p>We thank Chunpu Xu, Yuqing Yang for supporting the human annotation process. This project is partially supported by Qingyuan Research Project and Shanghai Artificial Intelligence Laboratory.</p></div>
			</div>

			<div type="annex">
<div xmlns="http://www.tei-c.org/ns/1.0"><head>A SCENARIO DESCRIPTION Summarization post_summarization</head><p>Write a summary for a reddit post. text_summarization Write a summary for a piece of text. note_summarization Write a note to summarize a piece of text.</p><p>Exam Questions</p><p>math_reasoning Write an answer with the step-by-step reasoning process for a math question. exam_question_with_math Solve an exam question (like fill-in-the-blank, multiple choice, problem solving, etc) with math involved. exam_question_without_math Solve an exam question (like fill-in-the-blank, multiple choice, problem solving, etc) with no math involved.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Rewriting text_simplification</head><p>Reduce the complexity of the vocabulary and sentence structure of text while retaining its original meaning.  Critiques from GPT-4 with scenario criteria as the system message</p><p>The response does not provide a plan for the fifth day of the trip, which is a significant omission given the user's request for a 5-day trip plan. This makes the plan incomplete and not fully aligned with the user's query. While the plan is generally well-structured, it lacks a clear timeline for each day's activities. Providing specific timeframes for each activity would make the plan more practical and easier to follow. The plan also does not consider potential constraints such as the feasibility of visiting multiple major attractions in a single day, which may not be realistic given factors like travel time, waiting lines, and the time needed to fully appreciate each site.</p><p>The plan could also be more creative and personalized. For example, it could suggest less touristy, off-the-beaten-path attractions or activities that could provide a unique and memorable experience. Lastly, the plan does not provide any information on the budget, which is an essential aspect of trip planning. Including estimated costs for transportation, accommodation, meals, and entrance fees would make the plan more thorough and useful.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>The combined final judgment</head><p>The response provides a detailed and comprehensive itinerary for a trip to France, however, it falls short in several areas. Firstly, the plan does not fully align with the user's query as it only provides an itinerary for four days, leaving the fifth day unaccounted for. This is a significant omission given the user's specific request for a 5-day trip plan.</p><p>Secondly, the feasibility of the proposed itinerary is questionable. For instance, the plan suggests a trip from Paris to the Côte d'Azur on Day 4, which is a considerable distance and would consume a significant amount of time. This could potentially leave the traveler with little time to enjoy the activities listed for the day. The plan also does not consider potential constraints such as the feasibility of visiting multiple major attractions in a single day. Factors like travel time, waiting lines, and the time needed to fully appreciate each site have not been taken into account. Providing specific timeframes for each activity would make the plan more practical and easier to follow. Furthermore, the plan lacks creativity and personalization. It could be improved by suggesting less touristy, off-the-beaten-path attractions or activities that could provide a unique and memorable experience. Lastly, the plan does not provide any information on the budget, which is an essential aspect of trip planning. Including estimated costs for transportation, accommodation, meals, and entrance fees would make the plan more thorough and useful.</p><p>In conclusion, while the response is well-structured and detailed, it lacks completeness, feasibility, and personalization. Therefore, the response could be significantly improved by addressing these issues. Rating: <ref type="bibr">[[5]</ref>] Table <ref type="table">19</ref>: An example for collecting a complete evaluation judgment for single-response evaluation. We show the query, response, raw GPT-4 critiques with and without scenario criteria as the system message, and the final judgment by combining the previous two critiques. Judgment by Auto-J (✓)</p><p>1. The key factors to distinguish these two responses:</p><p>-purpose: Both responses meet this criterion as they both inform the family about the booked flights for Thanksgiving. However, Response 2 does a better job by asking for any plans for meals or activities, which aligns more closely with the purpose of the email, which is to inform and also to plan for the Thanksgiving celebration.</p><p>-tone: Both responses maintain a casual and friendly tone, which is appropriate for the situation. However, Response 1's tone is more formal and serious, while Response 2's tone is more casual and friendly, which is more suitable for a family email.</p><p>-body: Both responses provide details about the trip in the body of the email. However, Response 2 provides more detailed information about the trip, such as the arrival and departure dates, and also asks for any plans for meals or activities, which makes it more informative and engaging.</p><p>-personalization: Response 2 is more personalized as it asks for any plans for meals or activities, which shows that the sender is interested in the family's plans and wants to be involved in the celebration.</p><p>-information richness: Response 2 provides more detailed information about the trip and also asks for any plans for meals or activities, which makes it richer in information compared to Response 1.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head n="2.">The final decision:</head><p>So, the final decision is Response 2. Although both responses meet the criteria and are well-written, Response 2 is better because it is more informative, engaging, and personalized, which makes it more suitable for a family email.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Judgment by ChatGPT (✗)</head><p>Assistant A's response is more detailed and provides a warm and enthusiastic tone. The email includes a subject line that captures attention and clearly states the purpose of the email. It also mentions surprises and encourages the family to keep an open mind, adding a touch of excitement. The email includes an attached flight itinerary for reference and invites the family to reach out with any questions or concerns. Overall, Assistant A's response is helpful, relevant, accurate, and provides a good level of detail. Assistant B's response is also helpful and relevant. It includes a subject line that states the purpose of the email and provides the necessary information about the dates of arrival and departure. It also offers to contribute and asks if there are any plans or items to bring. However, it lacks the same level of enthusiasm and detail as Assistant A's response. It does not mention surprises or provide an attached flight itinerary. While it is a straightforward and concise email, it could benefit from more personalization and excitement. Based on the factors mentioned above, Assistant A provides a better response.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>[[A]]</head><p>Judgment by LLaMA-2-Chat-13B (✗) Sure, I'd be happy to help! Here's my evaluation of the two responses: Both Assistant A and Assistant B provide helpful and relevant responses to the user's question. However, there are some differences in the quality of their responses. Assistant A's response is more detailed and creative. The email draft provided is well-written and includes a personal touch with the mention of potential surprises. The language used is also more formal and polished, which is appropriate for a family email. Additionally, Assistant A provides a flight itinerary for reference, which is a thoughtful touch. Assistant B's response, on the other hand, is more straightforward and to the point. The email draft provided is simple and easy to understand, but it lacks the personal touch and creativity of Assistant A's response. Assistant B also uses a more casual tone, which may not be appropriate for a family email. Based on these factors, I would say that Assistant A provides a better response to the user's question. Assistant A's response is more detailed, creative, and formal, while Assistant B's response is more straightforward and casual. Therefore, my final verdict is: Judgment by Vicuna-13B-v1.5 (✗)</p><p>After comparing the two responses, I find that Assistant A's response is more helpful, relevant, accurate, and creative. The response is well-structured, with a clear subject line and a warm greeting. It provides the necessary information about the travel plans and expresses excitement about spending time with the family. The response also includes a touch of creativity with the mention of surprises. Assistant B's response is also good, but it lacks the depth and creativity of Assistant A's response. The response is more straightforward and lacks a personal touch. It does ask for input on meals and activities, which is a good point, but it could have been more engaging.</p><p>Based on these factors, I conclude that Assistant A's response is better, and the final verdict is "[[A]]".</p><p>Judgment by WizarLM-13B-v1.2 (✗)</p><p>After evaluating both responses, I would rate Assistant A as the better one. Here's why: Assistant A's response is more concise and directly answers the user's question. It provides all the necessary information, including the dates of arrival and departure, and a brief message expressing excitement to spend Thanksgiving with the family. The email is well-structured and professional, with a clear subject line and a warm, personalized closing. Assistant B's response, while polite and friendly, is less focused on the main request. It asks for input on meals and activities, which is not directly related to the user's question. The response also seems to be more of a draft rather than a final email, as it invites the user to modify the email before sending it. Therefore, based on the factors of helpfulness, relevance, accuracy, depth, creativity, and level of detail, I would rate Assistant A as the better response.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Judgment by GPT-4 (✓)</head><p>Both assistants provided good responses, drafting an email that communicates the user's travel plans for Thanksgiving. However, Assistant B's response is slightly more interactive and considerate, asking if there's anything they should bring or if there are any plans already in the works for meals or activities. This shows a willingness to contribute to the family gathering. Assistant B also acknowledges that other family members might be traveling, wishing them safe travels. Therefore, Assistant B's response is more comprehensive and thoughtful.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Final Verdict: [[B]]</head><p>Table <ref type="table">25</ref>: The complete judgments given by different models on a pairwise response comparison example. We mark if each judgment agrees with human preference as well, where ✓ stands for agreement and ✗ stands for disagreement. (Part 2)</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Query</head><p>What's the best way to cook salmon on the grill.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Response</head><p>The best way to cook salmon on the grill is to preheat the grill to medium-high heat, brush the salmon with olive oil, season with salt and pepper, and place skin-side down on the grill. Allow the salmon to cook for 5-7 minutes, until the skin is crispy and the fish flakes easily with a fork. Flip the salmon, and cook for an additional 3-4 minutes. Serve with your favorite sides and enjoy.</p><p>Critique from AUTO-J</p><p>The response provided is generally informative and offers a basic method for grilling salmon. However, it lacks depth and detail in several key areas. A main shortcoming is the absence of specific temperatures or cooking times, which are crucial for achieving the perfect grilled salmon. The response vaguely states "medium-high heat" and "5-7 minutes" for the first side of cooking, and "3-4 minutes" for the second side. More precise guidance would be beneficial for users who may not be experienced grill masters.</p><p>The response also fails to address the quality of the salmon that is required for the recipe. It is essential to mention the type of salmon that would be best suited for this method of cooking. Furthermore, the response does not provide detailed information on how to determine when the salmon is cooked. This could potentially lead to overcooked or undercooked salmon, which is a common problem for novice cooks.</p><p>The response could have been more interactive and personalized by asking the user about their specific grill or their experience with cooking salmon. This would have made the response more engaging and helpful. Additionally, the response could have been more professional by citing credible sources or providing references to back up the cooking instructions. This would have added credibility to the response.</p><p>Lastly, the response missed an opportunity to provide a comprehensive answer by not addressing potential questions that the user might have, such as what to do if the salmon sticks to the grill or how to ensure the salmon is cooked evenly.</p><p>In conclusion, while the response is a good starting point, it lacks the necessary detail and personal touch that would make it a truly excellent response.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Critique from GPT-4 (AUTO-J wins)</head><p>The response is quite comprehensive and provides a clear, step-by-step guide on how to grill salmon. It includes details such as the heat level of the grill, how to prepare the salmon, how long to cook it on each side, and how to determine when it's done. However, it could be improved by including some tips on choosing the best piece of salmon for grilling, or suggesting some specific side dishes that pair well with grilled salmon.</p><p>Critique from ChatGPT (AUTO-J wins)</p><p>1. Lack of details: The response could benefit from providing more specific instructions. For example, it does not mention the thickness of the salmon fillet or whether the grill should be covered or uncovered during cooking.</p><p>2. Absence of alternative methods: While the provided method is a good starting point, it would be helpful to mention other techniques such as using a cedar plank or aluminum foil to cook the salmon.</p><p>3. Limited seasoning options: While salt and pepper are mentioned as seasonings, it would be nice to suggest additional herbs or spices that could enhance the flavor of the salmon.</p></div>
<div xmlns="http://www.tei-c.org/ns/1.0"><head>Critique from SelFee (AUTO-J wins)</head><p>The answer is clear and concise, providing step-by-step instructions on how to cook salmon on the grill. However, it would be helpful to mention the ideal grill temperature and suggest using a grill brush to clean the grates before cooking. Additionally, it would be useful to suggest using a thermometer to check the internal temperature of the salmon to ensure it is cooked to a safe temperature. Overall, I would give this answer a score of 8. Revision is needed.</p><p>Critique from LLaMA-2-Chat-13B (AUTO-J wins) 1. Lack of detail: The response could benefit from more detail on the cooking process. For example, the user might want to know the exact temperature of the grill, or how to tell when the salmon is done.</p><p>2. Limited seasoning options: The response only mentions salt and pepper as seasoning options, but there are many other delicious seasonings that could be used to enhance the flavor of the salmon.</p><p>3. No visuals: The response does not include any visuals or images to help the user understand the cooking process or the final product.</p><p>Critique from Vicuna-13B-v1.5 (AUTO-J wins)</p><p>Critique 1: The response provides a clear and concise method for cooking salmon on the grill. It includes important details such as preheating the grill and brushing the salmon with olive oil. However, it would be helpful to include more information about the cooking time and temperature, as well as tips for checking the doneness of the salmon.</p><p>Critique 2: The response is well-written and easy to follow, with clear instructions for seasoning and cooking the salmon. It also suggests serving the salmon with favorite sides, which adds a personal touch. However, the response could benefit from more variety in the language and a few more details about the cooking process, such as the importance of flipping the salmon and monitoring the grill temperature.</p><p>Critique from Wizardlm-13B-v1.2 (Tie)</p><p>1. The response is concise and provides a clear, step-by-step guide on how to cook salmon on the grill. It mentions the preheating of the grill, seasoning the salmon, and the cooking time, which are all essential details.</p><p>2. The response could benefit from more detail on the type of olive oil to use and the seasoning options. For example, extra virgin olive oil is a good choice for grilling, and other seasoning options could include lemon juice, garlic powder, or smoked paprika.</p><p>3. The response does not mention the importance of oiling the grates before cooking, which can prevent the salmon from sticking to the grill.</p><p>4. The response does not provide any information on how to tell if the salmon is cooked properly. It would be helpful to mention that the salmon should flake easily with a fork and have a slight pink color in the center.</p><p>5. The response does not suggest any sides to serve with the salmon, which could be a helpful addition for users who are unsure of what to serve with grilled salmon.</p><p>Table 26: Critiques given by different models for a response. We remove the "rating" part in each critique, and mark the comparison results between AUTO-J and baselines judged by human.</p></div>			</div>
			<div type="references">

				<listBibl>

<biblStruct xml:id="b0">
	<monogr>
		<title level="m" type="main">Training a helpful and harmless assistant with reinforcement learning from human feedback</title>
		<author>
			<persName><forename type="first">Yuntao</forename><surname>Bai</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Andy</forename><surname>Jones</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kamal</forename><surname>Ndousse</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Amanda</forename><surname>Askell</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Anna</forename><surname>Chen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Nova</forename><surname>Dassarma</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dawn</forename><surname>Drain</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Stanislav</forename><surname>Fort</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Deep</forename><surname>Ganguli</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tom</forename><surname>Henighan</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2204.05862</idno>
		<imprint>
			<date type="published" when="2022">2022</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Yuntao Bai, Andy Jones, Kamal Ndousse, Amanda Askell, Anna Chen, Nova DasSarma, Dawn Drain, Stanislav Fort, Deep Ganguli, Tom Henighan, et al. Training a helpful and harmless assistant with reinforcement learning from human feedback. arXiv preprint arXiv:2204.05862, 2022a.</note>
</biblStruct>

<biblStruct xml:id="b1">
	<monogr>
		<author>
			<persName><forename type="first">Yuntao</forename><surname>Bai</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Saurav</forename><surname>Kadavath</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sandipan</forename><surname>Kundu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Amanda</forename><surname>Askell</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jackson</forename><surname>Kernion</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Andy</forename><surname>Jones</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Anna</forename><surname>Chen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Anna</forename><surname>Goldie</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Azalia</forename><surname>Mirhoseini</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Cameron</forename><surname>Mckinnon</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2212.08073</idno>
		<title level="m">Constitutional ai: Harmlessness from ai feedback</title>
		<imprint>
			<date type="published" when="2022">2022</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Yuntao Bai, Saurav Kadavath, Sandipan Kundu, Amanda Askell, Jackson Kernion, Andy Jones, Anna Chen, Anna Goldie, Azalia Mirhoseini, Cameron McKinnon, et al. Constitutional ai: Harmlessness from ai feedback. arXiv preprint arXiv:2212.08073, 2022b.</note>
</biblStruct>

<biblStruct xml:id="b2">
	<analytic>
		<title level="a" type="main">Reevaluating evaluation in text summarization</title>
		<author>
			<persName><forename type="first">Manik</forename><surname>Bhandari</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pranav</forename><surname>Narayan Gour</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Atabak</forename><surname>Ashfaq</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pengfei</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Graham</forename><surname>Neubig</surname></persName>
		</author>
		<idno type="DOI">10.18653/v1/2020.emnlp-main.751</idno>
		<ptr target="https://aclanthology.org/2020.emnlp-main.751" />
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP)</title>
		<meeting>the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP)</meeting>
		<imprint>
			<publisher>Association for Computational Linguistics</publisher>
			<date type="published" when="2020-11">November 2020</date>
			<biblScope unit="page" from="9347" to="9359" />
		</imprint>
	</monogr>
	<note type="raw_reference">Manik Bhandari, Pranav Narayan Gour, Atabak Ashfaq, Pengfei Liu, and Graham Neubig. Re- evaluating evaluation in text summarization. In Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP), pp. 9347-9359, Online, November 2020. Association for Computational Linguistics. doi: 10.18653/v1/2020.emnlp-main.751. URL https: //aclanthology.org/2020.emnlp-main.751.</note>
</biblStruct>

<biblStruct xml:id="b3">
	<analytic>
		<title level="a" type="main">Language models are few-shot learners</title>
		<author>
			<persName><forename type="first">Tom</forename><surname>Brown</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Benjamin</forename><surname>Mann</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Nick</forename><surname>Ryder</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Melanie</forename><surname>Subbiah</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jared</forename><forename type="middle">D</forename><surname>Kaplan</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Prafulla</forename><surname>Dhariwal</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Arvind</forename><surname>Neelakantan</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pranav</forename><surname>Shyam</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Girish</forename><surname>Sastry</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Amanda</forename><surname>Askell</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">Advances in neural information processing systems</title>
		<imprint>
			<date type="published" when="2020">2020</date>
			<biblScope unit="volume">33</biblScope>
			<biblScope unit="page" from="1877" to="1901" />
		</imprint>
	</monogr>
	<note type="raw_reference">Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. Advances in neural information processing systems, 33:1877-1901, 2020.</note>
</biblStruct>

<biblStruct xml:id="b4">
	<monogr>
		<author>
			<persName><forename type="first">Sébastien</forename><surname>Bubeck</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Varun</forename><surname>Chandrasekaran</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ronen</forename><surname>Eldan</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Johannes</forename><surname>Gehrke</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Eric</forename><surname>Horvitz</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ece</forename><surname>Kamar</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Peter</forename><surname>Lee</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yin</forename><surname>Tat Lee</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yuanzhi</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Scott</forename><surname>Lundberg</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2303.12712</idno>
		<title level="m">Sparks of artificial general intelligence: Early experiments with gpt-4</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Sébastien Bubeck, Varun Chandrasekaran, Ronen Eldan, Johannes Gehrke, Eric Horvitz, Ece Kamar, Peter Lee, Yin Tat Lee, Yuanzhi Li, Scott Lundberg, et al. Sparks of artificial general intelligence: Early experiments with gpt-4. arXiv preprint arXiv:2303.12712, 2023.</note>
</biblStruct>

<biblStruct xml:id="b5">
	<monogr>
		<title level="m" type="main">Training deep nets with sublinear memory cost</title>
		<author>
			<persName><forename type="first">Tianqi</forename><surname>Chen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Bing</forename><surname>Xu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chiyuan</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Carlos</forename><surname>Guestrin</surname></persName>
		</author>
		<idno type="arXiv">arXiv:1604.06174</idno>
		<imprint>
			<date type="published" when="2016">2016</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Tianqi Chen, Bing Xu, Chiyuan Zhang, and Carlos Guestrin. Training deep nets with sublinear memory cost. arXiv preprint arXiv:1604.06174, 2016.</note>
</biblStruct>

<biblStruct xml:id="b6">
	<monogr>
		<title level="m" type="main">Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality</title>
		<author>
			<persName><forename type="first">Wei-Lin</forename><surname>Chiang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhuohan</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zi</forename><surname>Lin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ying</forename><surname>Sheng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhanghao</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Hao</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Lianmin</forename><surname>Zheng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Siyuan</forename><surname>Zhuang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yonghao</forename><surname>Zhuang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Joseph</forename><forename type="middle">E</forename><surname>Gonzalez</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ion</forename><surname>Stoica</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Eric</forename><forename type="middle">P</forename><surname>Xing</surname></persName>
		</author>
		<ptr target="https://lmsys.org/blog/2023-03-30-vicuna/" />
		<imprint>
			<date type="published" when="2023-03">March 2023</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Wei-Lin Chiang, Zhuohan Li, Zi Lin, Ying Sheng, Zhanghao Wu, Hao Zhang, Lianmin Zheng, Siyuan Zhuang, Yonghao Zhuang, Joseph E. Gonzalez, Ion Stoica, and Eric P. Xing. Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality, March 2023. URL https: //lmsys.org/blog/2023-03-30-vicuna/.</note>
</biblStruct>

<biblStruct xml:id="b7">
	<monogr>
		<author>
			<persName><forename type="first">Aakanksha</forename><surname>Chowdhery</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sharan</forename><surname>Narang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jacob</forename><surname>Devlin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Maarten</forename><surname>Bosma</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Gaurav</forename><surname>Mishra</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Adam</forename><surname>Roberts</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Paul</forename><surname>Barham</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Hyung</forename><forename type="middle">Won</forename><surname>Chung</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Charles</forename><surname>Sutton</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sebastian</forename><surname>Gehrmann</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2204.02311</idno>
		<title level="m">Scaling language modeling with pathways</title>
		<imprint>
			<date type="published" when="2022">2022</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Aakanksha Chowdhery, Sharan Narang, Jacob Devlin, Maarten Bosma, Gaurav Mishra, Adam Roberts, Paul Barham, Hyung Won Chung, Charles Sutton, Sebastian Gehrmann, et al. Palm: Scaling language modeling with pathways. arXiv preprint arXiv:2204.02311, 2022.</note>
</biblStruct>

<biblStruct xml:id="b8">
	<monogr>
		<title level="m" type="main">Flashattention-2: Faster attention with better parallelism and work partitioning</title>
		<author>
			<persName><forename type="first">Tri</forename><surname>Dao</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2307.08691</idno>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Tri Dao. Flashattention-2: Faster attention with better parallelism and work partitioning. arXiv preprint arXiv:2307.08691, 2023.</note>
</biblStruct>

<biblStruct xml:id="b9">
	<analytic>
		<title level="a" type="main">Flashattention: Fast and memoryefficient exact attention with io-awareness</title>
		<author>
			<persName><forename type="first">Tri</forename><surname>Dao</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dan</forename><surname>Fu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Stefano</forename><surname>Ermon</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Atri</forename><surname>Rudra</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Christopher</forename><surname>Ré</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="j">Advances in Neural Information Processing Systems</title>
		<imprint>
			<biblScope unit="volume">35</biblScope>
			<biblScope unit="page" from="16344" to="16359" />
			<date type="published" when="2022">2022</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Tri Dao, Dan Fu, Stefano Ermon, Atri Rudra, and Christopher Ré. Flashattention: Fast and memory- efficient exact attention with io-awareness. Advances in Neural Information Processing Systems, 35:16344-16359, 2022.</note>
</biblStruct>

<biblStruct xml:id="b10">
	<analytic>
		<title level="a" type="main">BERT: Pre-training of deep bidirectional transformers for language understanding</title>
		<author>
			<persName><forename type="first">Jacob</forename><surname>Devlin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ming-Wei</forename><surname>Chang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kenton</forename><surname>Lee</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kristina</forename><surname>Toutanova</surname></persName>
		</author>
		<idno type="DOI">10.18653/v1/N19-1423</idno>
		<ptr target="https://aclanthology.org/N19-1423" />
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies</title>
		<title level="s">Long and Short Papers</title>
		<meeting>the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies<address><addrLine>Minneapolis, Minnesota</addrLine></address></meeting>
		<imprint>
			<publisher>Association for Computational Linguistics</publisher>
			<date type="published" when="2019-06">June 2019</date>
			<biblScope unit="volume">1</biblScope>
			<biblScope unit="page" from="4171" to="4186" />
		</imprint>
	</monogr>
	<note type="raw_reference">Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. BERT: Pre-training of deep bidirectional transformers for language understanding. In Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers), pp. 4171-4186, Minneapolis, Minnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-1423. URL https: //aclanthology.org/N19-1423.</note>
</biblStruct>

<biblStruct xml:id="b11">
	<monogr>
		<title level="m" type="main">Alpacafarm: A simulation framework for methods that learn from human feedback</title>
		<author>
			<persName><forename type="first">Yann</forename><surname>Dubois</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xuechen</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Rohan</forename><surname>Taori</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tianyi</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ishaan</forename><surname>Gulrajani</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jimmy</forename><surname>Ba</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Carlos</forename><surname>Guestrin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Percy</forename><surname>Liang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tatsunori B</forename><surname>Hashimoto</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2305.14387</idno>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Yann Dubois, Xuechen Li, Rohan Taori, Tianyi Zhang, Ishaan Gulrajani, Jimmy Ba, Carlos Guestrin, Percy Liang, and Tatsunori B Hashimoto. Alpacafarm: A simulation framework for methods that learn from human feedback. arXiv preprint arXiv:2305.14387, 2023.</note>
</biblStruct>

<biblStruct xml:id="b12">
	<analytic>
		<title level="a" type="main">Understanding dataset difficulty with V-usable information</title>
		<author>
			<persName><forename type="first">Kawin</forename><surname>Ethayarajh</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yejin</forename><surname>Choi</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Swabha</forename><surname>Swayamdipta</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">International Conference on Machine Learning</title>
		<imprint>
			<date type="published" when="2022">2022</date>
			<biblScope unit="page" from="5988" to="6008" />
		</imprint>
	</monogr>
	<note type="raw_reference">Kawin Ethayarajh, Yejin Choi, and Swabha Swayamdipta. Understanding dataset difficulty with V-usable information. In International Conference on Machine Learning, pp. 5988-6008. PMLR, 2022.</note>
</biblStruct>

<biblStruct xml:id="b13">
	<monogr>
		<author>
			<persName><forename type="first">Jinlan</forename><surname>Fu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">See-Kiong</forename><surname>Ng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhengbao</forename><surname>Jiang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pengfei</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><surname>Gptscore</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2302.04166</idno>
		<title level="m">Evaluate as you desire</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Jinlan Fu, See-Kiong Ng, Zhengbao Jiang, and Pengfei Liu. Gptscore: Evaluate as you desire. arXiv preprint arXiv:2302.04166, 2023.</note>
</biblStruct>

<biblStruct xml:id="b14">
	<analytic>
		<title level="a" type="main">Scaling laws for reward model overoptimization</title>
		<author>
			<persName><forename type="first">Leo</forename><surname>Gao</surname></persName>
		</author>
		<author>
			<persName><forename type="first">John</forename><surname>Schulman</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jacob</forename><surname>Hilton</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">International Conference on Machine Learning</title>
		<imprint>
			<date type="published" when="2023">2023</date>
			<biblScope unit="page" from="10835" to="10866" />
		</imprint>
	</monogr>
	<note type="raw_reference">Leo Gao, John Schulman, and Jacob Hilton. Scaling laws for reward model overoptimization. In International Conference on Machine Learning, pp. 10835-10866. PMLR, 2023.</note>
</biblStruct>

<biblStruct xml:id="b15">
	<monogr>
		<title level="m" type="main">synthetic-instruct-gptj-pairwise</title>
		<author>
			<persName><forename type="first">Alex</forename><surname>Havrilla</surname></persName>
		</author>
		<ptr target="https://huggingface.co/datasets/Dahoas/synthetic-instruct-gptj-pairwise" />
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Alex Havrilla. synthetic-instruct-gptj-pairwise., 2023. URL https://huggingface.co/datasets/ Dahoas/synthetic-instruct-gptj-pairwise.</note>
</biblStruct>

<biblStruct xml:id="b16">
	<monogr>
		<author>
			<persName><forename type="first">Jiaming</forename><surname>Ji</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Mickel</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Juntao</forename><surname>Dai</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xuehai</forename><surname>Pan</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chi</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ce</forename><surname>Bian</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ruiyang</forename><surname>Sun</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yizhou</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yaodong</forename><surname>Yang</surname></persName>
		</author>
		<author>
			<persName><surname>Beavertails</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2307.04657</idno>
		<title level="m">Towards improved safety alignment of llm via a human-preference dataset</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Jiaming Ji, Mickel Liu, Juntao Dai, Xuehai Pan, Chi Zhang, Ce Bian, Ruiyang Sun, Yizhou Wang, and Yaodong Yang. Beavertails: Towards improved safety alignment of llm via a human-preference dataset. arXiv preprint arXiv:2307.04657, 2023.</note>
</biblStruct>

<biblStruct xml:id="b17">
	<monogr>
		<author>
			<persName><forename type="first">Andreas</forename><surname>Köpf</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yannic</forename><surname>Kilcher</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sotiris</forename><surname>Dimitri Von Rütte</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhi-Rui</forename><surname>Anagnostidis</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Keith</forename><surname>Tam</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Abdullah</forename><surname>Stevens</surname></persName>
		</author>
		<author>
			<persName><surname>Barhoum</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Minh</forename><surname>Nguyen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Oliver</forename><surname>Duc</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Richárd</forename><surname>Stanley</surname></persName>
		</author>
		<author>
			<persName><surname>Nagyfi</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2304.07327</idno>
		<title level="m">Openassistant conversations-democratizing large language model alignment</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Andreas Köpf, Yannic Kilcher, Dimitri von Rütte, Sotiris Anagnostidis, Zhi-Rui Tam, Keith Stevens, Abdullah Barhoum, Nguyen Minh Duc, Oliver Stanley, Richárd Nagyfi, et al. Openassistant conversations-democratizing large language model alignment. arXiv preprint arXiv:2304.07327, 2023.</note>
</biblStruct>

<biblStruct xml:id="b18">
	<analytic>
		<title level="a" type="main">BART: Denoising sequence-to-sequence pre-training for natural language generation, translation, and comprehension</title>
		<author>
			<persName><forename type="first">Mike</forename><surname>Lewis</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yinhan</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Naman</forename><surname>Goyal</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Marjan</forename><surname>Ghazvininejad</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Abdelrahman</forename><surname>Mohamed</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Omer</forename><surname>Levy</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Veselin</forename><surname>Stoyanov</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Luke</forename><surname>Zettlemoyer</surname></persName>
		</author>
		<idno type="DOI">10.18653/v1/2020.acl-main.703</idno>
		<ptr target="https://aclanthology.org/2020.acl-main.703" />
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics</title>
		<meeting>the 58th Annual Meeting of the Association for Computational Linguistics</meeting>
		<imprint>
			<publisher>Association for Computational Linguistics</publisher>
			<date type="published" when="2020-07">July 2020</date>
			<biblScope unit="page" from="7871" to="7880" />
		</imprint>
	</monogr>
	<note type="raw_reference">Mike Lewis, Yinhan Liu, Naman Goyal, Marjan Ghazvininejad, Abdelrahman Mohamed, Omer Levy, Veselin Stoyanov, and Luke Zettlemoyer. BART: Denoising sequence-to-sequence pre-training for natural language generation, translation, and comprehension. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pp. 7871-7880, Online, July 2020. Association for Computational Linguistics. doi: 10.18653/v1/2020.acl-main.703. URL https://aclanthology.org/2020.acl-main.703.</note>
</biblStruct>

<biblStruct xml:id="b19">
	<monogr>
		<author>
			<persName><forename type="first">Vineet</forename><surname>Hunter Lightman</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yura</forename><surname>Kosaraju</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Harri</forename><surname>Burda</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Bowen</forename><surname>Edwards</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Teddy</forename><surname>Baker</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jan</forename><surname>Lee</surname></persName>
		</author>
		<author>
			<persName><forename type="first">John</forename><surname>Leike</surname></persName>
		</author>
		<author>
			<persName><surname>Schulman</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2305.20050</idno>
		<title level="m">Ilya Sutskever, and Karl Cobbe. Let&apos;s verify step by step</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Hunter Lightman, Vineet Kosaraju, Yura Burda, Harri Edwards, Bowen Baker, Teddy Lee, Jan Leike, John Schulman, Ilya Sutskever, and Karl Cobbe. Let&apos;s verify step by step. arXiv preprint arXiv:2305.20050, 2023.</note>
</biblStruct>

<biblStruct xml:id="b20">
	<analytic>
		<title level="a" type="main">ROUGE: A package for automatic evaluation of summaries</title>
		<author>
			<persName><forename type="first">Chin-Yew</forename><surname>Lin</surname></persName>
		</author>
		<ptr target="https://aclanthology.org/W04-1013" />
	</analytic>
	<monogr>
		<title level="m">Text Summarization Branches Out</title>
		<meeting><address><addrLine>Barcelona, Spain</addrLine></address></meeting>
		<imprint>
			<publisher>Association for Computational Linguistics</publisher>
			<date type="published" when="2004-07">July 2004</date>
			<biblScope unit="page" from="74" to="81" />
		</imprint>
	</monogr>
	<note type="raw_reference">Chin-Yew Lin. ROUGE: A package for automatic evaluation of summaries. In Text Summarization Branches Out, pp. 74-81, Barcelona, Spain, July 2004. Association for Computational Linguistics. URL https://aclanthology.org/W04-1013.</note>
</biblStruct>

<biblStruct xml:id="b21">
	<monogr>
		<author>
			<persName><forename type="first">Yang</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dan</forename><surname>Iter</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yichong</forename><surname>Xu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Shuohang</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ruochen</forename><surname>Xu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chenguang</forename><surname>Zhu</surname></persName>
		</author>
		<author>
			<persName><surname>Gpteval</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2303.16634</idno>
		<title level="m">Nlg evaluation using gpt-4 with better human alignment</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Yang Liu, Dan Iter, Yichong Xu, Shuohang Wang, Ruochen Xu, and Chenguang Zhu. Gpteval: Nlg evaluation using gpt-4 with better human alignment. arXiv preprint arXiv:2303.16634, 2023.</note>
</biblStruct>

<biblStruct xml:id="b22">
	<monogr>
		<author>
			<persName><forename type="first">Ilya</forename><surname>Loshchilov</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Frank</forename><surname>Hutter</surname></persName>
		</author>
		<idno type="arXiv">arXiv:1711.05101</idno>
		<title level="m">Decoupled weight decay regularization</title>
		<imprint>
			<date type="published" when="2017">2017</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Ilya Loshchilov and Frank Hutter. Decoupled weight decay regularization. arXiv preprint arXiv:1711.05101, 2017.</note>
</biblStruct>

<biblStruct xml:id="b23">
	<analytic>
		<title level="a" type="main">Distributed representations of words and phrases and their compositionality</title>
		<author>
			<persName><forename type="first">Tomas</forename><surname>Mikolov</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ilya</forename><surname>Sutskever</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kai</forename><surname>Chen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Greg</forename><forename type="middle">S</forename><surname>Corrado</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeff</forename><surname>Dean</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="j">Advances in neural information processing systems</title>
		<imprint>
			<biblScope unit="volume">26</biblScope>
			<date type="published" when="2013">2013</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Tomas Mikolov, Ilya Sutskever, Kai Chen, Greg S Corrado, and Jeff Dean. Distributed representations of words and phrases and their compositionality. Advances in neural information processing systems, 26, 2013.</note>
</biblStruct>

<biblStruct xml:id="b24">
	<monogr>
		<author>
			<persName><forename type="first">Reiichiro</forename><surname>Nakano</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jacob</forename><surname>Hilton</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Suchir</forename><surname>Balaji</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeff</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Long</forename><surname>Ouyang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Christina</forename><surname>Kim</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Christopher</forename><surname>Hesse</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Shantanu</forename><surname>Jain</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Vineet</forename><surname>Kosaraju</surname></persName>
		</author>
		<author>
			<persName><forename type="first">William</forename><surname>Saunders</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2112.09332</idno>
		<title level="m">Browser-assisted question-answering with human feedback</title>
		<imprint>
			<date type="published" when="2021">2021</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Reiichiro Nakano, Jacob Hilton, Suchir Balaji, Jeff Wu, Long Ouyang, Christina Kim, Christopher Hesse, Shantanu Jain, Vineet Kosaraju, William Saunders, et al. Webgpt: Browser-assisted question-answering with human feedback. arXiv preprint arXiv:2112.09332, 2021.</note>
</biblStruct>

<biblStruct xml:id="b25">
	<analytic>
		<title level="a" type="main">Better summarization evaluation with word embeddings for rouge</title>
		<author>
			<persName><forename type="first">Ping</forename><surname>Jun</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Viktoria</forename><surname>Ng</surname></persName>
		</author>
		<author>
			<persName><surname>Abrecht</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 2015 Conference on Empirical Methods in Natural Language Processing</title>
		<meeting>the 2015 Conference on Empirical Methods in Natural Language Processing</meeting>
		<imprint>
			<date type="published" when="2015">2015</date>
			<biblScope unit="page" from="1925" to="1930" />
		</imprint>
	</monogr>
	<note type="raw_reference">Jun Ping Ng and Viktoria Abrecht. Better summarization evaluation with word embeddings for rouge. In Proceedings of the 2015 Conference on Empirical Methods in Natural Language Processing, pp. 1925-1930, 2015.</note>
</biblStruct>

<biblStruct xml:id="b26">
	<monogr>
		<idno type="arXiv">arXiv:2303.08774</idno>
		<title level="m">OpenAI. Gpt-4 technical report</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">OpenAI. Gpt-4 technical report. arXiv preprint arXiv:2303.08774, 2023.</note>
</biblStruct>

<biblStruct xml:id="b27">
	<analytic>
		<title level="a" type="main">Training language models to follow instructions with human feedback</title>
		<author>
			<persName><forename type="first">Long</forename><surname>Ouyang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeffrey</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xu</forename><surname>Jiang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Diogo</forename><surname>Almeida</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Carroll</forename><surname>Wainwright</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pamela</forename><surname>Mishkin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chong</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sandhini</forename><surname>Agarwal</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Katarina</forename><surname>Slama</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Alex</forename><surname>Ray</surname></persName>
		</author>
		<author>
			<persName><forename type="first">John</forename><surname>Schulman</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jacob</forename><surname>Hilton</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Fraser</forename><surname>Kelton</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Luke</forename><surname>Miller</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Maddie</forename><surname>Simens</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Amanda</forename><surname>Askell</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Peter</forename><surname>Welinder</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jan</forename><surname>Paul F Christiano</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ryan</forename><surname>Leike</surname></persName>
		</author>
		<author>
			<persName><surname>Lowe</surname></persName>
		</author>
		<ptr target="https://proceedings.neurips.cc/paper_files/paper/2022/file/b" />
	</analytic>
	<monogr>
		<title level="m">Advances in Neural Information Processing Systems</title>
		<editor>
			<persName><forename type="first">S</forename><surname>Koyejo</surname></persName>
		</editor>
		<editor>
			<persName><forename type="first">S</forename><surname>Mohamed</surname></persName>
		</editor>
		<editor>
			<persName><forename type="first">A</forename><surname>Agarwal</surname></persName>
		</editor>
		<editor>
			<persName><forename type="first">D</forename><surname>Belgrave</surname></persName>
		</editor>
		<editor>
			<persName><forename type="first">K</forename><surname>Cho</surname></persName>
		</editor>
		<editor>
			<persName><forename type="first">A</forename><surname>Oh</surname></persName>
		</editor>
		<imprint>
			<publisher>Curran Associates, Inc</publisher>
			<date type="published" when="2022">2022</date>
			<biblScope unit="volume">35</biblScope>
			<biblScope unit="page" from="27730" to="27744" />
		</imprint>
	</monogr>
	<note>1efde53be364a73914f58805a001731-Paper-Conference.pdf</note>
	<note type="raw_reference">Long Ouyang, Jeffrey Wu, Xu Jiang, Diogo Almeida, Carroll Wainwright, Pamela Mishkin, Chong Zhang, Sandhini Agarwal, Katarina Slama, Alex Ray, John Schulman, Jacob Hilton, Fraser Kelton, Luke Miller, Maddie Simens, Amanda Askell, Peter Welinder, Paul F Christiano, Jan Leike, and Ryan Lowe. Training language models to follow instructions with human feed- back. In S. Koyejo, S. Mohamed, A. Agarwal, D. Belgrave, K. Cho, and A. Oh (eds.), Ad- vances in Neural Information Processing Systems, volume 35, pp. 27730-27744. Curran Asso- ciates, Inc., 2022. URL https://proceedings.neurips.cc/paper_files/paper/2022/file/ b1efde53be364a73914f58805a001731-Paper-Conference.pdf.</note>
</biblStruct>

<biblStruct xml:id="b28">
	<analytic>
		<title level="a" type="main">Bleu: a method for automatic evaluation of machine translation</title>
		<author>
			<persName><forename type="first">Kishore</forename><surname>Papineni</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Salim</forename><surname>Roukos</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Todd</forename><surname>Ward</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Wei-Jing</forename><surname>Zhu</surname></persName>
		</author>
		<idno type="DOI">10.3115/1073083.1073135</idno>
		<ptr target="https://aclanthology.org/P02-1040" />
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 40th Annual Meeting of the Association for Computational Linguistics</title>
		<meeting>the 40th Annual Meeting of the Association for Computational Linguistics<address><addrLine>Philadelphia, Pennsylvania, USA</addrLine></address></meeting>
		<imprint>
			<publisher>Association for Computational Linguistics</publisher>
			<date type="published" when="2002-07">July 2002</date>
			<biblScope unit="page" from="311" to="318" />
		</imprint>
	</monogr>
	<note type="raw_reference">Kishore Papineni, Salim Roukos, Todd Ward, and Wei-Jing Zhu. Bleu: a method for automatic evaluation of machine translation. In Proceedings of the 40th Annual Meeting of the Association for Computational Linguistics, pp. 311-318, Philadelphia, Pennsylvania, USA, July 2002. Association for Computational Linguistics. doi: 10.3115/1073083.1073135. URL https://aclanthology. org/P02-1040.</note>
</biblStruct>

<biblStruct xml:id="b29">
	<analytic>
		<title level="a" type="main">Deep contextualized word representations</title>
		<author>
			<persName><forename type="first">Matthew</forename><forename type="middle">E</forename><surname>Peters</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Mark</forename><surname>Neumann</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Mohit</forename><surname>Iyyer</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Matt</forename><surname>Gardner</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Christopher</forename><surname>Clark</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kenton</forename><surname>Lee</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Luke</forename><surname>Zettlemoyer</surname></persName>
		</author>
		<idno type="DOI">10.18653/v1/N18-1202</idno>
		<ptr target="https://aclanthology.org/N18-1202" />
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies</title>
		<title level="s">Long Papers</title>
		<meeting>the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies<address><addrLine>New Orleans, Louisiana</addrLine></address></meeting>
		<imprint>
			<publisher>Association for Computational Linguistics</publisher>
			<date type="published" when="2018-06">June 2018</date>
			<biblScope unit="volume">1</biblScope>
			<biblScope unit="page" from="2227" to="2237" />
		</imprint>
	</monogr>
	<note type="raw_reference">Matthew E. Peters, Mark Neumann, Mohit Iyyer, Matt Gardner, Christopher Clark, Kenton Lee, and Luke Zettlemoyer. Deep contextualized word representations. In Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long Papers), pp. 2227-2237, New Orleans, Louisiana, June 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1202. URL https: //aclanthology.org/N18-1202.</note>
</biblStruct>

<biblStruct xml:id="b30">
	<analytic>
		<title level="a" type="main">Zero: Memory optimizations toward training trillion parameter models</title>
		<author>
			<persName><forename type="first">Samyam</forename><surname>Rajbhandari</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeff</forename><surname>Rasley</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Olatunji</forename><surname>Ruwase</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yuxiong</forename><surname>He</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">SC20: International Conference for High Performance Computing, Networking, Storage and Analysis</title>
		<imprint>
			<publisher>IEEE</publisher>
			<date type="published" when="2020">2020</date>
			<biblScope unit="page" from="1" to="16" />
		</imprint>
	</monogr>
	<note type="raw_reference">Samyam Rajbhandari, Jeff Rasley, Olatunji Ruwase, and Yuxiong He. Zero: Memory optimizations toward training trillion parameter models. In SC20: International Conference for High Performance Computing, Networking, Storage and Analysis, pp. 1-16. IEEE, 2020.</note>
</biblStruct>

<biblStruct xml:id="b31">
	<analytic>
		<title level="a" type="main">Deepspeed: System optimizations enable training deep learning models with over 100 billion parameters</title>
		<author>
			<persName><forename type="first">Jeff</forename><surname>Rasley</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Samyam</forename><surname>Rajbhandari</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Olatunji</forename><surname>Ruwase</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yuxiong</forename><surname>He</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery &amp; Data Mining</title>
		<meeting>the 26th ACM SIGKDD International Conference on Knowledge Discovery &amp; Data Mining</meeting>
		<imprint>
			<date type="published" when="2020">2020</date>
			<biblScope unit="page" from="3505" to="3506" />
		</imprint>
	</monogr>
	<note type="raw_reference">Jeff Rasley, Samyam Rajbhandari, Olatunji Ruwase, and Yuxiong He. Deepspeed: System optimiza- tions enable training deep learning models with over 100 billion parameters. In Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery &amp; Data Mining, pp. 3505-3506, 2020.</note>
</biblStruct>

<biblStruct xml:id="b32">
	<analytic>
		<title level="a" type="main">Zero-offload: Democratizing billion-scale model training</title>
		<author>
			<persName><forename type="first">Jie</forename><surname>Ren</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Samyam</forename><surname>Rajbhandari</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Reza</forename><surname>Yazdani Aminabadi</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Olatunji</forename><surname>Ruwase</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Shuangyan</forename><surname>Yang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Minjia</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dong</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yuxiong</forename><surname>He</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">2021 USENIX Annual Technical Conference (USENIX ATC 21)</title>
		<imprint>
			<date type="published" when="2021">2021</date>
			<biblScope unit="page" from="551" to="564" />
		</imprint>
	</monogr>
	<note type="raw_reference">Jie Ren, Samyam Rajbhandari, Reza Yazdani Aminabadi, Olatunji Ruwase, Shuangyan Yang, Minjia Zhang, Dong Li, and Yuxiong He. Zero-offload: Democratizing billion-scale model training. In 2021 USENIX Annual Technical Conference (USENIX ATC 21), pp. 551-564, 2021.</note>
</biblStruct>

<biblStruct xml:id="b33">
	<monogr>
		<author>
			<persName><forename type="first">William</forename><surname>Saunders</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Catherine</forename><surname>Yeh</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeff</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Steven</forename><surname>Bills</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Long</forename><surname>Ouyang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jonathan</forename><surname>Ward</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jan</forename><surname>Leike</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2206.05802</idno>
		<title level="m">Self-critiquing models for assisting human evaluators</title>
		<imprint>
			<date type="published" when="2022">2022</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">William Saunders, Catherine Yeh, Jeff Wu, Steven Bills, Long Ouyang, Jonathan Ward, and Jan Leike. Self-critiquing models for assisting human evaluators. arXiv preprint arXiv:2206.05802, 2022.</note>
</biblStruct>

<biblStruct xml:id="b34">
	<monogr>
		<title/>
		<author>
			<persName><forename type="first">Nisan</forename><surname>Stiennon</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Long</forename><surname>Ouyang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeffrey</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Daniel</forename><surname>Ziegler</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ryan</forename><surname>Lowe</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chelsea</forename><surname>Voss</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Alec</forename><surname>Radford</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dario</forename><surname>Amodei</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Paul</forename><forename type="middle">F</forename><surname>Christiano</surname></persName>
		</author>
		<imprint/>
	</monogr>
	<note>Learning to summarize with human feedback</note>
	<note type="raw_reference">Nisan Stiennon, Long Ouyang, Jeffrey Wu, Daniel Ziegler, Ryan Lowe, Chelsea Voss, Alec Radford, Dario Amodei, and Paul F Christiano. Learning to summarize with human feed- back.</note>
</biblStruct>

<biblStruct xml:id="b35">
	<monogr>
		<author>
			<persName><forename type="first">In</forename><forename type="middle">H</forename><surname>Larochelle</surname></persName>
		</author>
		<author>
			<persName><forename type="first">M</forename><surname>Ranzato</surname></persName>
		</author>
		<author>
			<persName><forename type="first">R</forename><surname>Hadsell</surname></persName>
		</author>
		<author>
			<persName><forename type="first">M</forename><forename type="middle">F</forename></persName>
		</author>
		<ptr target="https://proceedings.neurips.cc/paper_files/paper/2020/file/1" />
		<title level="m">Advances in Neural Information Processing Systems</title>
		<editor>
			<persName><forename type="first">H</forename><surname>Balcan</surname></persName>
		</editor>
		<editor>
			<persName><surname>Lin</surname></persName>
		</editor>
		<imprint>
			<publisher>Curran Associates, Inc</publisher>
			<date type="published" when="2020">2020</date>
			<biblScope unit="volume">33</biblScope>
			<biblScope unit="page" from="3008" to="3021" />
		</imprint>
	</monogr>
	<note>f89885d556929e98d3ef9b86448f951-Paper.pdf</note>
	<note type="raw_reference">In H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin (eds.), Ad- vances in Neural Information Processing Systems, volume 33, pp. 3008-3021. Curran Asso- ciates, Inc., 2020. URL https://proceedings.neurips.cc/paper_files/paper/2020/file/ 1f89885d556929e98d3ef9b86448f951-Paper.pdf.</note>
</biblStruct>

<biblStruct xml:id="b36">
	<monogr>
		<author>
			<persName><forename type="first">Rohan</forename><surname>Taori</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ishaan</forename><surname>Gulrajani</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tianyi</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yann</forename><surname>Dubois</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xuechen</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Carlos</forename><surname>Guestrin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Percy</forename><surname>Liang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tatsunori</forename><forename type="middle">B</forename><surname>Hashimoto</surname></persName>
		</author>
		<ptr target="https://github.com/tatsu-lab/stanford_alpaca" />
		<title level="m">Stanford alpaca: An instruction-following llama model</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Rohan Taori, Ishaan Gulrajani, Tianyi Zhang, Yann Dubois, Xuechen Li, Carlos Guestrin, Percy Liang, and Tatsunori B. Hashimoto. Stanford alpaca: An instruction-following llama model. https://github.com/tatsu-lab/stanford_alpaca, 2023.</note>
</biblStruct>

<biblStruct xml:id="b37">
	<monogr>
		<author>
			<persName><forename type="first">Hugo</forename><surname>Touvron</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Thibaut</forename><surname>Lavril</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Gautier</forename><surname>Izacard</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xavier</forename><surname>Martinet</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Marie-Anne</forename><surname>Lachaux</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Timothée</forename><surname>Lacroix</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Baptiste</forename><surname>Rozière</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Naman</forename><surname>Goyal</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Eric</forename><surname>Hambro</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Faisal</forename><surname>Azhar</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2302.13971</idno>
		<title level="m">Open and efficient foundation language models</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal Azhar, et al. Llama: Open and efficient foundation language models. arXiv preprint arXiv:2302.13971, 2023a.</note>
</biblStruct>

<biblStruct xml:id="b38">
	<monogr>
		<title level="m" type="main">Llama 2: Open foundation and fine-tuned chat models</title>
		<author>
			<persName><forename type="first">Hugo</forename><surname>Touvron</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Louis</forename><surname>Martin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kevin</forename><surname>Stone</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Peter</forename><surname>Albert</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Amjad</forename><surname>Almahairi</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yasmine</forename><surname>Babaei</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Nikolay</forename><surname>Bashlykov</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Soumya</forename><surname>Batra</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Prajjwal</forename><surname>Bhargava</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Shruti</forename><surname>Bhosale</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2307.09288</idno>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Hugo Touvron, Louis Martin, Kevin Stone, Peter Albert, Amjad Almahairi, Yasmine Babaei, Nikolay Bashlykov, Soumya Batra, Prajjwal Bhargava, Shruti Bhosale, et al. Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288, 2023b.</note>
</biblStruct>

<biblStruct xml:id="b39">
	<monogr>
		<author>
			<persName><forename type="first">Peiyi</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Lei</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Liang</forename><surname>Chen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dawei</forename><surname>Zhu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Binghuai</forename><surname>Lin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yunbo</forename><surname>Cao</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Qi</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tianyu</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhifang</forename><surname>Sui</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2305.17926</idno>
		<title level="m">Large language models are not fair evaluators</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Peiyi Wang, Lei Li, Liang Chen, Dawei Zhu, Binghuai Lin, Yunbo Cao, Qi Liu, Tianyu Liu, and Zhifang Sui. Large language models are not fair evaluators. arXiv preprint arXiv:2305.17926, 2023a.</note>
</biblStruct>

<biblStruct xml:id="b40">
	<monogr>
		<author>
			<persName><forename type="first">Tianlu</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ping</forename><surname>Yu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ellen</forename><surname>Xiaoqing</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sean O'</forename><surname>Tan</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ramakanth</forename><surname>Brien</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jane</forename><surname>Pasunuru</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Olga</forename><surname>Dwivedi-Yu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Luke</forename><surname>Golovneva</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Maryam</forename><surname>Zettlemoyer</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Asli</forename><surname>Fazel-Zarandi</surname></persName>
		</author>
		<author>
			<persName><surname>Celikyilmaz</surname></persName>
		</author>
		<author>
			<persName><surname>Shepherd</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2308.04592</idno>
		<title level="m">A critic for language model generation</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Tianlu Wang, Ping Yu, Xiaoqing Ellen Tan, Sean O&apos;Brien, Ramakanth Pasunuru, Jane Dwivedi-Yu, Olga Golovneva, Luke Zettlemoyer, Maryam Fazel-Zarandi, and Asli Celikyilmaz. Shepherd: A critic for language model generation. arXiv preprint arXiv:2308.04592, 2023b.</note>
</biblStruct>

<biblStruct xml:id="b41">
	<monogr>
		<title level="m" type="main">Pandalm: An automatic evaluation benchmark for llm instruction tuning optimization</title>
		<author>
			<persName><forename type="first">Yidong</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhuohao</forename><surname>Yu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhengran</forename><surname>Zeng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Linyi</forename><surname>Yang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Cunxiang</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Hao</forename><surname>Chen</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chaoya</forename><surname>Jiang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Rui</forename><surname>Xie</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jindong</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xing</forename><surname>Xie</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2306.05087</idno>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Yidong Wang, Zhuohao Yu, Zhengran Zeng, Linyi Yang, Cunxiang Wang, Hao Chen, Chaoya Jiang, Rui Xie, Jindong Wang, Xing Xie, et al. Pandalm: An automatic evaluation benchmark for llm instruction tuning optimization. arXiv preprint arXiv:2306.05087, 2023c.</note>
</biblStruct>

<biblStruct xml:id="b42">
	<monogr>
		<title level="m" type="main">Self-instruct: Aligning language model with self generated instructions</title>
		<author>
			<persName><forename type="first">Yizhong</forename><surname>Wang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yeganeh</forename><surname>Kordi</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Swaroop</forename><surname>Mishra</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Alisa</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Noah</forename><forename type="middle">A</forename><surname>Smith</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Daniel</forename><surname>Khashabi</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Hannaneh</forename><surname>Hajishirzi</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2212.10560</idno>
		<imprint>
			<date type="published" when="2022">2022</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Yizhong Wang, Yeganeh Kordi, Swaroop Mishra, Alisa Liu, Noah A Smith, Daniel Khashabi, and Hannaneh Hajishirzi. Self-instruct: Aligning language model with self generated instructions. arXiv preprint arXiv:2212.10560, 2022.</note>
</biblStruct>

<biblStruct xml:id="b43">
	<monogr>
		<author>
			<persName><forename type="first">Can</forename><surname>Xu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Qingfeng</forename><surname>Sun</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kai</forename><surname>Zheng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xiubo</forename><surname>Geng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pu</forename><surname>Zhao</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jiazhan</forename><surname>Feng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Chongyang</forename><surname>Tao</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Daxin</forename><surname>Jiang</surname></persName>
		</author>
		<author>
			<persName><surname>Wizardlm</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2304.12244</idno>
		<title level="m">Empowering large language models to follow complex instructions</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Can Xu, Qingfeng Sun, Kai Zheng, Xiubo Geng, Pu Zhao, Jiazhan Feng, Chongyang Tao, and Daxin Jiang. Wizardlm: Empowering large language models to follow complex instructions. arXiv preprint arXiv:2304.12244, 2023.</note>
</biblStruct>

<biblStruct xml:id="b44">
	<monogr>
		<title level="m" type="main">Selfee: Iterative self-revising llm empowered by self-feedback generation. Blog post</title>
		<author>
			<persName><forename type="first">Seonghyeon</forename><surname>Ye</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yongrae</forename><surname>Jo</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Doyoung</forename><surname>Kim</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Sungdong</forename><surname>Kim</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Hyeonbin</forename><surname>Hwang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Minjoon</forename><surname>Seo</surname></persName>
		</author>
		<ptr target="https://kaistai.github.io/SelFee/" />
		<imprint>
			<date type="published" when="2023-05">May 2023</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Seonghyeon Ye, Yongrae Jo, Doyoung Kim, Sungdong Kim, Hyeonbin Hwang, and Minjoon Seo. Selfee: Iterative self-revising llm empowered by self-feedback generation. Blog post, May 2023. URL https://kaistai.github.io/SelFee/.</note>
</biblStruct>

<biblStruct xml:id="b45">
	<analytic>
		<title level="a" type="main">Bartscore: Evaluating generated text as text generation</title>
		<author>
			<persName><forename type="first">Weizhe</forename><surname>Yuan</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Graham</forename><surname>Neubig</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pengfei</forename><surname>Liu</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="j">Advances in Neural Information Processing Systems</title>
		<imprint>
			<biblScope unit="volume">34</biblScope>
			<biblScope unit="page" from="27263" to="27277" />
			<date type="published" when="2021">2021</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Weizhe Yuan, Graham Neubig, and Pengfei Liu. Bartscore: Evaluating generated text as text generation. Advances in Neural Information Processing Systems, 34:27263-27277, 2021.</note>
</biblStruct>

<biblStruct xml:id="b46">
	<analytic>
		<title level="a" type="main">Bertscore: Evaluating text generation with bert</title>
		<author>
			<persName><forename type="first">Tianyi</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Varsha</forename><surname>Kishore</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Felix</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Kilian</forename><forename type="middle">Q</forename><surname>Weinberger</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yoav</forename><surname>Artzi</surname></persName>
		</author>
	</analytic>
	<monogr>
		<title level="m">International Conference on Learning Representations</title>
		<imprint>
			<date type="published" when="2019">2019</date>
		</imprint>
	</monogr>
	<note type="raw_reference">Tianyi Zhang, Varsha Kishore, Felix Wu, Kilian Q Weinberger, and Yoav Artzi. Bertscore: Evaluating text generation with bert. In International Conference on Learning Representations, 2019.</note>
</biblStruct>

<biblStruct xml:id="b47">
	<monogr>
		<author>
			<persName><forename type="first">Xinghua</forename><surname>Zhang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Bowen</forename><surname>Yu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Haiyang</forename><surname>Yu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yangyu</forename><surname>Lv</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tingwen</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Fei</forename><surname>Huang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Hongbo</forename><surname>Xu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yongbin</forename><surname>Li</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2308.01862</idno>
		<title level="m">Wider and deeper llm networks are fairer llm evaluators</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Xinghua Zhang, Bowen Yu, Haiyang Yu, Yangyu Lv, Tingwen Liu, Fei Huang, Hongbo Xu, and Yong- bin Li. Wider and deeper llm networks are fairer llm evaluators. arXiv preprint arXiv:2308.01862, 2023.</note>
</biblStruct>

<biblStruct xml:id="b48">
	<monogr>
		<title level="m" type="main">Judging llm-as-a-judge with mt-bench and chatbot arena</title>
		<author>
			<persName><forename type="first">Lianmin</forename><surname>Zheng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Wei-Lin</forename><surname>Chiang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ying</forename><surname>Sheng</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Siyuan</forename><surname>Zhuang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhanghao</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yonghao</forename><surname>Zhuang</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zi</forename><surname>Lin</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Zhuohan</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dacheng</forename><surname>Li</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Eric</forename><surname>Xing</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2306.05685</idno>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Lianmin Zheng, Wei-Lin Chiang, Ying Sheng, Siyuan Zhuang, Zhanghao Wu, Yonghao Zhuang, Zi Lin, Zhuohan Li, Dacheng Li, Eric Xing, et al. Judging llm-as-a-judge with mt-bench and chatbot arena. arXiv preprint arXiv:2306.05685, 2023.</note>
</biblStruct>

<biblStruct xml:id="b49">
	<monogr>
		<author>
			<persName><forename type="first">Chunting</forename><surname>Zhou</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Pengfei</forename><surname>Liu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Puxin</forename><surname>Xu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Srini</forename><surname>Iyer</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jiao</forename><surname>Sun</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Yuning</forename><surname>Mao</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Xuezhe</forename><surname>Ma</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Avia</forename><surname>Efrat</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Ping</forename><surname>Yu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Lili</forename><surname>Yu</surname></persName>
		</author>
		<idno type="arXiv">arXiv:2305.11206</idno>
		<title level="m">Less is more for alignment</title>
		<imprint>
			<date type="published" when="2023">2023</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Chunting Zhou, Pengfei Liu, Puxin Xu, Srini Iyer, Jiao Sun, Yuning Mao, Xuezhe Ma, Avia Efrat, Ping Yu, Lili Yu, et al. Lima: Less is more for alignment. arXiv preprint arXiv:2305.11206, 2023.</note>
</biblStruct>

<biblStruct xml:id="b50">
	<monogr>
		<author>
			<persName><forename type="first">Nisan</forename><surname>Daniel M Ziegler</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Jeffrey</forename><surname>Stiennon</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Tom</forename><forename type="middle">B</forename><surname>Wu</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Alec</forename><surname>Brown</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Dario</forename><surname>Radford</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Paul</forename><surname>Amodei</surname></persName>
		</author>
		<author>
			<persName><forename type="first">Geoffrey</forename><surname>Christiano</surname></persName>
		</author>
		<author>
			<persName><surname>Irving</surname></persName>
		</author>
		<idno type="arXiv">arXiv:1909.08593</idno>
		<title level="m">Fine-tuning language models from human preferences</title>
		<imprint>
			<date type="published" when="2019">2019</date>
		</imprint>
	</monogr>
	<note type="report_type">arXiv preprint</note>
	<note type="raw_reference">Daniel M Ziegler, Nisan Stiennon, Jeffrey Wu, Tom B Brown, Alec Radford, Dario Amodei, Paul Christiano, and Geoffrey Irving. Fine-tuning language models from human preferences. arXiv preprint arXiv:1909.08593, 2019.</note>
</biblStruct>

				</listBibl>
			</div>
		</back>
	</text>
</TEI>
