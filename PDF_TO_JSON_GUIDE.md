# PDF转JSON自动化指南

## 🎯 新功能说明

现在Paper2Code流式API支持**自动PDF转JSON转换**！你只需要提供PDF文件，API会自动处理转换过程。

## 🚀 快速开始

### 1. 准备工作

首先确保你有s2orc-doc2json工具：

```bash
# 克隆s2orc-doc2json仓库
git clone https://github.com/allenai/s2orc-doc2json.git

# 启动Grobid服务（在新的终端窗口中运行）
cd s2orc-doc2json/grobid-0.7.3
./gradlew run
```

**重要：** 保持Grobid服务运行，它是PDF转换的核心服务。

### 2. 使用方式

现在你可以直接使用PDF文件：

```bash
# 将PDF文件放入data目录
cp your_paper.pdf data/

# 启动API服务
python api_server.py

# 使用Web界面或API调用处理
```

## 📋 处理流程

当你提供一个PDF文件时，API会自动执行：

1. **检查JSON文件** - 查看是否已存在对应的JSON文件
2. **自动转换** - 如果JSON不存在，使用s2orc-doc2json自动转换
3. **继续处理** - 转换完成后继续执行正常的Paper2Code流程

## 🔍 实时监控

通过Web界面，你可以实时看到：

- ✅ **PDF转JSON阶段** - 新增的处理阶段
- 📊 **转换进度** - 实时输出转换过程
- 🎯 **状态更新** - 每个步骤的详细状态

## 📁 文件结构

### 输入文件
```
data/
├── your_paper.pdf          # 你的PDF文件
└── (your_paper.json)       # 如果不存在，会自动生成
```

### 自动生成的文件
```
s2orc-doc2json/
├── temp_dir/               # 临时处理目录
└── output_dir/
    └── paper_coder/
        └── your_paper.json # 转换生成的JSON文件
```

API会自动将生成的JSON文件复制到data目录。

## ⚠️ 注意事项

### 1. Grobid服务必须运行
```bash
# 检查Grobid是否运行（应该看到8070端口）
curl http://localhost:8070/api/isalive
```

### 2. 网络连接
PDF转换过程可能需要下载模型文件，确保网络连接正常。

### 3. 处理时间
PDF转JSON可能需要几分钟时间，特别是：
- 大型PDF文件
- 复杂的数学公式
- 图表较多的论文

### 4. 磁盘空间
确保有足够的磁盘空间：
- 临时文件可能比原PDF大几倍
- 最终JSON文件通常比PDF小

## 🛠️ 故障排除

### 问题1: "s2orc-doc2json目录不存在"
```bash
# 解决方案：克隆仓库
git clone https://github.com/allenai/s2orc-doc2json.git
```

### 问题2: "PDF转JSON失败"
```bash
# 检查Grobid服务
curl http://localhost:8070/api/isalive

# 重启Grobid服务
cd s2orc-doc2json/grobid-0.7.3
./gradlew run
```

### 问题3: "未找到生成的JSON文件"
- 检查PDF文件是否损坏
- 确认PDF是学术论文格式
- 查看API日志中的详细错误信息

### 问题4: 转换速度慢
- 这是正常现象，PDF转换是计算密集型任务
- 可以通过Web界面查看实时进度
- 避免同时处理多个大型PDF

## 💡 最佳实践

### 1. 文件命名
- 使用英文文件名
- 避免特殊字符和空格
- 推荐格式：`paper_name.pdf`

### 2. PDF质量
- 使用文本型PDF（非扫描版）
- 确保PDF结构完整
- 学术论文格式效果最佳

### 3. 批量处理
- 一次处理一个PDF文件
- 大型PDF建议单独处理
- 可以使用API的文件列表功能管理多个文件

## 🔄 与手动转换的对比

### 手动方式（旧）
```bash
# 需要手动执行多个步骤
python ./s2orc-doc2json/doc2json/grobid2json/process_pdf.py \
    -i data/paper.pdf \
    -t ./s2orc-doc2json/temp_dir/ \
    -o data/
```

### 自动方式（新）
```bash
# 只需要启动API，其他全自动
python api_server.py
# 然后通过Web界面或API调用即可
```

## 🎉 总结

现在你可以：

1. **直接使用PDF文件** - 无需手动转换
2. **实时监控转换** - 通过Web界面查看进度
3. **一键处理** - 从PDF到代码的完整流程
4. **错误诊断** - 详细的错误信息和解决建议

这大大简化了Paper2Code的使用流程，让你专注于论文内容而不是技术细节！
