#!/usr/bin/env python3
"""
Paper2Code API 测试脚本
"""

import requests
import json
import time
from pathlib import Path

# API 基础URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保服务已启动")
        return False
    except Exception as e:
        print(f"❌ 健康检查出错: {e}")
        return False

def test_root_endpoint():
    """测试根路径接口"""
    print("\n🔍 测试根路径接口...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 根路径接口正常: {data['message']}")
            return True
        else:
            print(f"❌ 根路径接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 根路径接口出错: {e}")
        return False

def test_list_papers():
    """测试文件列表接口"""
    print("\n🔍 测试文件列表接口...")
    try:
        response = requests.get(f"{BASE_URL}/list_papers")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文件列表获取成功:")
            print(f"   Data目录PDF文件: {data['data_pdf_files']}")
            print(f"   Data目录JSON文件: {data['data_json_files']}")
            print(f"   Examples目录PDF文件: {data['examples_pdf_files']}")
            print(f"   Examples目录JSON文件: {data['examples_json_files']}")
            return data
        else:
            print(f"❌ 文件列表获取失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 文件列表接口出错: {e}")
        return None

def test_copy_example(filename):
    """测试复制示例文件接口"""
    print(f"\n🔍 测试复制示例文件接口 (文件: {filename})...")
    try:
        response = requests.post(f"{BASE_URL}/copy_example_to_data", params={"filename": filename})
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 文件复制成功: {data['message']}")
                print(f"   复制的文件: {data['copied_files']}")
                return True
            else:
                print(f"❌ 文件复制失败: {data['message']}")
                return False
        else:
            print(f"❌ 复制请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 复制文件接口出错: {e}")
        return False

def test_process_paper(filename="2310.05470v2.pdf", paper_name="test_paper"):
    """测试论文处理接口"""
    print(f"\n🔍 测试论文处理接口 (文件: {filename})...")
    
    # 检查文件是否存在
    pdf_path = Path("data") / filename
    json_path = Path("data") / filename.replace('.pdf', '.json')
    
    if not pdf_path.exists():
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    if not json_path.exists():
        print(f"❌ JSON文件不存在: {json_path}")
        print("   请先使用s2orc-doc2json转换PDF文件")
        return False
    
    try:
        # 发送处理请求
        request_data = {
            "filename": filename,
            "paper_name": paper_name,
            "gpt_version": "openai/o3-mini"
        }
        
        print(f"📤 发送请求: {request_data}")
        response = requests.post(
            f"{BASE_URL}/process_paper",
            json=request_data,
            timeout=3600  # 1小时超时
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("✅ 论文处理成功!")
                print(f"   输出目录: {data['output_dir']}")
                print(f"   代码仓库: {data['output_repo_dir']}")
                return True
            else:
                print(f"❌ 论文处理失败: {data['message']}")
                if data.get('error'):
                    print(f"   错误详情: {data['error']}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，论文处理可能需要更长时间")
        return False
    except Exception as e:
        print(f"❌ 论文处理出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Paper2Code API 测试开始")
    print("=" * 50)
    
    # 测试健康检查
    if not test_health_check():
        print("\n❌ API服务不可用，请检查服务是否已启动")
        return
    
    # 测试根路径
    test_root_endpoint()
    
    # 测试文件列表
    file_data = test_list_papers()
    
    # 测试文件处理功能
    if file_data:
        # 检查是否有可用的文件
        available_files = []

        # 优先使用data目录的文件
        if file_data['data_pdf_files']:
            available_files.extend(file_data['data_pdf_files'])
            print(f"\n📋 发现data目录中的PDF文件: {file_data['data_pdf_files']}")

        # 如果data目录没有文件，检查examples目录
        if not available_files and file_data['examples_pdf_files']:
            print(f"\n📋 发现examples目录中的PDF文件: {file_data['examples_pdf_files']}")

            # 选择第一个examples文件进行复制测试
            example_file = file_data['examples_pdf_files'][0]
            print(f"\n🔄 尝试复制示例文件: {example_file}")

            if test_copy_example(example_file):
                available_files.append(Path(example_file).name)

        # 如果有可用文件，进行处理测试
        if available_files:
            test_filename = available_files[0]
            print(f"\n🎯 选择文件进行测试: {test_filename}")

            # 询问用户是否要进行实际处理测试
            user_input = input("\n是否要进行实际的论文处理测试？这可能需要较长时间 (y/N): ")
            if user_input.lower() in ['y', 'yes']:
                test_process_paper(test_filename, "api_test")
            else:
                print("⏭️  跳过论文处理测试")
        else:
            print("\n📋 未发现可用的PDF文件，跳过处理测试")
            print("   请将PDF文件放入data目录并转换为JSON格式")
    else:
        print("\n📋 无法获取文件列表，跳过处理测试")
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
